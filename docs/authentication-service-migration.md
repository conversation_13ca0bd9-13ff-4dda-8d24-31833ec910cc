# Authentication Service Consolidation Migration Guide

## Overview

This document outlines the migration from three inconsistent authentication services to a single unified service.

## Services Being Replaced

### 1. SessionSecurity (`session-security.ts`)
- **Purpose**: Session validation with security features
- **Return Type**: `{ isValid, userId?, sessionData?, error?, statusCode? }`
- **Key Features**: Anti-enumeration, security logging, session type validation

### 2. UnifiedSessionManagement (`unified-session-management.ts`)
- **Purpose**: Interview session lifecycle management
- **Return Type**: `{ isValid, session?, user?, error?, statusCode? }`
- **Key Features**: Session creation, updates, lifecycle management

### 3. UserValidationService (`user-validation-service.ts`)
- **Purpose**: User validation and session repair
- **Return Type**: `{ isValid, userId?, error?, statusCode?, user? }`
- **Key Features**: User existence validation, relationship management

## New Unified Service

### UnifiedAuthenticationService (`unified-authentication-service.ts`)

**Unified Return Type:**
```typescript
interface UnifiedSessionValidationResult {
  isValid: boolean;
  userId?: string;
  user?: UserData;
  session?: SessionData;
  sessionData?: any;
  error?: string;
  statusCode?: number;
  securityFlags?: string[];
}
```

**Key Methods:**
- `validateSession()` - Primary validation method (replaces all validation methods)
- `validateSessionAccess()` - Session-specific validation with security features
- `createSession()` - Session creation with validation
- `updateSession()` - Session updates with state transition validation
- `getUserSessions()` - Session retrieval with filtering

## Migration Steps

### Step 1: Update API Routes

**Before (UserValidationService):**
```typescript
import { UserValidationService } from '@/lib/user-validation-service';

const validation = await UserValidationService.validateUserSession(request, {
  validateUserExists: true,
  checkAccountLock: true
});

if (!validation.isValid) {
  // Handle error
}

const userId = validation.userId!;
```

**After (UnifiedAuthenticationService):**
```typescript
import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';

const validation = await UnifiedAuthenticationService.validateSession(request, {
  validateUserExists: true,
  checkAccountLock: true
});

if (!validation.isValid) {
  // Handle error - same logic
}

const userId = validation.userId!;
```

### Step 2: Update Session Access Validation

**Before (SessionSecurity):**
```typescript
import { SessionSecurity } from '@/lib/session-security';

const validation = await SessionSecurity.validateSessionAccess(
  request,
  sessionId,
  'interview'
);
```

**After (UnifiedAuthenticationService):**
```typescript
import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';

const validation = await UnifiedAuthenticationService.validateSessionAccess(
  request,
  sessionId,
  { sessionType: 'interview' }
);
```

### Step 3: Update Session Management

**Before (UnifiedSessionManagement):**
```typescript
import { UnifiedSessionManagement } from '@/lib/unified-session-management';

const result = await UnifiedSessionManagement.createSession(userId, sessionData);
```

**After (UnifiedAuthenticationService):**
```typescript
import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';

const result = await UnifiedAuthenticationService.createSession(userId, sessionData);
```

## Migration Checklist

### Phase 1: Core API Routes
- [ ] `/api/interview-practice/route.ts` - Update UserValidationService usage
- [ ] `/api/auth/validate-session/route.ts` - Update session validation
- [ ] `/api/sessions/[sessionId]/route.ts` - Update SessionSecurity usage

### Phase 2: Session Management Routes
- [ ] Session creation endpoints
- [ ] Session update endpoints
- [ ] Session retrieval endpoints

### Phase 3: Security and Validation
- [ ] Update security logging integration
- [ ] Update CSRF protection integration
- [ ] Update rate limiting integration

### Phase 4: Testing and Verification
- [ ] Update unit tests to use new service
- [ ] Update integration tests
- [ ] Verify all authentication flows work correctly
- [ ] Performance testing

### Phase 5: Cleanup
- [ ] Remove old service files
- [ ] Update imports across codebase
- [ ] Update documentation

## Benefits of Consolidation

1. **Consistent Interface**: Single return type and method signatures
2. **Unified Security**: All security features in one place
3. **Better Maintainability**: Single service to maintain and update
4. **Reduced Complexity**: No more confusion about which service to use
5. **Improved Testing**: Single service to test comprehensively

## Breaking Changes

1. **Return Type Changes**: Some consumers may need to update property access
2. **Method Signature Changes**: Options parameter structure has changed
3. **Error Handling**: Security flags added to error responses

## Rollback Plan

If issues arise during migration:
1. Keep old services in place during migration
2. Use feature flags to switch between old and new services
3. Gradual rollout per API endpoint
4. Monitor error rates and performance metrics

## Testing Strategy

1. **Unit Tests**: Test all methods of new unified service
2. **Integration Tests**: Test with real database and session data
3. **Security Tests**: Verify all security features work correctly
4. **Performance Tests**: Ensure no performance regression
5. **End-to-End Tests**: Test complete authentication flows

## Timeline

- **Week 1**: Create unified service and migration documentation
- **Week 2**: Migrate core API routes and test
- **Week 3**: Migrate session management and security features
- **Week 4**: Complete testing and cleanup old services

## Support

For questions or issues during migration, refer to:
- Architecture team lead
- Security team for security-related changes
- QA team for testing strategy
