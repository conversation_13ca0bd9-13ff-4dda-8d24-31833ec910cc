# API Documentation

This document provides comprehensive documentation for all API endpoints in the FAAFO Career Platform.

## 🔗 Base URL

```
Development: http://localhost:3000/api
Production: https://your-domain.com/api
```

## 🔐 Authentication

Most API endpoints require authentication. The platform uses NextAuth.js for session management.

### **Authentication Headers**
```http
Cookie: next-auth.session-token=your-session-token
```

### **CSRF Protection**
State-changing operations require CSRF tokens:
```http
X-CSRF-Token: your-csrf-token
```

Get CSRF token from: `GET /api/csrf-token`

## 📋 API Endpoints

### **🔐 Authentication**

#### `POST /api/signup`
Create a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "<PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Account created successfully",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "<PERSON>"
  }
}
```

#### `POST /api/auth/forgot-password`
Request password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### `POST /api/auth/reset-password`
Reset password with token.

**Request Body:**
```json
{
  "token": "reset-token",
  "password": "newPassword123"
}
```

### **📊 Assessment**

#### `GET /api/assessment`
Get user's assessment data.

**Response:**
```json
{
  "id": "assessment-id",
  "userId": "user-id",
  "responses": {},
  "completedAt": "2024-01-01T00:00:00Z",
  "score": 85
}
```

#### `POST /api/assessment`
Submit assessment responses.

**Request Body:**
```json
{
  "responses": {
    "question1": "answer1",
    "question2": "answer2"
  }
}
```

#### `GET /api/assessment/[id]/enhanced-results`
Get enhanced assessment results with AI insights.

**Response:**
```json
{
  "assessment": {},
  "aiInsights": {
    "strengths": ["Leadership", "Communication"],
    "recommendations": ["Develop technical skills"],
    "careerPaths": ["Product Manager", "Team Lead"]
  }
}
```

### **🤖 AI Services**

#### `POST /api/ai/career-recommendations`
Get AI-powered career recommendations.

**Request Body:**
```json
{
  "skills": ["JavaScript", "React", "Node.js"],
  "experience": "mid-level",
  "interests": ["web development", "user experience"]
}
```

**Response:**
```json
{
  "recommendations": [
    {
      "title": "Frontend Developer",
      "match": 95,
      "description": "Perfect match for your skills",
      "requirements": ["React", "TypeScript"],
      "salaryRange": "$70k-$120k"
    }
  ]
}
```

#### `POST /api/ai/resume-analysis`
Analyze resume with AI.

**Request Body:**
```json
{
  "resumeText": "Resume content here...",
  "targetRole": "Software Engineer"
}
```

#### `POST /api/ai/skills-analysis`
Comprehensive skills analysis.

**Request Body:**
```json
{
  "currentSkills": ["React", "Node.js"],
  "targetRole": "Full Stack Developer",
  "experience": "2 years"
}
```

#### `GET /api/ai/health`
Check AI services health status.

**Response:**
```json
{
  "status": "healthy",
  "services": {
    "gemini": "operational",
    "cache": "operational"
  },
  "responseTime": 150
}
```

### **🎯 Interview Practice**

#### `POST /api/interview-practice`
Create new interview practice session.

**Request Body:**
```json
{
  "role": "Software Engineer",
  "difficulty": "intermediate",
  "duration": 30
}
```

#### `GET /api/interview-practice/[sessionId]`
Get interview session details.

#### `POST /api/interview-practice/[sessionId]/responses`
Submit interview response.

**Request Body:**
```json
{
  "questionId": "q1",
  "response": "My answer to the question...",
  "duration": 120
}
```

### **👤 Profile Management**

#### `GET /api/profile`
Get user profile.

**Response:**
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "name": "John Doe",
  "bio": "Software developer...",
  "skills": ["React", "Node.js"],
  "experience": "mid-level"
}
```

#### `PUT /api/profile`
Update user profile.

**Request Body:**
```json
{
  "name": "John Doe",
  "bio": "Updated bio...",
  "skills": ["React", "Node.js", "TypeScript"]
}
```

#### `POST /api/profile/photo`
Upload profile photo.

**Request:** Multipart form data with image file.

### **🎯 Goals & Progress**

#### `GET /api/goals`
Get user's goals.

#### `POST /api/goals`
Create new goal.

**Request Body:**
```json
{
  "title": "Learn TypeScript",
  "description": "Master TypeScript fundamentals",
  "targetDate": "2024-06-01",
  "category": "skill-development"
}
```

#### `GET /api/progress-tracker`
Get learning progress.

### **📚 Learning Resources**

#### `GET /api/learning-resources`
Get learning resources with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `category`: Filter by category
- `difficulty`: Filter by difficulty

#### `POST /api/learning-resources`
Create new learning resource (admin only).

#### `GET /api/learning-resources/categories`
Get all resource categories.

### **💬 Forum**

#### `GET /api/forum/posts`
Get forum posts with pagination.

#### `POST /api/forum/posts`
Create new forum post.

**Request Body:**
```json
{
  "title": "Question about React",
  "content": "I'm having trouble with...",
  "category": "technical"
}
```

#### `POST /api/forum/posts/[postId]/replies`
Reply to forum post.

#### `POST /api/forum/reactions`
Add reaction to post/reply.

### **📈 Analytics**

#### `GET /api/analytics/dashboard`
Get user analytics dashboard data.

**Response:**
```json
{
  "assessmentProgress": 75,
  "skillsGrowth": 15,
  "learningStreak": 7,
  "completedResources": 12
}
```

### **🏥 Health & Monitoring**

#### `GET /api/health`
System health check.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": {
    "database": "healthy",
    "ai": "healthy",
    "cache": "healthy"
  },
  "version": "1.0.0"
}
```

#### `GET /api/health/database`
Database-specific health check.

## 📝 Response Formats

### **Success Response**
```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully"
}
```

### **Error Response**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

## 🚨 Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Invalid input data | 400 |
| `UNAUTHORIZED` | Authentication required | 401 |
| `FORBIDDEN` | Insufficient permissions | 403 |
| `NOT_FOUND` | Resource not found | 404 |
| `RATE_LIMITED` | Too many requests | 429 |
| `INTERNAL_ERROR` | Server error | 500 |
| `SERVICE_UNAVAILABLE` | External service down | 503 |

## 🔒 Security Features

### **Rate Limiting**
- **Default:** 100 requests per 15 minutes per IP
- **AI Endpoints:** 20 requests per hour per user
- **Upload Endpoints:** 10 requests per hour per user

### **Input Validation**
- All inputs are validated and sanitized
- XSS protection enabled
- SQL injection prevention
- File upload restrictions

### **CORS Policy**
- Configured origins only
- Credentials included for authenticated requests
- Preflight requests handled

## 🧪 Testing

### **API Testing**
```bash
# Run API tests
npm run test:api

# Test specific endpoint
npm run test:api -- --grep "assessment"
```

### **Example Requests**
```bash
# Get CSRF token
curl -X GET http://localhost:3000/api/csrf-token

# Create assessment (with auth)
curl -X POST http://localhost:3000/api/assessment \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: your-token" \
  -d '{"responses": {"q1": "answer1"}}'
```

## 📚 Related Documentation

- [Environment Variables](./environment-variables.md)
- [Authentication Guide](../development/authentication-guide.md)
- [Security Configuration](./security-configuration.md)
- [Deployment Guide](../operations/deployment-guide.md)
