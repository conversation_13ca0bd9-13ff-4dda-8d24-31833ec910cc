/**
 * Enhanced Accessibility Hook
 * Provides comprehensive accessibility features and utilities
 */

import { useState, useEffect, useCallback, useRef } from 'react';

interface AccessibilityState {
  isReducedMotion: boolean;
  isHighContrast: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  screenReaderActive: boolean;
  keyboardNavigation: boolean;
}

interface AccessibilityActions {
  toggleHighContrast: () => void;
  setFontSize: (size: AccessibilityState['fontSize']) => void;
  announceToScreenReader: (message: string) => void;
  focusElement: (elementId: string) => void;
  skipToContent: () => void;
}

interface UseAccessibilityEnhancedReturn extends AccessibilityState, AccessibilityActions {
  isLoading: boolean;
  error: string | null;
}

export function useAccessibilityEnhanced(): UseAccessibilityEnhancedReturn {
  const [state, setState] = useState<AccessibilityState>({
    isReducedMotion: false,
    isHighContrast: false,
    fontSize: 'medium',
    screenReaderActive: false,
    keyboardNavigation: false,
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const announcementRef = useRef<HTMLDivElement | null>(null);

  // Initialize accessibility settings
  useEffect(() => {
    try {
      setIsLoading(true);
      
      // Check for reduced motion preference
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      
      // Check for high contrast preference
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      
      // Load saved preferences from localStorage
      const savedFontSize = localStorage.getItem('accessibility-font-size') as AccessibilityState['fontSize'] || 'medium';
      const savedHighContrast = localStorage.getItem('accessibility-high-contrast') === 'true';
      
      // Detect screen reader (basic detection)
      const screenReaderActive = navigator.userAgent.includes('NVDA') || 
                                navigator.userAgent.includes('JAWS') || 
                                navigator.userAgent.includes('VoiceOver');

      setState({
        isReducedMotion: prefersReducedMotion,
        isHighContrast: savedHighContrast || prefersHighContrast,
        fontSize: savedFontSize,
        screenReaderActive,
        keyboardNavigation: false,
      });

      // Apply initial settings
      applyAccessibilitySettings({
        isReducedMotion: prefersReducedMotion,
        isHighContrast: savedHighContrast || prefersHighContrast,
        fontSize: savedFontSize,
        screenReaderActive,
        keyboardNavigation: false,
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize accessibility settings');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Apply accessibility settings to the document
  const applyAccessibilitySettings = useCallback((settings: AccessibilityState) => {
    const root = document.documentElement;
    
    // Apply high contrast
    if (settings.isHighContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    // Apply font size
    root.classList.remove('font-small', 'font-medium', 'font-large', 'font-extra-large');
    root.classList.add(`font-${settings.fontSize}`);
    
    // Apply reduced motion
    if (settings.isReducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
  }, []);

  // Toggle high contrast
  const toggleHighContrast = useCallback(() => {
    setState(prev => {
      const newState = { ...prev, isHighContrast: !prev.isHighContrast };
      applyAccessibilitySettings(newState);
      localStorage.setItem('accessibility-high-contrast', String(newState.isHighContrast));
      return newState;
    });
  }, [applyAccessibilitySettings]);

  // Set font size
  const setFontSize = useCallback((size: AccessibilityState['fontSize']) => {
    setState(prev => {
      const newState = { ...prev, fontSize: size };
      applyAccessibilitySettings(newState);
      localStorage.setItem('accessibility-font-size', size);
      return newState;
    });
  }, [applyAccessibilitySettings]);

  // Announce message to screen readers
  const announceToScreenReader = useCallback((message: string) => {
    if (!announcementRef.current) {
      // Create announcement element if it doesn't exist
      const element = document.createElement('div');
      element.setAttribute('aria-live', 'polite');
      element.setAttribute('aria-atomic', 'true');
      element.className = 'sr-only';
      element.style.position = 'absolute';
      element.style.left = '-10000px';
      element.style.width = '1px';
      element.style.height = '1px';
      element.style.overflow = 'hidden';
      document.body.appendChild(element);
      announcementRef.current = element;
    }
    
    announcementRef.current.textContent = message;
    
    // Clear after announcement
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = '';
      }
    }, 1000);
  }, []);

  // Focus specific element
  const focusElement = useCallback((elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.focus();
      announceToScreenReader(`Focused on ${element.getAttribute('aria-label') || element.textContent || elementId}`);
    }
  }, [announceToScreenReader]);

  // Skip to main content
  const skipToContent = useCallback(() => {
    const mainContent = document.getElementById('main-content') || document.querySelector('main');
    if (mainContent) {
      mainContent.focus();
      announceToScreenReader('Skipped to main content');
    }
  }, [announceToScreenReader]);

  // Keyboard navigation detection
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        setState(prev => ({ ...prev, keyboardNavigation: true }));
      }
    };

    const handleMouseDown = () => {
      setState(prev => ({ ...prev, keyboardNavigation: false }));
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);

  return {
    ...state,
    isLoading,
    error,
    toggleHighContrast,
    setFontSize,
    announceToScreenReader,
    focusElement,
    skipToContent,
  };
}

export default useAccessibilityEnhanced;
