/**
 * CSRF Token Hook
 * Provides CSRF token management and secure fetch functionality
 */

import { useState, useEffect, useCallback } from 'react';

interface CSRFTokenHook {
  csrfToken: string | null;
  isLoading: boolean;
  error: string | null;
  csrfFetch: (url: string, options?: RequestInit) => Promise<Response>;
  refreshToken: () => Promise<void>;
}

export function useCSRFToken(): CSRFTokenHook {
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch CSRF token from the server
  const fetchCSRFToken = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/csrf-token', {
        method: 'GET',
        credentials: 'same-origin',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch CSRF token: ${response.status}`);
      }

      const data = await response.json();
      setCsrfToken(data.csrfToken);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch CSRF token';
      setError(errorMessage);
      console.error('CSRF token fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize CSRF token on mount
  useEffect(() => {
    fetchCSRFToken();
  }, [fetchCSRFToken]);

  // Enhanced fetch function that automatically includes CSRF token
  const csrfFetch = useCallback(async (url: string, options: RequestInit = {}) => {
    if (!csrfToken) {
      throw new Error('CSRF token not available');
    }

    const headers = new Headers(options.headers);
    
    // Add CSRF token to headers for non-GET requests
    if (options.method && options.method.toUpperCase() !== 'GET') {
      headers.set('X-CSRF-Token', csrfToken);
    }

    // Ensure credentials are included
    const fetchOptions: RequestInit = {
      ...options,
      headers,
      credentials: 'same-origin',
    };

    return fetch(url, fetchOptions);
  }, [csrfToken]);

  // Refresh token function
  const refreshToken = useCallback(async () => {
    await fetchCSRFToken();
  }, [fetchCSRFToken]);

  return {
    csrfToken,
    isLoading,
    error,
    csrfFetch,
    refreshToken,
  };
}

export default useCSRFToken;
