'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  FileText, 
  Play, 
  RefreshCw, 
  TrendingUp,
  Bug,
  Shield,
  Zap,
  Code,
  Database,
  TestTube
} from 'lucide-react';
import { toast } from 'sonner';
import { AuditMetrics } from '@/components/audit/AuditMetrics';
import { RecentIssues } from '@/components/audit/RecentIssues';
import { AuditRunsList } from '@/components/audit/AuditRunsList';

interface AuditDashboardData {
  overview: {
    totalIssues: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    openCount: number;
    resolvedCount: number;
    lastRunAt: string | null;
    lastRunStatus: string | null;
  };
  recentIssues: Array<{
    id: string;
    title: string;
    severity: string;
    category: string;
    filePath: string;
    createdAt: string;
  }>;
  recentRuns: Array<{
    id: string;
    startedAt: string;
    completedAt: string | null;
    status: string;
    totalIssues: number;
  }>;
}

export function AuditDashboard() {
  const [data, setData] = useState<AuditDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRunningAudit, setIsRunningAudit] = useState(false);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch overview metrics
      const [runsResponse, issuesResponse] = await Promise.all([
        fetch('/api/audit/runs?limit=5'),
        fetch('/api/audit/issues?limit=10')
      ]);

      if (!runsResponse.ok || !issuesResponse.ok) {
        throw new Error('Failed to fetch audit data');
      }

      const runsData = await runsResponse.json();
      const issuesData = await issuesResponse.json();

      // Calculate overview metrics
      const overview = {
        totalIssues: issuesData.data.summary?.totalIssues || 0,
        criticalCount: issuesData.data.summary?.criticalCount || 0,
        highCount: issuesData.data.summary?.highCount || 0,
        mediumCount: issuesData.data.summary?.mediumCount || 0,
        lowCount: issuesData.data.summary?.lowCount || 0,
        openCount: issuesData.data.summary?.openCount || 0,
        resolvedCount: issuesData.data.summary?.resolvedCount || 0,
        lastRunAt: runsData.data.runs[0]?.startedAt || null,
        lastRunStatus: runsData.data.runs[0]?.status || null,
      };

      setData({
        overview,
        recentIssues: issuesData.data.issues.slice(0, 5),
        recentRuns: runsData.data.runs
      });
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load audit dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const startNewAudit = async () => {
    try {
      setIsRunningAudit(true);
      
      const response = await fetch('/api/audit/runs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          enabledAnalyzers: ['typescript', 'eslint', 'security', 'performance'],
          categories: ['SECURITY', 'PERFORMANCE', 'MAINTAINABILITY', 'TESTING']
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to start audit');
      }

      const result = await response.json();
      toast.success('Audit started successfully!');
      
      // Refresh dashboard data
      setTimeout(() => {
        fetchDashboardData();
      }, 2000);
      
    } catch (err) {
      console.error('Error starting audit:', err);
      toast.error('Failed to start audit');
    } finally {
      setIsRunningAudit(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
            <div className="h-32 bg-gray-200 rounded-lg"></div>
            <div className="h-32 bg-gray-200 rounded-lg"></div>
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Audit Dashboard</h1>
          <p className="text-muted-foreground">
            Comprehensive codebase quality analysis and issue tracking.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchDashboardData}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            onClick={startNewAudit}
            disabled={isRunningAudit}
          >
            {isRunningAudit ? (
              <Clock className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            {isRunningAudit ? 'Running...' : 'Run Audit'}
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      {data && <AuditMetrics data={data.overview} />}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="issues">Recent Issues</TabsTrigger>
          <TabsTrigger value="runs">Audit Runs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {data && <RecentIssues issues={data.recentIssues} />}
            {data && <AuditRunsList runs={data.recentRuns} />}
          </div>
        </TabsContent>

        <TabsContent value="issues" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">All Issues</h2>
            <Button asChild>
              <Link href="/audit/issues">View All Issues</Link>
            </Button>
          </div>
          {data && <RecentIssues issues={data.recentIssues} showAll />}
        </TabsContent>

        <TabsContent value="runs" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Audit Runs History</h2>
            <Button asChild>
              <Link href="/audit/runs">View All Runs</Link>
            </Button>
          </div>
          {data && <AuditRunsList runs={data.recentRuns} showAll />}
        </TabsContent>
      </Tabs>
    </div>
  );
}
