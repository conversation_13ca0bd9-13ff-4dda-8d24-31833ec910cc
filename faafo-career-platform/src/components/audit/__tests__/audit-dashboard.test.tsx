/**
 * Audit Dashboard Component Tests
 * 
 * Tests for the audit dashboard UI components to ensure proper functionality
 * and integration with the audit system.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuditDashboard } from '@/components/audit/AuditDashboard';

// Mock fetch globally
global.fetch = jest.fn();

// Mock next/link
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
});

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children }: { children: React.ReactNode }) => <div data-testid="card">{children}</div>,
  CardContent: ({ children }: { children: React.ReactNode }) => <div data-testid="card-content">{children}</div>,
  CardDescription: ({ children }: { children: React.ReactNode }) => <div data-testid="card-description">{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: { children: React.ReactNode }) => <div data-testid="card-title">{children}</div>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button
      data-testid="button"
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  ),
}));

jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, defaultValue }: { children: React.ReactNode; defaultValue: string }) => (
    <div data-testid="tabs" data-default-value={defaultValue}>{children}</div>
  ),
  TabsContent: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <div data-testid="tabs-content" data-value={value}>{children}</div>
  ),
  TabsList: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tabs-list">{children}</div>
  ),
  TabsTrigger: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <button data-testid="tabs-trigger" data-value={value}>{children}</button>
  ),
}));

jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: { children: React.ReactNode }) => (
    <span data-testid="badge">{children}</span>
  ),
}));

jest.mock('@/components/ui/alert', () => ({
  Alert: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="alert">{children}</div>
  ),
  AlertDescription: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="alert-description">{children}</div>
  ),
}));

// Mock audit components
jest.mock('../AuditMetrics', () => ({
  AuditMetrics: ({ data }: { data: any }) => (
    <div data-testid="audit-metrics">
      <span>Total Issues: {data.totalIssues}</span>
      <span>Critical: {data.criticalCount}</span>
    </div>
  ),
}));

jest.mock('../RecentIssues', () => ({
  RecentIssues: ({ issues }: { issues: any[] }) => (
    <div data-testid="recent-issues">
      <span>Issues Count: {issues.length}</span>
    </div>
  ),
}));

jest.mock('../AuditRunsList', () => ({
  AuditRunsList: ({ runs }: { runs: any[] }) => (
    <div data-testid="audit-runs-list">
      <span>Runs Count: {runs.length}</span>
    </div>
  ),
}));

describe('AuditDashboard', () => {
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful API responses
    mockFetch.mockImplementation((url) => {
      if (url === '/api/audit/runs?limit=5') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            data: {
              runs: [
                {
                  id: 'run-1',
                  startedAt: '2024-01-01T00:00:00Z',
                  completedAt: '2024-01-01T00:05:00Z',
                  status: 'COMPLETED',
                  totalIssues: 5
                }
              ]
            }
          })
        } as Response);
      }
      
      if (url === '/api/audit/issues?limit=10') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            data: {
              summary: {
                totalIssues: 10,
                criticalCount: 2,
                highCount: 3,
                mediumCount: 3,
                lowCount: 2,
                openCount: 8,
                resolvedCount: 2
              },
              issues: [
                {
                  id: 'issue-1',
                  title: 'Test Issue',
                  severity: 'CRITICAL',
                  category: 'SECURITY',
                  filePath: '/test/file.ts',
                  createdAt: '2024-01-01T00:00:00Z'
                }
              ]
            }
          })
        } as Response);
      }
      
      return Promise.reject(new Error('Unknown URL'));
    });
  });

  it('renders audit dashboard with header', async () => {
    render(<AuditDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Audit Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Comprehensive codebase quality analysis and issue tracking.')).toBeInTheDocument();
    });
  });

  it('displays loading state initially', () => {
    render(<AuditDashboard />);

    // Should show loading skeleton - check for loading animation class
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('fetches and displays dashboard data', async () => {
    render(<AuditDashboard />);
    
    await waitFor(() => {
      expect(screen.getByTestId('audit-metrics')).toBeInTheDocument();
      expect(screen.getByText('Total Issues: 10')).toBeInTheDocument();
      expect(screen.getByText('Critical: 2')).toBeInTheDocument();
    });
    
    expect(screen.getAllByTestId('recent-issues')).toHaveLength(2); // One in overview, one in issues tab
    expect(screen.getAllByTestId('audit-runs-list')).toHaveLength(2); // One in overview, one in runs tab
  });

  it('handles refresh button click', async () => {
    render(<AuditDashboard />);
    
    await waitFor(() => {
      expect(screen.getByTestId('audit-metrics')).toBeInTheDocument();
    });
    
    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);
    
    // Should trigger another fetch
    expect(mockFetch).toHaveBeenCalledTimes(4); // 2 initial + 2 refresh
  });

  it('handles run audit button click', async () => {
    // Mock POST request for starting audit
    mockFetch.mockImplementation((url, options) => {
      if (url === '/api/audit/runs' && options?.method === 'POST') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            data: {
              id: 'new-audit-run',
              status: 'started',
              message: 'Audit run initiated successfully'
            }
          })
        } as Response);
      }
      
      // Return existing mock for GET requests
      if (url === '/api/audit/runs?limit=5') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            data: { runs: [] }
          })
        } as Response);
      }
      
      if (url === '/api/audit/issues?limit=10') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            data: {
              summary: {
                totalIssues: 0,
                criticalCount: 0,
                highCount: 0,
                mediumCount: 0,
                lowCount: 0,
                openCount: 0,
                resolvedCount: 0
              },
              issues: []
            }
          })
        } as Response);
      }
      
      return Promise.reject(new Error('Unknown URL'));
    });
    
    render(<AuditDashboard />);
    
    await waitFor(() => {
      expect(screen.getByTestId('audit-metrics')).toBeInTheDocument();
    });
    
    const runAuditButton = screen.getByText('Run Audit');
    fireEvent.click(runAuditButton);
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/audit/runs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          enabledAnalyzers: ['typescript', 'eslint', 'security', 'performance'],
          categories: ['SECURITY', 'PERFORMANCE', 'MAINTAINABILITY', 'TESTING']
        }),
      });
    });
  });

  it('displays error state when API fails', async () => {
    mockFetch.mockRejectedValue(new Error('API Error'));
    
    render(<AuditDashboard />);
    
    await waitFor(() => {
      expect(screen.getByTestId('alert')).toBeInTheDocument();
      expect(screen.getByText('Failed to load audit dashboard data')).toBeInTheDocument();
    });
  });

  it('renders tabs correctly', async () => {
    render(<AuditDashboard />);
    
    await waitFor(() => {
      expect(screen.getByTestId('tabs')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Recent Issues')).toBeInTheDocument();
    expect(screen.getByText('Audit Runs')).toBeInTheDocument();
  });
});
