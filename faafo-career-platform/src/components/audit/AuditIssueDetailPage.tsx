'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  AlertTriangle,
  Bug,
  Shield,
  Zap,
  Code,
  Database,
  TestTube,
  FileText,
  ArrowRight,
  Clock,
  RefreshCw,
  MessageSquare,
  User,
  Calendar,
  MapPin,
  CheckCircle,
  XCircle,
  Edit,
  Save,
  X,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface AuditIssueDetail {
  id: string;
  auditRunId: string;
  severity: string;
  category: string;
  title: string;
  description: string;
  filePath: string;
  lineNumber: number | null;
  columnNumber: number | null;
  codeSnippet: string | null;
  recommendation: string | null;
  fixExample: string | null;
  status: string;
  assignedToId: string | null;
  resolvedAt: string | null;
  falsePositive: boolean;
  createdAt: string;
  updatedAt: string;
  assignedTo?: {
    id: string;
    name: string | null;
    email: string;
  };
  auditRun: {
    id: string;
    startedAt: string;
    status: string;
  };
  comments: Array<{
    id: string;
    comment: string;
    createdAt: string;
    user: {
      id: string;
      name: string | null;
      email: string;
    };
  }>;
}

interface AuditIssueDetailPageProps {
  issueId: string;
}

export function AuditIssueDetailPage({ issueId }: AuditIssueDetailPageProps) {
  const [issue, setIssue] = useState<AuditIssueDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [isAddingComment, setIsAddingComment] = useState(false);

  const fetchIssueDetail = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/audit/issues/${issueId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Issue not found');
        }
        throw new Error('Failed to fetch issue details');
      }
      
      const result = await response.json();
      setIssue(result.data);
    } catch (err) {
      console.error('Error fetching issue details:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  const updateIssueStatus = async (newStatus: string) => {
    if (!issue) return;
    
    try {
      setIsUpdating(true);
      
      const response = await fetch(`/api/audit/issues/${issueId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update issue status');
      }

      const result = await response.json();
      setIssue(result.data);
      toast.success('Issue status updated successfully');
      
    } catch (err) {
      console.error('Error updating issue status:', err);
      toast.error('Failed to update issue status');
    } finally {
      setIsUpdating(false);
    }
  };

  const addComment = async () => {
    if (!newComment.trim() || !issue) return;
    
    try {
      setIsAddingComment(true);
      
      const response = await fetch(`/api/audit/issues/${issueId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          comment: newComment.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add comment');
      }

      const result = await response.json();
      
      // Refresh issue details to get updated comments
      await fetchIssueDetail();
      setNewComment('');
      toast.success('Comment added successfully');
      
    } catch (err) {
      console.error('Error adding comment:', err);
      toast.error('Failed to add comment');
    } finally {
      setIsAddingComment(false);
    }
  };

  useEffect(() => {
    fetchIssueDetail();
  }, [issueId]);

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return <AlertTriangle className="h-5 w-5" />;
      case 'high':
        return <Bug className="h-5 w-5" />;
      case 'medium':
        return <Zap className="h-5 w-5" />;
      case 'low':
        return <Code className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'security':
        return <Shield className="h-5 w-5" />;
      case 'performance':
        return <Zap className="h-5 w-5" />;
      case 'testing':
        return <TestTube className="h-5 w-5" />;
      case 'architecture':
        return <Database className="h-5 w-5" />;
      default:
        return <Code className="h-5 w-5" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'text-red-600 dark:text-red-400';
      case 'high':
        return 'text-orange-600 dark:text-orange-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'low':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getSeverityVariant = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!issue) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>Issue not found</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/audit/issues">
                <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
                Back to Issues
              </Link>
            </Button>
          </div>
          <div className="flex items-center gap-3 mb-2">
            <div className={cn('flex-shrink-0', getSeverityColor(issue.severity))}>
              {getSeverityIcon(issue.severity)}
            </div>
            <Badge variant={getSeverityVariant(issue.severity) as any}>
              {issue.severity}
            </Badge>
            <div className="flex items-center gap-1 text-gray-500">
              {getCategoryIcon(issue.category)}
              <span className="text-sm">{issue.category}</span>
            </div>
            <Badge variant="outline">
              {issue.status.replace('_', ' ')}
            </Badge>
          </div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
            {issue.title}
          </h1>
          <p className="text-muted-foreground mt-2">
            Issue #{issue.id.slice(-8)} • Found {formatDateTime(issue.createdAt)}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={fetchIssueDetail}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Issue Details */}
          <Card>
            <CardHeader>
              <CardTitle>Issue Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Description</h4>
                <p className="text-gray-700 dark:text-gray-300">{issue.description}</p>
              </div>
              
              <Separator />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">File Path</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
                    {issue.filePath}
                  </p>
                </div>
                {issue.lineNumber && (
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">Location</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Line {issue.lineNumber}
                      {issue.columnNumber && `, Column ${issue.columnNumber}`}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Code Snippet */}
          {issue.codeSnippet && (
            <Card>
              <CardHeader>
                <CardTitle>Code Snippet</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm">
                  <code>{issue.codeSnippet}</code>
                </pre>
              </CardContent>
            </Card>
          )}

          {/* Recommendation */}
          {issue.recommendation && (
            <Card>
              <CardHeader>
                <CardTitle>Recommendation</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 dark:text-gray-300 mb-4">{issue.recommendation}</p>
                
                {issue.fixExample && (
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Fix Example</h4>
                    <pre className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{issue.fixExample}</code>
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Comments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Comments ({issue.comments.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Existing Comments */}
              {issue.comments.length > 0 ? (
                <div className="space-y-4">
                  {issue.comments.map((comment) => (
                    <div key={comment.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          {comment.user.name || comment.user.email}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatDateTime(comment.createdAt)}
                        </span>
                      </div>
                      <p className="text-gray-700 dark:text-gray-300">{comment.comment}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No comments yet</p>
              )}
              
              {/* Add Comment */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <Textarea
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="mb-2"
                />
                <Button
                  onClick={addComment}
                  disabled={!newComment.trim() || isAddingComment}
                  size="sm"
                >
                  {isAddingComment ? (
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <MessageSquare className="h-4 w-4 mr-2" />
                  )}
                  Add Comment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Management */}
          <Card>
            <CardHeader>
              <CardTitle>Status Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                  Current Status
                </label>
                <Select
                  value={issue.status}
                  onValueChange={updateIssueStatus}
                  disabled={isUpdating}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="OPEN">Open</SelectItem>
                    <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                    <SelectItem value="RESOLVED">Resolved</SelectItem>
                    <SelectItem value="DEFERRED">Deferred</SelectItem>
                    <SelectItem value="FALSE_POSITIVE">False Positive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {issue.resolvedAt && (
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 block">
                    Resolved At
                  </label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDateTime(issue.resolvedAt)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Issue Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Issue Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {formatDateTime(issue.createdAt)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Last Updated</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {formatDateTime(issue.updatedAt)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Audit Run</p>
                  <Link 
                    href={`/audit/runs/${issue.auditRunId}`}
                    className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400"
                  >
                    #{issue.auditRunId.slice(-8)}
                  </Link>
                </div>
              </div>
              
              {issue.assignedTo && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Assigned To</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {issue.assignedTo.name || issue.assignedTo.email}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
