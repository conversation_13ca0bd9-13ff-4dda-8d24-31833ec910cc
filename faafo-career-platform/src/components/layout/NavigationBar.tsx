'use client'; // Required for useSession hook

import React from 'react';
import Link from 'next/link';
import { signIn, signOut } from 'next-auth/react';
import { useTheme } from 'next-themes';
import { useState, useEffect } from 'react';
import { Compass, HomeIcon, Settings2, User, LogOut, LogIn, Moon, UserPlus, Sun, HelpCircle, MessageSquare, Briefcase, BookOpen, Menu, X, Target, Calculator, TrendingUp, Clock, Award, ChevronDown, BarChart3, Shield, Users, FileText, Bug } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuthStatus } from '@/hooks/useAuthState';
import { UserMenu } from '@/components/layout/UserMenu';
import { useNavigationState, useNavigationShortcuts } from '@/hooks/useNavigationState';
import { AuthNavigationSkeleton } from '@/components/ui/auth-loading-states';

export function NavigationBar() {
  // Use unified auth state instead of direct useSession
  const { isAuthenticated, user, isAdmin, isLoading } = useAuthStatus();
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Use persistent navigation state
  const {
    isMobileMenuOpen,
    isToolsDropdownOpen,
    toggleMobileMenu,
    closeMobileMenu,
    toggleToolsDropdown,
    closeToolsDropdown
  } = useNavigationState();



  // Enable keyboard shortcuts
  useNavigationShortcuts();

  // Fix hydration mismatch by only rendering theme toggle after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Note: Click outside handling is now managed by the navigation state manager

  return (
    <div className="sticky top-0 z-[9998] bg-background dark:bg-card shadow-sm dark:shadow-gray-700">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" aria-label="Main navigation" role="navigation">
        <div className="w-full py-3 flex items-center justify-between">
          <div className="flex items-center">
            <Link
              href="/"
              className="flex items-center text-foreground"
              aria-label="FAAFO Career Platform - Home"
            >
              <Compass className="h-8 w-8 mr-2 text-primary" aria-hidden="true" />
              <span className="text-2xl font-bold">FAAFO</span>
            </Link>
          </div>
          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleMobileMenu();
              }}
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
              aria-label={isMobileMenuOpen ? 'Close main menu' : 'Open main menu'}
              className="inline-flex items-center justify-center p-3 min-h-[44px] min-w-[44px] rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
              data-navigation-menu
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex ml-6 space-x-2 items-center">
            <Link
              href="/"
              className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
              aria-label="Go to home page"
            >
              <HomeIcon className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
              <span className="hidden xl:inline">Home</span>
            </Link>
            <Link
              href="/career-paths"
              className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
              aria-label="Explore career paths"
            >
              <Briefcase className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
              <span className="hidden xl:inline">Paths</span>
            </Link>
            <Link
              href="/resources"
              className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
              aria-label="Browse learning resources"
            >
              <BookOpen className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
              <span className="hidden xl:inline">Resources</span>
            </Link>
            <div className="relative">
              <button
                onClick={toggleToolsDropdown}
                className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
                data-tools-dropdown
              >
                <Settings2 className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
                <span className="hidden xl:inline">Tools</span>
                <ChevronDown className="h-3 w-3 text-gray-600 dark:text-gray-300" aria-hidden="true" />
              </button>
              {isToolsDropdownOpen && (
                <div
                  className="absolute left-0 mt-2 w-56 bg-popover border border-border rounded-md shadow-md z-[9999]"
                  style={{ zIndex: 9999 }}
                  data-tools-dropdown
                >
                  <div className="py-1">
                    <div className="px-3 py-2 text-xs font-semibold text-muted-foreground border-b border-border">
                      Career Tools
                    </div>
                    <Link
                      href="/assessment"
                      className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                      onClick={closeToolsDropdown}
                    >
                      <Target className="h-4 w-4 mr-2 text-muted-foreground" />
                      Career Assessment
                    </Link>
                    <Link
                      href="/tools/salary-calculator"
                      className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                      onClick={closeToolsDropdown}
                    >
                      <Calculator className="h-4 w-4 mr-2 text-muted-foreground" />
                      Salary Calculator
                    </Link>
                    <Link
                      href="/interview-practice"
                      className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                      onClick={closeToolsDropdown}
                    >
                      <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                      Interview Practice
                    </Link>
                    <Link
                      href="/resume-builder"
                      className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                      onClick={closeToolsDropdown}
                    >
                      <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                      Resume Builder
                    </Link>
                    <Link
                      href="/skills/gap-analyzer"
                      className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                      onClick={closeToolsDropdown}
                    >
                      <Target className="h-4 w-4 mr-2 text-green-600 dark:text-green-400" />
                      Skill Gap Analyzer
                    </Link>
                    {isAuthenticated && isAdmin && (
                      <>
                        <Link
                          href="/admin/analytics"
                          className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                          onClick={closeToolsDropdown}
                        >
                          <Shield className="h-4 w-4 mr-2 text-orange-600 dark:text-orange-400" />
                          Platform Analytics
                        </Link>
                        <Link
                          href="/audit"
                          className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                          onClick={closeToolsDropdown}
                        >
                          <Bug className="h-4 w-4 mr-2 text-red-600 dark:text-red-400" />
                          Audit Dashboard
                        </Link>
                      </>
                    )}

                    <div className="border-t border-border my-1"></div>
                    <Link
                      href="/tools"
                      className="flex items-center px-3 py-2 text-sm text-popover-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
                      onClick={closeToolsDropdown}
                    >
                      <Settings2 className="h-4 w-4 mr-2 text-muted-foreground" />
                      View All Tools
                    </Link>
                  </div>
                </div>
              )}
            </div>
            <Link
              href="/help"
              className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md transition-colors"
              aria-label="Get help and support"
            >
              <HelpCircle className="h-5 w-5 text-gray-600 dark:text-gray-300" aria-hidden="true" />
              <span>Help</span>
            </Link>



            <UserMenu variant="desktop" />

            {mounted ? (
              <button
                aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
                className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 px-3 py-2 min-h-[44px] ml-1 inline-flex items-center space-x-1 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded-md transition-colors"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              >
                {theme === 'dark' ? (
                  <Sun className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
                ) : (
                  <Moon className="h-4 w-4 text-gray-600 dark:text-gray-300" aria-hidden="true" />
                )}
                <span className="hidden xl:inline">{theme === 'dark' ? 'Light' : 'Dark'}</span>
              </button>
            ) : (
              <div className="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse ml-1"></div>
            )}
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div
            id="mobile-menu"
            className="md:hidden border-t border-gray-200 dark:border-gray-700"
            role="menu"
            aria-label="Mobile navigation menu"
            data-navigation-menu
          >
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white dark:bg-gray-900">
              <Link
                href="/"
                onClick={closeMobileMenu}
                className="block px-3 py-3 min-h-[44px] rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <HomeIcon className="h-5 w-5 mr-3" aria-hidden="true" />
                  Home
                </div>
              </Link>
              <Link
                href="/career-paths"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <Briefcase className="h-5 w-5 mr-3" aria-hidden="true" />
                  Career Paths
                </div>
              </Link>
              <Link
                href="/resources"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-3" aria-hidden="true" />
                  Resources
                </div>
              </Link>
              <Link
                href="/tools"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <Settings2 className="h-5 w-5 mr-3" aria-hidden="true" />
                  Tools
                </div>
              </Link>
              <Link
                href="/tools/salary-calculator"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <Calculator className="h-5 w-5 mr-3" aria-hidden="true" />
                  Salary Calculator
                </div>
              </Link>
              <Link
                href="/interview-practice"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-3" aria-hidden="true" />
                  Interview Practice
                </div>
              </Link>
              <Link
                href="/resume-builder"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <FileText className="h-5 w-5 mr-3" aria-hidden="true" />
                  Resume Builder
                </div>
              </Link>
              <Link
                href="/skills/gap-analyzer"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <Target className="h-5 w-5 mr-3" aria-hidden="true" />
                  Skill Gap Analyzer
                </div>
              </Link>
              <Link
                href="/help"
                onClick={closeMobileMenu}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <div className="flex items-center">
                  <HelpCircle className="h-5 w-5 mr-3" aria-hidden="true" />
                  Help
                </div>
              </Link>

              <UserMenu variant="mobile" onItemClick={closeMobileMenu} />

              {mounted ? (
                <button
                  onClick={() => {
                    setTheme(theme === 'dark' ? 'light' : 'dark');
                    closeMobileMenu();
                  }}
                  className="w-full text-left block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <div className="flex items-center">
                    {theme === 'dark' ? (
                      <Sun className="h-5 w-5 mr-3" aria-hidden="true" />
                    ) : (
                      <Moon className="h-5 w-5 mr-3" aria-hidden="true" />
                    )}
                    {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
                  </div>
                </button>
              ) : (
                <div className="px-3 py-2">
                  <div className="h-6 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              )}
            </div>
          </div>
        )}
      </nav>
    </div>
  );
}
