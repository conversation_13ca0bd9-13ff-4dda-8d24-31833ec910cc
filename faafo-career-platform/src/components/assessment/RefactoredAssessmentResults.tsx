/**
 * Refactored Assessment Results Component
 * Main component that orchestrates all assessment result modules
 */

'use client';

import React from 'react';
import { useAssessmentResults, useAssessmentStats } from './hooks/useAssessmentResults';
import { ScoreCards } from './components/ScoreCards';
import { CareerSuggestions } from './components/CareerSuggestions';
import { LearningResources } from './components/LearningResources';
import { 
  AssessmentResultsLoading, 
  AssessmentResultsError, 
  AssessmentResultsEmpty 
} from './components/StateComponents';

export interface RefactoredAssessmentResultsProps {
  assessmentId: string;
}

/**
 * Header Component
 */
const AssessmentResultsHeader: React.FC<{ stats: any }> = ({ stats }) => {
  return (
    <div className="text-center mb-8">
      <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        Your Career Assessment Results
      </h1>
      <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">
        Personalized insights and recommendations based on your responses
      </p>
      {stats.hasPersonalizedRecommendations && (
        <p className="text-sm text-blue-600 dark:text-blue-400">
          ✨ Enhanced with AI-powered recommendations
        </p>
      )}
    </div>
  );
};

/**
 * Results Summary Component
 */
const ResultsSummary: React.FC<{ results: any; stats: any }> = ({ results, stats }) => {
  const { insights } = results;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      {/* Readiness Level */}
      <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
          Readiness Level
        </h3>
        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
          {stats.readinessLevel}
        </p>
        <p className="text-xs text-blue-700 dark:text-blue-300">
          {insights.scores.readinessScore}% overall score
        </p>
      </div>

      {/* Career Matches */}
      <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
        <h3 className="text-sm font-medium text-green-900 dark:text-green-100 mb-1">
          Career Matches
        </h3>
        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
          {stats.totalCareerSuggestions}
        </p>
        <p className="text-xs text-green-700 dark:text-green-300">
          {stats.averageCareerMatch.toFixed(0)}% avg match
        </p>
      </div>

      {/* Learning Resources */}
      <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
        <h3 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
          Learning Resources
        </h3>
        <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
          {stats.totalLearningResources}
        </p>
        <p className="text-xs text-purple-700 dark:text-purple-300">
          Personalized for you
        </p>
      </div>
    </div>
  );
};

/**
 * Key Insights Component
 */
const KeyInsights: React.FC<{ insights: any }> = ({ insights }) => {
  if (!insights.keyRecommendations || insights.keyRecommendations.length === 0) {
    return null;
  }

  return (
    <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
        🎯 Key Insights & Recommendations
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Primary Motivation */}
        {insights.primaryMotivation && (
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Primary Motivation
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {insights.primaryMotivation}
            </p>
          </div>
        )}

        {/* Top Skills */}
        {insights.topSkills && insights.topSkills.length > 0 && (
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Top Skills
            </h3>
            <div className="flex flex-wrap gap-2">
              {insights.topSkills.slice(0, 5).map((skill: string, index: number) => (
                <span 
                  key={index}
                  className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Recommended Timeline */}
        {insights.recommendedTimeline && (
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Recommended Timeline
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {insights.recommendedTimeline}
            </p>
          </div>
        )}

        {/* Biggest Obstacles */}
        {insights.biggestObstacles && insights.biggestObstacles.length > 0 && (
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Areas to Address
            </h3>
            <div className="space-y-1">
              {insights.biggestObstacles.slice(0, 3).map((obstacle: string, index: number) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-1 h-1 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-xs text-gray-600 dark:text-gray-400">{obstacle}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Key Recommendations */}
      {insights.keyRecommendations && insights.keyRecommendations.length > 0 && (
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
            🚀 Next Steps
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {insights.keyRecommendations.slice(0, 4).map((recommendation: string, index: number) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-white dark:bg-gray-800/50 rounded-lg">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0">
                  {index + 1}
                </div>
                <p className="text-sm text-gray-700 dark:text-gray-300">{recommendation}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Main Refactored Assessment Results Component
 */
export const RefactoredAssessmentResults: React.FC<RefactoredAssessmentResultsProps> = ({ 
  assessmentId 
}) => {
  const { results, loading, error, refetch } = useAssessmentResults(assessmentId);
  const stats = useAssessmentStats(results);

  // Loading state
  if (loading) {
    return <AssessmentResultsLoading />;
  }

  // Error state
  if (error) {
    return <AssessmentResultsError error={error} onRetry={refetch} />;
  }

  // Empty state
  if (!results) {
    return <AssessmentResultsEmpty />;
  }

  const { insights, careerSuggestions, personalizedRecommendations } = results;

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <AssessmentResultsHeader stats={stats} />

      {/* Results Summary */}
      <ResultsSummary results={results} stats={stats} />

      {/* Key Insights */}
      <KeyInsights insights={insights} />

      {/* Score Cards */}
      <ScoreCards 
        scores={insights.scores} 
        readinessLevel={stats.readinessLevel} 
      />

      {/* Career Suggestions */}
      {careerSuggestions && careerSuggestions.length > 0 && (
        <CareerSuggestions suggestions={careerSuggestions} />
      )}

      {/* Learning Resources */}
      {personalizedRecommendations?.learningResources && 
       personalizedRecommendations.learningResources.length > 0 && (
        <LearningResources resources={personalizedRecommendations.learningResources} />
      )}

      {/* Footer Actions */}
      <div className="text-center pt-8 border-t border-gray-200 dark:border-gray-700">
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Want to update your results? Take the assessment again to get fresh insights.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={refetch}
            className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Refresh Results
          </button>
          <button
            onClick={() => window.location.href = '/assessment'}
            className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Retake Assessment
          </button>
        </div>
      </div>
    </div>
  );
};

export default RefactoredAssessmentResults;
