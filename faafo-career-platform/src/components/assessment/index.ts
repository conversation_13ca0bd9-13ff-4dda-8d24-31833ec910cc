/**
 * Assessment Components Module Exports
 * Centralized exports for all assessment-related components and hooks
 */

// Main refactored component
export { 
  RefactoredAssessmentResults as AssessmentResults,
  default as RefactoredAssessmentResults,
  type RefactoredAssessmentResultsProps
} from './RefactoredAssessmentResults';

// Custom hooks
export {
  useAssessmentResults,
  useAssessmentStats,
  useCareerSuggestions,
  useLearningResources,
  useAssessmentActions,
  type AssessmentResultsData,
  type UseAssessmentResultsReturn
} from './hooks/useAssessmentResults';

// Score components
export {
  ScoreCards,
  ReadinessScoreCard,
  IndividualScoreCard,
  getReadinessLevel,
  getScoreColor,
  getScoreBackground,
  type ScoreData,
  type ReadinessCardProps,
  type ScoreCardsProps,
  type IndividualScoreCardProps
} from './components/ScoreCards';

// Career suggestions components
export {
  CareerSuggestions,
  CareerPathCard,
  FilterControls,
  getMatchLevel,
  type CareerSuggestion,
  type CareerSuggestionsProps,
  type CareerPathCardProps,
  type FilterControlsProps
} from './components/CareerSuggestions';

// Learning resources components
export {
  LearningResources,
  ResourceCard,
  ResourceGrid,
  ResourcesByType,
  ResourcesByLevel,
  getSkillLevelColor,
  getTypeIcon,
  type LearningResource,
  type LearningResourcesProps,
  type ResourceCardProps,
  type ResourceGridProps,
  type ResourcesByTypeProps,
  type ResourcesByLevelProps
} from './components/LearningResources';

// State components
export {
  AssessmentResultsLoading,
  AssessmentResultsError,
  AssessmentResultsEmpty,
  NetworkError,
  MaintenanceMode,
  type AssessmentResultsErrorProps
} from './components/StateComponents';

// Legacy compatibility - export the refactored component as the original name
export { RefactoredAssessmentResults as default } from './RefactoredAssessmentResults';
