import React from 'react';
interface ScaleQuestionProps {
  questionKey: string;
  minLabel: string;
  maxLabel: string;
  numberOfSteps: number;
  currentValue: number | null;
  onChange: (questionKey: string, value: number) => void;
}

const ScaleQuestion: React.FC<ScaleQuestionProps> = ({
  questionKey,
  minLabel,
  maxLabel,
  numberOfSteps = 5,
  currentValue,
  onChange,
}) => {
  const handleChange = (value: number) => {
    onChange(questionKey, value);
  };

  const steps = Array.from({ length: numberOfSteps }, (_, i) => i + 1);

  return (
    <div className="space-y-4">
      {/* Scale Labels */}
      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
        <span className="text-left max-w-[40%]">{minLabel}</span>
        <span className="text-right max-w-[40%]">{maxLabel}</span>
      </div>

      {/* Scale Options */}
      <div className="flex justify-between items-center space-x-2">
        {steps.map((step) => (
          <label
            key={step}
            className="flex flex-col items-center space-y-2 cursor-pointer group"
          >
            <input
              type="radio"
              name={questionKey}
              value={step}
              checked={currentValue === step}
              onChange={() => handleChange(step)}
              className="sr-only"
            />
            <div
              className={`
                w-12 h-12 rounded-full border-2 flex items-center justify-center
                transition-all duration-200 group-hover:scale-110
                ${currentValue === step
                  ? 'border-blue-500 bg-blue-500 text-white shadow-lg'
                  : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                }
              `}
            >
              <span className="text-sm font-medium">{step}</span>
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {step}
            </span>
          </label>
        ))}
      </div>

      {/* Current Selection Display */}
      {currentValue && (
        <div className="text-center">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Selected: <span className="font-medium text-blue-600 dark:text-blue-400">{currentValue}</span>
          </span>
        </div>
      )}

      {/* Helper Text */}
      <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
        Click on a number to select your rating
      </div>
    </div>
  );
};

export default ScaleQuestion;
