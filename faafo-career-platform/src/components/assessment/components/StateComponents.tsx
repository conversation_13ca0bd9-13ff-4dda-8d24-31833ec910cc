/**
 * Assessment State Components
 * Loading, error, and empty state components for assessment results
 */

import React from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, Target, RefreshCw } from 'lucide-react';
import { getUserFriendlyError } from '@/lib/user-friendly-errors';

/**
 * Loading State Component
 */
export const AssessmentResultsLoading: React.FC = () => {
  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header Skeleton */}
      <div className="text-center mb-8">
        <Skeleton className="h-8 w-64 mx-auto mb-4" />
        <Skeleton className="h-4 w-96 mx-auto" />
      </div>

      {/* Overall Score Skeleton */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-6 w-24" />
        </div>
        <div className="p-4 rounded-lg mb-4">
          <div className="flex items-center justify-between mb-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-8 w-16" />
          </div>
          <Skeleton className="h-3 w-full mb-2" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        <div className="text-center">
          <Skeleton className="h-4 w-48 mx-auto" />
        </div>
      </Card>

      {/* Score Cards Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i} className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-5 w-24" />
              </div>
              <Skeleton className="h-5 w-12" />
            </div>
            <Skeleton className="h-2 w-full mb-3" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-3 w-3/4" />
          </Card>
        ))}
      </div>

      {/* Career Suggestions Skeleton */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-6 w-24" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="mb-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-5/6" />
              </div>
              <div className="p-3 rounded-lg mb-4">
                <div className="flex items-center justify-between mb-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-5 w-12" />
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-20" />
              </div>
            </Card>
          ))}
        </div>
      </Card>

      {/* Learning Resources Skeleton */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-6 w-24" />
        </div>
        <div className="mb-6">
          <Skeleton className="h-12 w-full" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-4">
              <div className="mb-3">
                <div className="flex items-start gap-2 mb-2">
                  <Skeleton className="h-4 w-4 mt-1" />
                  <Skeleton className="h-5 w-3/4" />
                </div>
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-5/6" />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-20" />
                </div>
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="mt-4 pt-3 border-t">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-5 w-12" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </Card>
    </div>
  );
};

/**
 * Error State Component
 */
export interface AssessmentResultsErrorProps {
  error: string | Error;
  onRetry?: () => void;
}

export const AssessmentResultsError: React.FC<AssessmentResultsErrorProps> = ({ 
  error, 
  onRetry 
}) => {
  const friendlyError = getUserFriendlyError(error, 'assessment');

  return (
    <div className="container mx-auto py-8">
      <Card className="p-8 text-center max-w-md mx-auto">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        
        <h2 className="text-xl font-semibold text-red-700 dark:text-red-400 mb-2">
          {friendlyError.title}
        </h2>
        
        <p className="text-red-600 dark:text-red-300 mb-4">
          {friendlyError.message}
        </p>
        
        {friendlyError.action && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            {friendlyError.action}
          </p>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <Button onClick={onRetry} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
          
          <Button asChild>
            <Link href="/assessment">Take Assessment Again</Link>
          </Button>
        </div>

        {/* Additional Help */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 mb-2">
            Still having trouble?
          </p>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/support">Contact Support</Link>
          </Button>
        </div>
      </Card>
    </div>
  );
};

/**
 * Empty State Component
 */
export const AssessmentResultsEmpty: React.FC = () => {
  return (
    <div className="container mx-auto py-8">
      <Card className="p-8 text-center max-w-md mx-auto">
        <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        
        <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
          No Results Available
        </h2>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Complete your career assessment to receive personalized insights, 
          career suggestions, and learning recommendations tailored to your goals.
        </p>

        <div className="space-y-3">
          <Button asChild className="w-full">
            <Link href="/assessment">Take Assessment</Link>
          </Button>
          
          <Button variant="outline" asChild className="w-full">
            <Link href="/career-paths">Browse Career Paths</Link>
          </Button>
        </div>

        {/* Benefits Preview */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
            What you'll get:
          </h3>
          <div className="space-y-2 text-xs text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-2">
              <div className="w-1 h-1 bg-blue-500 rounded-full" />
              <span>Personalized career readiness score</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1 h-1 bg-blue-500 rounded-full" />
              <span>AI-powered career path recommendations</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1 h-1 bg-blue-500 rounded-full" />
              <span>Curated learning resources</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1 h-1 bg-blue-500 rounded-full" />
              <span>Actionable next steps</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

/**
 * Network Error Component
 */
export const NetworkError: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => {
  return (
    <div className="container mx-auto py-8">
      <Card className="p-8 text-center max-w-md mx-auto">
        <div className="h-12 w-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
        </div>
        
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Connection Problem
        </h2>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          We're having trouble connecting to our servers. Please check your 
          internet connection and try again.
        </p>

        <div className="flex flex-col gap-3">
          {onRetry && (
            <Button onClick={onRetry}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry Connection
            </Button>
          )}
          
          <Button variant="outline" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500">
            If the problem persists, please contact our support team.
          </p>
        </div>
      </Card>
    </div>
  );
};

/**
 * Maintenance Mode Component
 */
export const MaintenanceMode: React.FC = () => {
  return (
    <div className="container mx-auto py-8">
      <Card className="p-8 text-center max-w-md mx-auto">
        <div className="h-12 w-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
        </div>
        
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Temporarily Unavailable
        </h2>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          We're currently performing maintenance to improve your experience. 
          Please try again in a few minutes.
        </p>

        <Button variant="outline" onClick={() => window.location.reload()}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Check Again
        </Button>

        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500">
            Expected completion: Usually within 15 minutes
          </p>
        </div>
      </Card>
    </div>
  );
};
