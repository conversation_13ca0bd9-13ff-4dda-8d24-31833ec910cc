/**
 * Learning Resources Component
 * Displays personalized learning resources and recommendations
 */

import React from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  BookOpen, 
  GraduationCap, 
  Star, 
  Timer, 
  ExternalLink,
  Award,
  Filter,
  Zap,
  Target
} from 'lucide-react';
import { useLearningResources } from '../hooks/useAssessmentResults';

export interface LearningResource {
  id: string;
  title: string;
  description: string;
  type: 'COURSE' | 'BOOK' | 'VIDEO' | 'ARTICLE';
  category: string;
  skillLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  duration?: string;
  cost: 'FREE' | 'PAID' | 'FREEMIUM';
  averageRating?: number;
  totalRatings?: number;
  url?: string;
}

export interface LearningResourcesProps {
  resources: LearningResource[];
}

/**
 * Get skill level color
 */
export function getSkillLevelColor(level: string): string {
  const normalizedLevel = level.toLowerCase();
  switch (normalizedLevel) {
    case 'beginner':
      return 'text-green-700 border-green-200 bg-green-50 dark:text-green-300 dark:border-green-800 dark:bg-green-900/20';
    case 'intermediate':
      return 'text-yellow-700 border-yellow-200 bg-yellow-50 dark:text-yellow-300 dark:border-yellow-800 dark:bg-yellow-900/20';
    case 'advanced':
      return 'text-red-700 border-red-200 bg-red-50 dark:text-red-300 dark:border-red-800 dark:bg-red-900/20';
    default:
      return 'text-gray-700 border-gray-200 bg-gray-50 dark:text-gray-300 dark:border-gray-800 dark:bg-gray-900/20';
  }
}

/**
 * Get resource type icon
 */
export function getTypeIcon(type: string) {
  switch (type) {
    case 'COURSE': return <GraduationCap className="h-4 w-4" />;
    case 'BOOK': return <BookOpen className="h-4 w-4" />;
    case 'VIDEO': return <Star className="h-4 w-4" />;
    case 'ARTICLE': return <Target className="h-4 w-4" />;
    default: return <BookOpen className="h-4 w-4" />;
  }
}

/**
 * Enhanced Resource Card Component
 */
export interface ResourceCardProps {
  resource: LearningResource;
  isPriority?: boolean;
}

export const ResourceCard: React.FC<ResourceCardProps> = ({ resource, isPriority = false }) => {
  const levelColor = getSkillLevelColor(resource.skillLevel);

  return (
    <Card className={`p-4 hover:shadow-md transition-shadow relative ${
      isPriority ? 'ring-1 ring-purple-200 dark:ring-purple-800' : ''
    }`}>
      {isPriority && (
        <div className="absolute top-2 right-2">
          <Badge variant="default" className="bg-purple-600 text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Priority
          </Badge>
        </div>
      )}

      <div className="mb-3">
        <div className="flex items-start gap-2 mb-2">
          <div className="text-purple-600 mt-1">
            {getTypeIcon(resource.type)}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 line-clamp-2 pr-8">
              {resource.title}
            </h4>
          </div>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
          {resource.description}
        </p>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Badge variant="outline" className={levelColor}>
            {resource.skillLevel}
          </Badge>
          <Badge variant="outline">{resource.category}</Badge>
        </div>

        {resource.duration && (
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Timer className="h-3 w-3" />
            <span>{resource.duration}</span>
          </div>
        )}

        {resource.averageRating && (
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span>{resource.averageRating.toFixed(1)} ({resource.totalRatings} reviews)</span>
          </div>
        )}
      </div>

      <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className={`text-xs px-2 py-1 rounded ${
            resource.cost === 'FREE' ? 'bg-green-100 text-green-800' :
            resource.cost === 'PAID' ? 'bg-blue-100 text-blue-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            {resource.cost}
          </span>
          <Button size="sm" variant="ghost" asChild>
            <Link href={resource.url || `/resources/${resource.id}`}>
              View Resource
              <ExternalLink className="h-3 w-3 ml-1" />
            </Link>
          </Button>
        </div>
      </div>
    </Card>
  );
};

/**
 * Resource Grid Component
 */
export interface ResourceGridProps {
  resources: LearningResource[];
  showPriority?: boolean;
  maxItems?: number;
}

export const ResourceGrid: React.FC<ResourceGridProps> = ({ 
  resources, 
  showPriority = false, 
  maxItems 
}) => {
  const displayResources = maxItems ? resources.slice(0, maxItems) : resources;

  if (resources.length === 0) {
    return (
      <div className="text-center py-8">
        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600 dark:text-gray-400">No resources found for this category.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {displayResources.map((resource, index) => (
        <ResourceCard 
          key={resource.id} 
          resource={resource} 
          isPriority={showPriority && index < 3}
        />
      ))}
    </div>
  );
};

/**
 * Resources by Type Component
 */
export interface ResourcesByTypeProps {
  resourcesByType: Record<string, LearningResource[]>;
}

export const ResourcesByType: React.FC<ResourcesByTypeProps> = ({ resourcesByType }) => {
  return (
    <div className="space-y-6">
      {['COURSE', 'BOOK', 'VIDEO', 'ARTICLE'].map(type => {
        const typeResources = resourcesByType[type] || [];
        if (typeResources.length === 0) return null;

        return (
          <div key={type}>
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 capitalize">
              {type.toLowerCase()}s ({typeResources.length})
            </h4>
            <ResourceGrid resources={typeResources} maxItems={3} />
          </div>
        );
      })}
    </div>
  );
};

/**
 * Resources by Level Component
 */
export interface ResourcesByLevelProps {
  resourcesByLevel: Record<string, LearningResource[]>;
}

export const ResourcesByLevel: React.FC<ResourcesByLevelProps> = ({ resourcesByLevel }) => {
  return (
    <div className="space-y-6">
      {['BEGINNER', 'INTERMEDIATE', 'ADVANCED'].map(level => {
        const levelResources = resourcesByLevel[level] || [];
        if (levelResources.length === 0) return null;

        return (
          <div key={level}>
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
              {level} Level ({levelResources.length})
            </h4>
            <ResourceGrid resources={levelResources} maxItems={3} />
          </div>
        );
      })}
    </div>
  );
};

/**
 * Main Learning Resources Component
 */
export const LearningResources: React.FC<LearningResourcesProps> = ({ resources }) => {
  const {
    activeTab,
    setActiveTab,
    resourcesByType,
    resourcesByLevel,
    priorityResources,
    totalResources
  } = useLearningResources(resources);

  if (resources.length === 0) {
    return (
      <Card className="p-8 text-center">
        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
          No Learning Resources Available
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Complete your assessment to receive personalized learning recommendations.
        </p>
        <Button asChild>
          <Link href="/assessment">Complete Assessment</Link>
        </Button>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-purple-600" />
          Personalized Learning Resources
        </h3>
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <Badge variant="outline" className="text-purple-600 border-purple-600">
            {totalResources} Resources
          </Badge>
        </div>
      </div>

      <div className="mb-6 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
        <p className="text-sm text-purple-700 dark:text-purple-300">
          <Award className="h-4 w-4 inline mr-2" />
          These resources are specifically chosen based on your career goals, current skills, and learning preferences.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="priority">Priority</TabsTrigger>
          <TabsTrigger value="type">By Type</TabsTrigger>
          <TabsTrigger value="level">By Level</TabsTrigger>
        </TabsList>

        <TabsContent value="priority" className="mt-6">
          <ResourceGrid resources={priorityResources} showPriority maxItems={6} />
        </TabsContent>

        <TabsContent value="type" className="mt-6">
          <ResourcesByType resourcesByType={resourcesByType} />
        </TabsContent>

        <TabsContent value="level" className="mt-6">
          <ResourcesByLevel resourcesByLevel={resourcesByLevel} />
        </TabsContent>
      </Tabs>

      <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
        <Button asChild>
          <Link href="/resources">Browse All Resources</Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/ai/skills-analysis">Get Skills Analysis</Link>
        </Button>
      </div>
    </Card>
  );
};
