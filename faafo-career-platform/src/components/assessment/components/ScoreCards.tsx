/**
 * Assessment Score Cards Components
 * Individual score display components for assessment results
 */

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Target, 
  TrendingUp, 
  Shield, 
  Clock, 
  Users, 
  DollarSign,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

export interface ScoreData {
  readinessScore: number;
  riskTolerance: number;
  urgencyLevel: number;
  skillsConfidence: number;
  supportLevel: number;
  financialReadiness: number;
}

export interface ReadinessCardProps {
  score: number;
  level: string;
}

export interface ScoreCardsProps {
  scores: ScoreData;
  readinessLevel: string;
}

/**
 * Get readiness level from score
 */
export function getReadinessLevel(score: number): string {
  if (score >= 80) return 'High';
  if (score >= 60) return 'Medium';
  return 'Low';
}

/**
 * Get score color based on value
 */
export function getScoreColor(score: number): string {
  if (score >= 80) return 'text-green-600';
  if (score >= 60) return 'text-yellow-600';
  return 'text-red-600';
}

/**
 * Get score background color
 */
export function getScoreBackground(score: number): string {
  if (score >= 80) return 'bg-green-50 dark:bg-green-900/20';
  if (score >= 60) return 'bg-yellow-50 dark:bg-yellow-900/20';
  return 'bg-red-50 dark:bg-red-900/20';
}

/**
 * Overall Readiness Score Card
 */
export const ReadinessScoreCard: React.FC<ReadinessCardProps> = ({ score, level }) => {
  const getReadinessIcon = () => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    return <Info className="h-5 w-5 text-red-600" />;
  };

  const getReadinessMessage = () => {
    if (score >= 80) return "You're well-prepared for your next career move!";
    if (score >= 60) return "You're on the right track with some areas to improve.";
    return "Focus on building foundational skills and confidence.";
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          Career Readiness Assessment
        </h2>
        <Badge variant={score >= 70 ? 'default' : 'secondary'}>
          {level} Readiness
        </Badge>
      </div>

      <div className={`p-4 rounded-lg mb-4 ${getScoreBackground(score)}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {getReadinessIcon()}
            <span className="font-medium text-gray-900 dark:text-gray-100">
              Overall Score
            </span>
          </div>
          <span className={`text-2xl font-bold ${getScoreColor(score)}`}>
            {score}%
          </span>
        </div>
        <Progress value={score} className="h-3 mb-2" />
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {getReadinessMessage()}
        </p>
      </div>

      <div className="text-center">
        <p className="text-sm text-gray-500 mb-2">
          Based on your responses across 6 key areas
        </p>
      </div>
    </Card>
  );
};

/**
 * Individual Score Card Component
 */
export interface IndividualScoreCardProps {
  title: string;
  score: number;
  icon: React.ReactNode;
  description: string;
  insights?: string[];
}

export const IndividualScoreCard: React.FC<IndividualScoreCardProps> = ({
  title,
  score,
  icon,
  description,
  insights = []
}) => {
  return (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          {icon}
          <h3 className="font-medium text-gray-900 dark:text-gray-100">
            {title}
          </h3>
        </div>
        <span className={`text-lg font-semibold ${getScoreColor(score)}`}>
          {score}%
        </span>
      </div>

      <Progress value={score} className="h-2 mb-3" />
      
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
        {description}
      </p>

      {insights.length > 0 && (
        <div className="space-y-1">
          {insights.map((insight, index) => (
            <div key={index} className="flex items-start gap-2">
              <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
              <p className="text-xs text-gray-500">{insight}</p>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
};

/**
 * All Score Cards Component
 */
export const ScoreCards: React.FC<ScoreCardsProps> = ({ scores, readinessLevel }) => {
  const scoreCards = [
    {
      title: 'Risk Tolerance',
      score: scores.riskTolerance,
      icon: <Shield className="h-4 w-4 text-blue-600" />,
      description: 'Your comfort level with career uncertainty and change',
      insights: [
        scores.riskTolerance >= 70 ? 'Comfortable with career risks' : 'Prefer stable career paths',
        scores.riskTolerance >= 50 ? 'Open to new opportunities' : 'Value security and predictability'
      ]
    },
    {
      title: 'Urgency Level',
      score: scores.urgencyLevel,
      icon: <Clock className="h-4 w-4 text-orange-600" />,
      description: 'How quickly you want to make your next career move',
      insights: [
        scores.urgencyLevel >= 70 ? 'Ready for immediate change' : 'Planning for future transition',
        scores.urgencyLevel >= 50 ? 'Actively seeking opportunities' : 'Taking time to prepare'
      ]
    },
    {
      title: 'Skills Confidence',
      score: scores.skillsConfidence,
      icon: <TrendingUp className="h-4 w-4 text-green-600" />,
      description: 'Your confidence in your current skill set',
      insights: [
        scores.skillsConfidence >= 70 ? 'Strong confidence in abilities' : 'Room for skill development',
        scores.skillsConfidence >= 50 ? 'Ready to take on challenges' : 'Focus on building expertise'
      ]
    },
    {
      title: 'Support Level',
      score: scores.supportLevel,
      icon: <Users className="h-4 w-4 text-purple-600" />,
      description: 'Available support from network and resources',
      insights: [
        scores.supportLevel >= 70 ? 'Strong support network' : 'Building professional connections',
        scores.supportLevel >= 50 ? 'Good mentorship access' : 'Expand your professional network'
      ]
    },
    {
      title: 'Financial Readiness',
      score: scores.financialReadiness,
      icon: <DollarSign className="h-4 w-4 text-emerald-600" />,
      description: 'Your financial preparedness for career transition',
      insights: [
        scores.financialReadiness >= 70 ? 'Financially prepared for change' : 'Build financial cushion',
        scores.financialReadiness >= 50 ? 'Adequate emergency funds' : 'Focus on financial planning'
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Overall Readiness Score */}
      <ReadinessScoreCard score={scores.readinessScore} level={readinessLevel} />

      {/* Individual Score Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {scoreCards.map((card, index) => (
          <IndividualScoreCard
            key={index}
            title={card.title}
            score={card.score}
            icon={card.icon}
            description={card.description}
            insights={card.insights}
          />
        ))}
      </div>

      {/* Score Summary */}
      <Card className="p-4 bg-blue-50 dark:bg-blue-900/20">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              Understanding Your Scores
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              These scores reflect your current readiness across key career transition areas. 
              Higher scores indicate stronger preparation, while lower scores highlight areas 
              for focused improvement. Use these insights to create your personalized action plan.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};
