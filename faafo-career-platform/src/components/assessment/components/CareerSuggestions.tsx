/**
 * Career Suggestions Component
 * Displays personalized career path recommendations
 */

import React from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Briefcase, 
  TrendingUp, 
  DollarSign, 
  ExternalLink,
  Star,
  Target,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { useCareerSuggestions } from '../hooks/useAssessmentResults';

export interface CareerSuggestion {
  careerPath: {
    id: string;
    name: string;
    slug: string;
    overview: string;
    pros: string;
    cons: string;
    actionableSteps: any;
  };
  score: number;
  matchReason?: string;
}

export interface CareerSuggestionsProps {
  suggestions: CareerSuggestion[];
}

/**
 * Get match level based on score
 */
export function getMatchLevel(score: number): { level: string; color: string; bgColor: string } {
  if (score >= 85) {
    return { 
      level: 'Excellent Match', 
      color: 'text-green-700 dark:text-green-300', 
      bgColor: 'bg-green-100 dark:bg-green-900/30' 
    };
  }
  if (score >= 70) {
    return { 
      level: 'Good Match', 
      color: 'text-blue-700 dark:text-blue-300', 
      bgColor: 'bg-blue-100 dark:bg-blue-900/30' 
    };
  }
  if (score >= 55) {
    return { 
      level: 'Fair Match', 
      color: 'text-yellow-700 dark:text-yellow-300', 
      bgColor: 'bg-yellow-100 dark:bg-yellow-900/30' 
    };
  }
  return { 
    level: 'Consider', 
    color: 'text-gray-700 dark:text-gray-300', 
    bgColor: 'bg-gray-100 dark:bg-gray-900/30' 
  };
}

/**
 * Individual Career Path Card
 */
export interface CareerPathCardProps {
  suggestion: CareerSuggestion;
  rank: number;
}

export const CareerPathCard: React.FC<CareerPathCardProps> = ({ suggestion, rank }) => {
  const { careerPath, score, matchReason } = suggestion;
  const matchInfo = getMatchLevel(score);

  return (
    <Card className="p-6 hover:shadow-lg transition-all duration-200 relative">
      {/* Rank Badge */}
      <div className="absolute top-4 right-4">
        <Badge variant={rank <= 3 ? 'default' : 'secondary'} className="text-xs">
          #{rank}
        </Badge>
      </div>

      {/* Header */}
      <div className="mb-4 pr-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          {careerPath.name}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
          {careerPath.overview}
        </p>
      </div>

      {/* Match Score */}
      <div className={`p-3 rounded-lg mb-4 ${matchInfo.bgColor}`}>
        <div className="flex items-center justify-between mb-2">
          <span className={`text-sm font-medium ${matchInfo.color}`}>
            {matchInfo.level}
          </span>
          <span className={`text-lg font-bold ${matchInfo.color}`}>
            {score}%
          </span>
        </div>
        <Progress value={score} className="h-2 mb-2" />
        {matchReason && (
          <p className={`text-xs ${matchInfo.color}`}>
            {matchReason}
          </p>
        )}
      </div>

      {/* Pros and Cons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <h4 className="text-sm font-medium text-green-700 dark:text-green-300 mb-2 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Advantages
          </h4>
          <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-3">
            {careerPath.pros}
          </p>
        </div>
        <div>
          <h4 className="text-sm font-medium text-orange-700 dark:text-orange-300 mb-2 flex items-center gap-1">
            <Target className="h-3 w-3" />
            Considerations
          </h4>
          <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-3">
            {careerPath.cons}
          </p>
        </div>
      </div>

      {/* Action Steps Preview */}
      {careerPath.actionableSteps && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-1">
            <ArrowRight className="h-3 w-3" />
            Next Steps
          </h4>
          <div className="space-y-1">
            {careerPath.actionableSteps.slice(0, 2).map((step: string, index: number) => (
              <div key={index} className="flex items-start gap-2">
                <div className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                <p className="text-xs text-gray-600 dark:text-gray-400">{step}</p>
              </div>
            ))}
            {careerPath.actionableSteps.length > 2 && (
              <p className="text-xs text-blue-600 dark:text-blue-400">
                +{careerPath.actionableSteps.length - 2} more steps
              </p>
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center gap-3 text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <DollarSign className="h-3 w-3" />
            <span>Salary Info</span>
          </div>
          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            <span>Growth</span>
          </div>
        </div>
        <Button size="sm" asChild>
          <Link href={`/career-paths/${careerPath.slug}`}>
            Explore Path
            <ExternalLink className="h-3 w-3 ml-1" />
          </Link>
        </Button>
      </div>
    </Card>
  );
};

/**
 * Career Suggestions Filter Controls
 */
export interface FilterControlsProps {
  sortBy: 'score' | 'name';
  setSortBy: (sort: 'score' | 'name') => void;
  filterBy: 'all' | 'high' | 'medium';
  setFilterBy: (filter: 'all' | 'high' | 'medium') => void;
  totalCount: number;
  filteredCount: number;
}

export const FilterControls: React.FC<FilterControlsProps> = ({
  sortBy,
  setSortBy,
  filterBy,
  setFilterBy,
  totalCount,
  filteredCount
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-6">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'score' | 'name')}
            className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
          >
            <option value="score">Match Score</option>
            <option value="name">Career Name</option>
          </select>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">Filter:</span>
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value as 'all' | 'high' | 'medium')}
            className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
          >
            <option value="all">All Matches</option>
            <option value="high">High Match (80%+)</option>
            <option value="medium">Medium Match (60%+)</option>
          </select>
        </div>
      </div>

      <div className="text-sm text-gray-500">
        Showing {filteredCount} of {totalCount} career paths
      </div>
    </div>
  );
};

/**
 * Main Career Suggestions Component
 */
export const CareerSuggestions: React.FC<CareerSuggestionsProps> = ({ suggestions }) => {
  const {
    suggestions: filteredSuggestions,
    sortBy,
    setSortBy,
    filterBy,
    setFilterBy,
    totalCount,
    filteredCount
  } = useCareerSuggestions(suggestions);

  if (suggestions.length === 0) {
    return (
      <Card className="p-8 text-center">
        <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
          No Career Suggestions Available
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Complete your assessment to receive personalized career recommendations.
        </p>
        <Button asChild>
          <Link href="/assessment">Complete Assessment</Link>
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Briefcase className="h-5 w-5 text-blue-600" />
          Personalized Career Suggestions
        </h2>
        <Badge variant="outline" className="text-blue-600 border-blue-600">
          {totalCount} Matches Found
        </Badge>
      </div>

      {/* Info Banner */}
      <Card className="p-4 bg-blue-50 dark:bg-blue-900/20">
        <div className="flex items-start gap-3">
          <Star className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              AI-Powered Career Matching
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              These career paths are ranked based on your skills, interests, goals, and assessment responses. 
              Higher scores indicate better alignment with your profile.
            </p>
          </div>
        </div>
      </Card>

      {/* Filter Controls */}
      <FilterControls
        sortBy={sortBy}
        setSortBy={setSortBy}
        filterBy={filterBy}
        setFilterBy={setFilterBy}
        totalCount={totalCount}
        filteredCount={filteredCount}
      />

      {/* Career Path Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredSuggestions.map((suggestion, index) => (
          <CareerPathCard
            key={suggestion.careerPath.id}
            suggestion={suggestion}
            rank={index + 1}
          />
        ))}
      </div>

      {/* View More */}
      {filteredCount > 6 && (
        <div className="text-center pt-6">
          <Button variant="outline" asChild>
            <Link href="/career-paths">
              Explore All Career Paths
              <ExternalLink className="h-4 w-4 ml-2" />
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
};
