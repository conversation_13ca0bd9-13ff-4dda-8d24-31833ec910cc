/**
 * Assessment Results Data Hook
 * Custom hook for fetching and managing assessment results data
 */

import { useState, useEffect, useCallback } from 'react';
import { getUserFriendlyError } from '@/lib/user-friendly-errors';

export interface AssessmentResultsData {
  assessment: {
    id: string;
    status: string;
    completedAt: Date | null;
    currentStep: number;
  };
  insights: {
    scores: {
      readinessScore: number;
      riskTolerance: number;
      urgencyLevel: number;
      skillsConfidence: number;
      supportLevel: number;
      financialReadiness: number;
    };
    primaryMotivation: string;
    topSkills: string[];
    biggestObstacles: string[];
    recommendedTimeline: string;
    keyRecommendations: string[];
    careerPathSuggestions: string[];
  };
  careerSuggestions: Array<{
    careerPath: {
      id: string;
      name: string;
      slug: string;
      overview: string;
      pros: string;
      cons: string;
      actionableSteps: any;
    };
    score: number;
    matchReason?: string;
  }>;
  personalizedRecommendations?: {
    learningResources: any[];
    skillGaps: any[];
    nextSteps: string[];
  };
}

export interface UseAssessmentResultsReturn {
  results: AssessmentResultsData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  clearError: () => void;
}

export function useAssessmentResults(assessmentId: string): UseAssessmentResultsReturn {
  const [results, setResults] = useState<AssessmentResultsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchResults = useCallback(async () => {
    if (!assessmentId) {
      setError('Assessment ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/assessment/results/${assessmentId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }
      
      const data = await response.json();
      setResults(data.data);
    } catch (err) {
      console.error('Error fetching assessment results:', err);
      const friendlyError = getUserFriendlyError(err, 'assessment');
      setError(friendlyError.message);
    } finally {
      setLoading(false);
    }
  }, [assessmentId]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refetch = useCallback(async () => {
    await fetchResults();
  }, [fetchResults]);

  useEffect(() => {
    fetchResults();
  }, [fetchResults]);

  return {
    results,
    loading,
    error,
    refetch,
    clearError,
  };
}

/**
 * Hook for assessment statistics and derived data
 */
export function useAssessmentStats(results: AssessmentResultsData | null) {
  const stats = {
    readinessLevel: '',
    completionPercentage: 0,
    totalCareerSuggestions: 0,
    totalLearningResources: 0,
    averageCareerMatch: 0,
    topSkillsCount: 0,
    hasPersonalizedRecommendations: false,
  };

  if (!results) {
    return stats;
  }

  // Calculate readiness level
  const readinessScore = results.insights.scores.readinessScore;
  if (readinessScore >= 80) {
    stats.readinessLevel = 'High';
  } else if (readinessScore >= 60) {
    stats.readinessLevel = 'Medium';
  } else {
    stats.readinessLevel = 'Low';
  }

  // Calculate completion percentage based on assessment status
  stats.completionPercentage = results.assessment.status === 'COMPLETED' ? 100 : 
    (results.assessment.currentStep / 10) * 100; // Assuming 10 total steps

  // Career suggestions stats
  stats.totalCareerSuggestions = results.careerSuggestions.length;
  stats.averageCareerMatch = results.careerSuggestions.length > 0 
    ? results.careerSuggestions.reduce((sum, suggestion) => sum + suggestion.score, 0) / results.careerSuggestions.length
    : 0;

  // Skills and recommendations stats
  stats.topSkillsCount = results.insights.topSkills.length;
  stats.totalLearningResources = results.personalizedRecommendations?.learningResources.length || 0;
  stats.hasPersonalizedRecommendations = Boolean(results.personalizedRecommendations);

  return stats;
}

/**
 * Hook for filtering and sorting career suggestions
 */
export function useCareerSuggestions(careerSuggestions: AssessmentResultsData['careerSuggestions']) {
  const [sortBy, setSortBy] = useState<'score' | 'name'>('score');
  const [filterBy, setFilterBy] = useState<'all' | 'high' | 'medium'>('all');

  const filteredAndSortedSuggestions = careerSuggestions
    .filter(suggestion => {
      if (filterBy === 'all') return true;
      if (filterBy === 'high') return suggestion.score >= 80;
      if (filterBy === 'medium') return suggestion.score >= 60 && suggestion.score < 80;
      return true;
    })
    .sort((a, b) => {
      if (sortBy === 'score') {
        return b.score - a.score;
      }
      return a.careerPath.name.localeCompare(b.careerPath.name);
    });

  return {
    suggestions: filteredAndSortedSuggestions,
    sortBy,
    setSortBy,
    filterBy,
    setFilterBy,
    totalCount: careerSuggestions.length,
    filteredCount: filteredAndSortedSuggestions.length,
  };
}

/**
 * Hook for managing learning resources
 */
export function useLearningResources(learningResources: any[] = []) {
  const [activeTab, setActiveTab] = useState<'priority' | 'type' | 'level'>('priority');
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<string | null>(null);

  const resourcesByType = learningResources.reduce((acc, resource) => {
    const type = resource.type || 'OTHER';
    if (!acc[type]) acc[type] = [];
    acc[type].push(resource);
    return acc;
  }, {} as Record<string, any[]>);

  const resourcesByLevel = learningResources.reduce((acc, resource) => {
    const level = resource.skillLevel || 'BEGINNER';
    if (!acc[level]) acc[level] = [];
    acc[level].push(resource);
    return acc;
  }, {} as Record<string, any[]>);

  const priorityResources = learningResources
    .sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0))
    .slice(0, 6);

  const getFilteredResources = () => {
    switch (activeTab) {
      case 'priority':
        return priorityResources;
      case 'type':
        return selectedType ? resourcesByType[selectedType] || [] : learningResources;
      case 'level':
        return selectedLevel ? resourcesByLevel[selectedLevel] || [] : learningResources;
      default:
        return learningResources;
    }
  };

  return {
    activeTab,
    setActiveTab,
    selectedType,
    setSelectedType,
    selectedLevel,
    setSelectedLevel,
    resourcesByType,
    resourcesByLevel,
    priorityResources,
    filteredResources: getFilteredResources(),
    totalResources: learningResources.length,
    availableTypes: Object.keys(resourcesByType),
    availableLevels: Object.keys(resourcesByLevel),
  };
}

/**
 * Hook for assessment actions and navigation
 */
export function useAssessmentActions() {
  const retakeAssessment = useCallback(() => {
    window.location.href = '/assessment';
  }, []);

  const exploreCareerPath = useCallback((slug: string) => {
    window.location.href = `/career-paths/${slug}`;
  }, []);

  const viewResource = useCallback((resourceId: string) => {
    window.location.href = `/resources/${resourceId}`;
  }, []);

  const getSkillsAnalysis = useCallback(() => {
    window.location.href = '/ai/skills-analysis';
  }, []);

  const browseAllResources = useCallback(() => {
    window.location.href = '/resources';
  }, []);

  return {
    retakeAssessment,
    exploreCareerPath,
    viewResource,
    getSkillsAnalysis,
    browseAllResources,
  };
}
