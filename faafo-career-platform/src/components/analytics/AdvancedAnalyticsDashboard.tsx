'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, Download, Users, BookOpen, TrendingUp, MessageSquare, Shield, AlertTriangle } from 'lucide-react';
import { MetricCard, UserMetricCard, LearningMetricCard } from '@/components/analytics/MetricCard';
import { Line<PERSON>hart } from '@/components/analytics/charts/LineChart';
import { Bar<PERSON>hart } from '@/components/analytics/charts/BarChart';
import { PieChart, DonutChart } from '@/components/analytics/charts/PieChart';
import { Alert, AlertDescription } from '@/components/ui/alert';
import useMemoryManagement from '@/hooks/useMemoryManagement';
import { cn } from '@/lib/utils';

interface AnalyticsData {
  userEngagement: any;
  learningProgress: any;
  careerPaths: any;
  community: any;
  generatedAt: string;
  timeRange: string;
  type?: string;
}

interface AdvancedAnalyticsDashboardProps {
  className?: string;
}

export function AdvancedAnalyticsDashboard({ className }: AdvancedAnalyticsDashboardProps) {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('30');
  const [activeTab, setActiveTab] = useState('overview');

  // Memory management hook for cleanup
  const memoryManager = useMemoryManagement();
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchAnalytics = useCallback(async (range: string = timeRange) => {
    try {
      setLoading(true);
      setError(null);

      // Abort any previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      const { controller } = memoryManager.createAbortController();
      abortControllerRef.current = controller;

      const response = await fetch(`/api/analytics/dashboard?range=${range}&metric=all&type=platform`, {
        signal: controller.signal
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch analytics');
      }

      setData(result.data);
    } catch (err) {
      if (err instanceof Error && err instanceof Error ? err.name : String(err) !== 'AbortError') {
        setError(err instanceof Error ? err.message : String(err));
        console.error('Error fetching analytics:', err);
      }
    } finally {
      setLoading(false);
    }
  }, [timeRange, memoryManager]);

  useEffect(() => {
    fetchAnalytics();

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      memoryManager.forceCleanup();
    };
  }, [fetchAnalytics, memoryManager]);

  const handleTimeRangeChange = useCallback((newRange: string) => {
    setTimeRange(newRange);
    fetchAnalytics(newRange);
  }, [fetchAnalytics]);

  const handleRefresh = useCallback(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  const handleExport = () => {
    if (!data) return;
    
    const exportData = {
      ...data,
      exportedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              Platform Analytics Dashboard
            </h1>
            <Shield className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Platform-wide insights for administrators only
          </p>
          <Alert className="mt-3 border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800 dark:text-orange-200">
              This dashboard contains sensitive platform-wide analytics. Admin access required.
            </AlertDescription>
          </Alert>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={handleRefresh} variant="outline" size="sm" disabled={loading}>
            <RefreshCw className={cn('h-4 w-4 mr-2', loading && 'animate-spin')} />
            Refresh
          </Button>
          
          <Button onClick={handleExport} variant="outline" size="sm" disabled={!data}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard
            title="Total Users"
            value={data.userEngagement.totalUsers}
            change={{
              value: 12.5,
              period: 'last month',
              type: 'increase',
            }}
            icon={Users}
            iconColor="text-blue-600"
            loading={loading}
          />
          
          <MetricCard
            title="Active Learners"
            value={data.userEngagement.activeUsers.monthly}
            change={{
              value: 8.2,
              period: 'last month',
              type: 'increase',
            }}
            icon={BookOpen}
            iconColor="text-green-600"
            loading={loading}
          />
          
          <MetricCard
            title="Completion Rate"
            value={`${data.learningProgress.completionRate.toFixed(1)}%`}
            change={{
              value: 3.1,
              period: 'last month',
              type: 'increase',
            }}
            icon={TrendingUp}
            iconColor="text-purple-600"
            loading={loading}
          />
          
          <MetricCard
            title="Community Posts"
            value={data.community.totalPosts}
            change={{
              value: 15.7,
              period: 'last month',
              type: 'increase',
            }}
            icon={MessageSquare}
            iconColor="text-orange-600"
            loading={loading}
          />
        </div>
      )}

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">User Engagement</TabsTrigger>
          <TabsTrigger value="learning">Learning Progress</TabsTrigger>
          <TabsTrigger value="community">Community</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {data && (
            <>
              {/* Engagement Trends */}
              <LineChart
                data={data.userEngagement.engagementTrends}
                xAxisKey="date"
                lines={[
                  { key: 'activeUsers', name: 'Active Users', color: '#8884d8' },
                  { key: 'newUsers', name: 'New Users', color: '#82ca9d' },
                ]}
                title="User Engagement Trends"
                height={300}
              />

              {/* Learning vs Community Activity */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <BarChart
                  data={data.learningProgress.categoryBreakdown}
                  xAxisKey="category"
                  bars={[
                    { key: 'completedResources', name: 'Completed', color: '#8884d8' },
                    { key: 'totalResources', name: 'Total', color: '#82ca9d' },
                  ]}
                  title="Learning by Category"
                  height={300}
                />
                
                <PieChart
                  data={data.community.categoryActivity.map((cat: any) => ({
                    name: cat.categoryName,
                    value: cat.postCount,
                  }))}
                  title="Community Activity Distribution"
                  height={300}
                />
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          {data && (
            <>
              <UserMetricCard
                totalUsers={data.userEngagement.totalUsers}
                activeUsers={data.userEngagement.activeUsers.monthly}
                newUsers={data.userEngagement.newUsers.thisMonth}
                loading={loading}
              />
              
              <LineChart
                data={data.userEngagement.engagementTrends}
                xAxisKey="date"
                lines={[
                  { key: 'activeUsers', name: 'Daily Active Users', color: '#8884d8' },
                  { key: 'newUsers', name: 'New Registrations', color: '#82ca9d' },
                ]}
                title="User Engagement Over Time"
                height={400}
              />
            </>
          )}
        </TabsContent>

        <TabsContent value="learning" className="space-y-6">
          {data && (
            <>
              <LearningMetricCard
                totalResources={data.learningProgress.totalResources}
                completedResources={data.learningProgress.completedResources}
                completionRate={data.learningProgress.completionRate}
                loading={loading}
              />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <LineChart
                  data={data.learningProgress.learningTrends}
                  xAxisKey="date"
                  lines={[
                    { key: 'completions', name: 'Completions', color: '#8884d8' },
                    { key: 'newStarted', name: 'New Started', color: '#82ca9d' },
                  ]}
                  title="Learning Activity Trends"
                  height={300}
                />
                
                <BarChart
                  data={data.learningProgress.popularResources.slice(0, 10)}
                  xAxisKey="title"
                  bars={[
                    { key: 'completions', name: 'Completions', color: '#8884d8' },
                  ]}
                  title="Most Popular Resources"
                  height={300}
                  orientation="horizontal"
                />
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="community" className="space-y-6">
          {data && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <MetricCard
                  title="Total Posts"
                  value={data.community.totalPosts}
                  icon={MessageSquare}
                  iconColor="text-blue-600"
                  loading={loading}
                />
                <MetricCard
                  title="Total Replies"
                  value={data.community.totalReplies}
                  icon={MessageSquare}
                  iconColor="text-green-600"
                  loading={loading}
                />
                <MetricCard
                  title="Engagement Rate"
                  value={`${data.community.engagementRate.toFixed(1)}%`}
                  icon={TrendingUp}
                  iconColor="text-purple-600"
                  loading={loading}
                />
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <LineChart
                  data={data.community.communityTrends}
                  xAxisKey="date"
                  lines={[
                    { key: 'newPosts', name: 'New Posts', color: '#8884d8' },
                    { key: 'newReplies', name: 'New Replies', color: '#82ca9d' },
                  ]}
                  title="Community Activity Trends"
                  height={300}
                />
                
                <BarChart
                  data={data.community.topContributors.slice(0, 10)}
                  xAxisKey="userName"
                  bars={[
                    { key: 'postCount', name: 'Posts', color: '#8884d8' },
                    { key: 'replyCount', name: 'Replies', color: '#82ca9d' },
                  ]}
                  title="Top Contributors"
                  height={300}
                />
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
