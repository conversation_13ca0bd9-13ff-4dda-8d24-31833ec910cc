'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, X } from 'lucide-react';
import { Skill } from '@/components/resume-builder/ResumeBuilder';

interface SkillsFormProps {
  skills: Skill[];
  onChange: (skills: Skill[]) => void;
}

const skillCategories = [
  'Programming Languages',
  'Frameworks & Libraries',
  'Databases',
  'Tools & Technologies',
  'Soft Skills',
  'Languages',
  'Certifications',
  'Other'
];

const skillLevels = [
  { value: 'BEGINNER', label: 'Beginner' },
  { value: 'INTERMEDIATE', label: 'Intermediate' },
  { value: 'ADVANCED', label: 'Advanced' },
  { value: 'EXPERT', label: 'Expert' }
] as const;

export function SkillsForm({ skills, onChange }: SkillsFormProps) {
  const [newSkillName, setNewSkillName] = useState('');
  const [newSkillLevel, setNewSkillLevel] = useState<'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'>('INTERMEDIATE');
  const [newSkillCategory, setNewSkillCategory] = useState('Programming Languages');

  const addSkill = useCallback(() => {
    if (!newSkillName.trim()) return;

    const newSkill: Skill = {
      id: Date.now().toString(),
      name: newSkillName.trim(),
      level: newSkillLevel,
      category: newSkillCategory,
    };

    onChange([...skills, newSkill]);
    setNewSkillName('');
    setNewSkillLevel('INTERMEDIATE');
  }, [newSkillName, newSkillLevel, newSkillCategory, skills, onChange]);

  const removeSkill = useCallback((id: string) => {
    onChange(skills.filter(skill => skill.id !== id));
  }, [skills, onChange]);

  const updateSkill = useCallback((id: string, updates: Partial<Skill>) => {
    onChange(
      skills.map(skill =>
        skill.id === id ? { ...skill, ...updates } : skill
      )
    );
  }, [skills, onChange]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSkill();
    }
  }, [addSkill]);

  // Group skills by category
  const skillsByCategory = skills.reduce((acc, skill) => {
    const category = skill.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(skill);
    return acc;
  }, {} as Record<string, Skill[]>);

  const getLevelColor = useCallback((level?: string) => {
    switch (level) {
      case 'BEGINNER':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'INTERMEDIATE':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ADVANCED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'EXPERT':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Skills</CardTitle>
          <CardDescription>
            Add your technical and soft skills with proficiency levels
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Add New Skill */}
          <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
            <h4 className="font-medium">Add New Skill</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="skillName">Skill Name</Label>
                <Input
                  id="skillName"
                  value={newSkillName}
                  onChange={(e) => setNewSkillName(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="e.g., JavaScript, Leadership, Spanish"
                />
              </div>
              <div>
                <Label htmlFor="skillLevel">Proficiency Level</Label>
                <Select value={newSkillLevel} onValueChange={(value: any) => setNewSkillLevel(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {skillLevels.map(level => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="skillCategory">Category</Label>
                <Select value={newSkillCategory} onValueChange={setNewSkillCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {skillCategories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Button onClick={addSkill} disabled={!newSkillName.trim()}>
              <Plus className="w-4 h-4 mr-2" />
              Add Skill
            </Button>
          </div>

          {/* Skills Display */}
          {skills.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No skills added yet.</p>
              <p className="text-sm">Add your first skill above to get started.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
                <div key={category}>
                  <h4 className="font-medium mb-3 text-sm text-muted-foreground uppercase tracking-wide">
                    {category} ({categorySkills.length})
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {categorySkills.map(skill => (
                      <Card key={skill.id} className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-sm">{skill.name}</h5>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSkill(skill.id)}
                            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                        <div className="space-y-2">
                          <div>
                            <Label htmlFor={`level-${skill.id}`} className="text-xs">Level</Label>
                            <Select
                              value={skill.level || 'INTERMEDIATE'}
                              onValueChange={(value: any) => updateSkill(skill.id, { level: value })}
                            >
                              <SelectTrigger className="h-8 text-xs">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {skillLevels.map(level => (
                                  <SelectItem key={level.value} value={level.value}>
                                    {level.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor={`category-${skill.id}`} className="text-xs">Category</Label>
                            <Select
                              value={skill.category || 'Other'}
                              onValueChange={(value) => updateSkill(skill.id, { category: value })}
                            >
                              <SelectTrigger className="h-8 text-xs">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {skillCategories.map(cat => (
                                  <SelectItem key={cat} value={cat}>
                                    {cat}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Skills Summary */}
          {skills.length > 0 && (
            <div className="p-4 border rounded-lg bg-muted/50">
              <h4 className="font-medium mb-3">Skills Summary</h4>
              <div className="flex flex-wrap gap-2">
                {skills.map(skill => (
                  <Badge
                    key={skill.id}
                    variant="outline"
                    className={getLevelColor(skill.level)}
                  >
                    {skill.name}
                    {skill.level && (
                      <span className="ml-1 text-xs opacity-75">
                        ({skill.level.toLowerCase()})
                      </span>
                    )}
                  </Badge>
                ))}
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                Total: {skills.length} skills across {Object.keys(skillsByCategory).length} categories
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
