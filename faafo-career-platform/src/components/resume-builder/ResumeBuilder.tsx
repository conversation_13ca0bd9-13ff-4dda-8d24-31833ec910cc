'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, Trash2, Save, Download, Eye, FileText } from 'lucide-react';
import { ResumePreview } from '@/components/resume-builder/ResumePreview';
import { PersonalInfoForm } from '@/components/resume-builder/PersonalInfoForm';
import { ExperienceForm } from '@/components/resume-builder/ExperienceForm';
import { EducationForm } from '@/components/resume-builder/EducationForm';
import { SkillsForm } from '@/components/resume-builder/SkillsForm';
import { TextOverflow } from '@/components/ui/text-truncate';

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  website?: string;
  linkedIn?: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  description?: string;
  achievements?: string[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  field?: string;
  startDate?: string;
  endDate?: string;
  gpa?: string;
  honors?: string;
}

export interface Skill {
  id: string;
  name: string;
  level?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  category?: string;
}

export interface Resume {
  id?: string;
  title: string;
  personalInfo: PersonalInfo;
  summary?: string;
  experience: Experience[];
  education: Education[];
  skills: Skill[];
  sections?: Record<string, any>;
  template: string;
  isPublic: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface ResumeBuilderProps {
  resumeId?: string;
  initialMode?: 'edit' | 'preview';
  onSave?: (resume: Resume) => void;
  onCancel?: () => void;
}

export function ResumeBuilder({ resumeId, initialMode = 'edit', onSave, onCancel }: ResumeBuilderProps) {
  const { data: session } = useSession();
  const [resume, setResume] = useState<Resume>({
    title: 'My Resume',
    personalInfo: {
      firstName: '',
      lastName: '',
      email: session?.user?.email || '',
    },
    summary: '',
    experience: [],
    education: [],
    skills: [],
    template: 'modern',
    isPublic: false,
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('personal');
  const [showPreview, setShowPreview] = useState(initialMode === 'preview');
  const [csrfToken, setCsrfToken] = useState<string | null>(null);

  // Fetch CSRF token on component mount
  useEffect(() => {
    const fetchCSRFToken = async () => {
      try {
        const response = await fetch('/api/csrf-token');
        if (response.ok) {
          const data = await response.json();
          setCsrfToken(data.csrfToken);
        }
      } catch (error) {
        console.error('Failed to fetch CSRF token:', error);
      }
    };

    fetchCSRFToken();
  }, []);

  // Load existing resume if resumeId is provided
  useEffect(() => {
    if (resumeId) {
      loadResume(resumeId);
    }
  }, [resumeId]);

  const loadResume = async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/resume-builder/${id}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });

      if (!response.ok) {
        throw new Error('Failed to load resume');
      }

      const data = await response.json();
      if (data.success) {
        setResume(data.data);
      } else {
        throw new Error(data.error || 'Failed to load resume');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  const saveResume = async () => {
    if (!session) {
      setError('You must be logged in to save a resume');
      return;
    }

    if (!csrfToken) {
      setError('Security token not available. Please refresh the page.');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const url = resumeId ? `/api/resume-builder/${resumeId}` : '/api/resume-builder';
      const method = resumeId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
        },
        body: JSON.stringify(resume),
      });

      if (!response.ok) {
        throw new Error('Failed to save resume');
      }

      const data = await response.json();
      if (data.success) {
        setResume(data.data);
        onSave?.(data.data);
      } else {
        throw new Error(data.error || 'Failed to save resume');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setSaving(false);
    }
  };

  const updateResume = (updates: Partial<Resume>) => {
    setResume(prev => ({ ...prev, ...updates }));
  };

  const updatePersonalInfo = (personalInfo: PersonalInfo) => {
    updateResume({ personalInfo });
  };

  const updateExperience = (experience: Experience[]) => {
    updateResume({ experience });
  };

  const updateEducation = (education: Education[]) => {
    updateResume({ education });
  };

  const updateSkills = (skills: Skill[]) => {
    updateResume({ skills });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (showPreview) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Resume Preview</h2>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              <FileText className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button onClick={saveResume} disabled={saving}>
              {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Save className="w-4 h-4 mr-2" />}
              Save
            </Button>
          </div>
        </div>
        <ResumePreview resume={resume} />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Resume Builder</h1>
          <p className="text-muted-foreground">Create and customize your professional resume</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowPreview(true)}>
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button onClick={saveResume} disabled={saving}>
            {saving ? <LoadingSpinner size="sm" className="mr-2" /> : <Save className="w-4 h-4 mr-2" />}
            Save
          </Button>
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Resume Title */}
      <Card>
        <CardHeader>
          <CardTitle>Resume Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Resume Title</Label>
              <Input
                id="title"
                value={resume.title}
                onChange={(e) => updateResume({ title: e.target.value })}
                placeholder="e.g., Software Engineer Resume"
              />
            </div>
            <div>
              <Label htmlFor="template">Template</Label>
              <Select value={resume.template} onValueChange={(value) => updateResume({ template: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="modern">Modern</SelectItem>
                  <SelectItem value="classic">Classic</SelectItem>
                  <SelectItem value="minimal">Minimal</SelectItem>
                  <SelectItem value="creative">Creative</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form Sections */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="personal">Personal</TabsTrigger>
              <TabsTrigger value="experience">Experience</TabsTrigger>
              <TabsTrigger value="education">Education</TabsTrigger>
              <TabsTrigger value="skills">Skills</TabsTrigger>
            </TabsList>

            <TabsContent value="personal" className="space-y-4">
              <PersonalInfoForm
                personalInfo={resume.personalInfo}
                onChange={updatePersonalInfo}
              />
              <Card>
                <CardHeader>
                  <CardTitle>Professional Summary</CardTitle>
                  <CardDescription>
                    Write a brief summary of your professional background and goals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={resume.summary || ''}
                    onChange={(e) => updateResume({ summary: e.target.value })}
                    placeholder="Experienced software engineer with 5+ years of experience in full-stack development..."
                    rows={4}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="experience">
              <ExperienceForm
                experience={resume.experience}
                onChange={updateExperience}
              />
            </TabsContent>

            <TabsContent value="education">
              <EducationForm
                education={resume.education}
                onChange={updateEducation}
              />
            </TabsContent>

            <TabsContent value="skills">
              <SkillsForm
                skills={resume.skills}
                onChange={updateSkills}
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Quick Preview */}
        <div className="lg:col-span-1">
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Quick Preview</CardTitle>
              <CardDescription>See how your resume looks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div>
                  <TextOverflow maxLines={1}>
                    <strong>{resume.personalInfo.firstName} {resume.personalInfo.lastName}</strong>
                  </TextOverflow>
                </div>
                <TextOverflow maxLines={1}>
                  <div className="text-muted-foreground">{resume.personalInfo.email}</div>
                </TextOverflow>
                {resume.summary && (
                  <TextOverflow maxLines={3}>
                    <div className="text-xs text-muted-foreground">
                      {resume.summary}
                    </div>
                  </TextOverflow>
                )}
                <Separator />
                <div className="space-y-1">
                  <div className="text-xs font-medium">Experience: {resume.experience.length} entries</div>
                  <div className="text-xs font-medium">Education: {resume.education.length} entries</div>
                  <div className="text-xs font-medium">Skills: {resume.skills.length} skills</div>
                </div>
                <Separator />
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary">{resume.template}</Badge>
                  {resume.isPublic && <Badge variant="outline">Public</Badge>}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
