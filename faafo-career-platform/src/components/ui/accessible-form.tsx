'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { useFormAccessibility, useAriaLiveRegion } from '@/hooks/useAccessibilityEnhanced';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { AlertCircle, CheckCircle, Info } from 'lucide-react';

export interface AccessibleFieldProps {
  name: string;
  label: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea';
  placeholder?: string;
  description?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  className?: string;
  'data-testid'?: string;
}

/**
 * Accessible form field with comprehensive ARIA support
 */
export function AccessibleField({
  name,
  label,
  type = 'text',
  placeholder,
  description,
  error,
  required = false,
  disabled = false,
  value = '',
  onChange,
  onBlur,
  className,
  'data-testid': testId,
}: AccessibleFieldProps) {
  const { getFieldProps, announceValidation } = useFormAccessibility();
  const [hasBeenTouched, setHasBeenTouched] = React.useState(false);
  const [isValid, setIsValid] = React.useState(!error);

  const fieldProps = getFieldProps(name, error, description);
  const fieldId = fieldProps.id;
  const errorId = `${fieldId}-error`;
  const descriptionId = `${fieldId}-description`;

  const handleChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    onChange?.(newValue);
    
    // Validate on change if field has been touched
    if (hasBeenTouched) {
      const newIsValid = !error;
      if (newIsValid !== isValid) {
        setIsValid(newIsValid);
        announceValidation(newIsValid, label, error);
      }
    }
  }, [onChange, hasBeenTouched, error, isValid, announceValidation, label]);

  const handleBlur = React.useCallback(() => {
    setHasBeenTouched(true);
    onBlur?.();
    
    const newIsValid = !error;
    if (newIsValid !== isValid) {
      setIsValid(newIsValid);
      announceValidation(newIsValid, label, error);
    }
  }, [onBlur, error, isValid, announceValidation, label]);

  const inputProps = {
    ...fieldProps,
    name,
    placeholder,
    value,
    onChange: handleChange,
    onBlur: handleBlur,
    disabled,
    'data-testid': testId,
    className: cn(
      'transition-colors',
      error && hasBeenTouched && 'border-destructive focus-visible:ring-destructive/20',
      isValid && hasBeenTouched && 'border-green-500 focus-visible:ring-green-500/20',
      className
    ),
  };

  return (
    <div className="space-y-2">
      <Label 
        htmlFor={fieldId}
        className={cn(
          'text-sm font-medium',
          error && hasBeenTouched && 'text-destructive',
          required && "after:content-['*'] after:ml-0.5 after:text-destructive"
        )}
      >
        {label}
      </Label>

      {description && (
        <p 
          id={descriptionId}
          className="text-sm text-muted-foreground flex items-start gap-2"
        >
          <Info className="h-4 w-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
          {description}
        </p>
      )}

      {type === 'textarea' ? (
        <Textarea {...inputProps} rows={4} />
      ) : (
        <Input {...inputProps} type={type} />
      )}

      {error && hasBeenTouched && (
        <div 
          id={errorId}
          className="flex items-start gap-2 text-sm text-destructive"
          role="alert"
          aria-live="polite"
        >
          <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" aria-hidden="true" />
          {error}
        </div>
      )}

      {isValid && hasBeenTouched && !error && (
        <div className="flex items-center gap-2 text-sm text-green-600">
          <CheckCircle className="h-4 w-4" aria-hidden="true" />
          <span className="sr-only">{label} is valid</span>
        </div>
      )}
    </div>
  );
}

export interface AccessibleFormProps {
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  className?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'data-testid'?: string;
}

/**
 * Accessible form wrapper with proper ARIA attributes
 */
export function AccessibleForm({
  children,
  onSubmit,
  className,
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledBy,
  'data-testid': testId,
}: AccessibleFormProps) {
  const { createLiveRegion } = useAriaLiveRegion();

  const handleSubmit = React.useCallback((e: React.FormEvent) => {
    e.preventDefault();
    onSubmit?.(e);
  }, [onSubmit]);

  return (
    <>
      <form
        onSubmit={handleSubmit}
        className={cn('space-y-6', className)}
        aria-label={ariaLabel}
        aria-labelledby={ariaLabelledBy}
        data-testid={testId}
        noValidate // We handle validation ourselves for better accessibility
      >
        {children}
      </form>
      {createLiveRegion('polite')}
    </>
  );
}

export interface AccessibleSubmitButtonProps {
  children: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
  loadingText?: string;
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  'data-testid'?: string;
  onClick?: () => void;
}

/**
 * Accessible submit button with loading states
 */
export function AccessibleSubmitButton({
  children,
  isLoading = false,
  disabled = false,
  loadingText = 'Submitting...',
  className,
  variant = 'default',
  'data-testid': testId,
  onClick,
}: AccessibleSubmitButtonProps) {
  return (
    <Button
      type="submit"
      disabled={disabled || isLoading}
      variant={variant}
      className={className}
      data-testid={testId}
      onClick={onClick}
      aria-describedby={isLoading ? 'submit-loading-status' : undefined}
    >
      {isLoading ? (
        <>
          <div 
            className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" 
            aria-hidden="true"
          />
          {loadingText}
          <span id="submit-loading-status" className="sr-only">
            Form is being submitted, please wait
          </span>
        </>
      ) : (
        children
      )}
    </Button>
  );
}

/**
 * Accessible fieldset for grouping related form fields
 */
export interface AccessibleFieldsetProps {
  legend: string;
  children: React.ReactNode;
  className?: string;
  description?: string;
}

export function AccessibleFieldset({
  legend,
  children,
  className,
  description,
}: AccessibleFieldsetProps) {
  const fieldsetId = React.useId();
  const descriptionId = description ? `${fieldsetId}-description` : undefined;

  return (
    <fieldset 
      className={cn('space-y-4 border border-border rounded-lg p-4', className)}
      aria-describedby={descriptionId}
    >
      <legend className="text-sm font-medium px-2 -ml-2">
        {legend}
      </legend>
      
      {description && (
        <p id={descriptionId} className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      
      <div className="space-y-4">
        {children}
      </div>
    </fieldset>
  );
}

/**
 * Hook for form validation with accessibility announcements
 */
export function useAccessibleFormValidation() {
  const { announceError, announceSuccess } = useFormAccessibility();
  const [errors, setErrors] = React.useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const validateField = React.useCallback((name: string, value: string, rules: ValidationRule[]) => {
    for (const rule of rules) {
      const error = rule.validate(value);
      if (error) {
        setErrors(prev => ({ ...prev, [name]: error }));
        announceError(name, error);
        return false;
      }
    }
    
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
    return true;
  }, [announceError]);

  const validateForm = React.useCallback((fields: Record<string, { value: string; rules: ValidationRule[] }>) => {
    let isValid = true;
    const newErrors: Record<string, string> = {};

    Object.entries(fields).forEach(([name, { value, rules }]) => {
      for (const rule of rules) {
        const error = rule.validate(value);
        if (error) {
          newErrors[name] = error;
          isValid = false;
          break;
        }
      }
    });

    setErrors(newErrors);

    if (!isValid) {
      const firstError = Object.entries(newErrors)[0];
      if (firstError) {
        announceError(firstError[0], firstError[1]);
      }
    }

    return isValid;
  }, [announceError]);

  const submitForm = React.useCallback(async (
    submitFn: () => Promise<void>,
    successMessage = 'Form submitted successfully'
  ) => {
    setIsSubmitting(true);
    try {
      await submitFn();
      announceSuccess(successMessage);
      setErrors({});
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      announceError('form', message);
    } finally {
      setIsSubmitting(false);
    }
  }, [announceError, announceSuccess]);

  return {
    errors,
    isSubmitting,
    validateField,
    validateForm,
    submitForm,
  };
}

export interface ValidationRule {
  validate: (value: string) => string | null;
}

export const validationRules = {
  required: (message = 'This field is required'): ValidationRule => ({
    validate: (value) => value.trim() ? null : message,
  }),
  
  email: (message = 'Please enter a valid email address'): ValidationRule => ({
    validate: (value) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value) ? null : message;
    },
  }),
  
  minLength: (min: number, message?: string): ValidationRule => ({
    validate: (value) => {
      return value.length >= min ? null : message || `Must be at least ${min} characters`;
    },
  }),
  
  maxLength: (max: number, message?: string): ValidationRule => ({
    validate: (value) => {
      return value.length <= max ? null : message || `Must be no more than ${max} characters`;
    },
  }),
};
