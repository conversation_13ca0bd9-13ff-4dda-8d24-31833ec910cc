/**
 * Feedback Button Component
 * Provides automatic loading states, error feedback, and success feedback for button actions
 */

'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Clock,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getUserFriendlyError } from '@/lib/user-friendly-errors';
import { QuickErrorRecovery } from '@/components/ui/error-recovery-guide';
import { useRetry } from '@/hooks/useRetry';

type ButtonProps = React.ComponentProps<typeof Button>;

export interface FeedbackButtonProps extends Omit<ButtonProps, 'onClick'> {
  onClick: () => Promise<void> | void;
  loadingText?: string;
  successText?: string;
  errorText?: string;
  showSuccessState?: boolean;
  showErrorState?: boolean;
  successDuration?: number;
  errorDuration?: number;
  retryable?: boolean;
  context?: string; // For user-friendly error mapping
  onSuccess?: () => void;
  onError?: (error: any) => void;
  onRetry?: () => void;
  preventMultipleClicks?: boolean;
  timeout?: number; // Timeout in milliseconds
  maxRetryAttempts?: number; // Maximum retry attempts
  retryDelay?: number; // Delay between retries
}

type ButtonState = 'idle' | 'loading' | 'success' | 'error' | 'timeout';

export function FeedbackButton({
  onClick,
  children,
  loadingText,
  successText,
  errorText,
  showSuccessState = true,
  showErrorState = true,
  successDuration = 2000,
  errorDuration = 5000,
  retryable = true,
  context,
  onSuccess,
  onError,
  onRetry,
  preventMultipleClicks = true,
  timeout,
  maxRetryAttempts = 3,
  retryDelay = 1000,
  className,
  disabled,
  ...buttonProps
}: FeedbackButtonProps) {
  const [state, setState] = useState<ButtonState>('idle');
  const [error, setError] = useState<string | null>(null);

  // Use retry hook for intelligent retry management
  const retry = useRetry(
    async () => {
      await onClick();
    },
    {
      maxAttempts: maxRetryAttempts,
      operationType: 'user',
      config: {
        baseDelay: retryDelay
      },
      onRetry: (attempt, retryError) => {
        setState('loading');
        setError(null);
        onRetry?.();
      },
      onSuccess: () => {
        setState('success');
        onSuccess?.();
        if (showSuccessState && successDuration > 0) {
          setTimeout(() => setState('idle'), successDuration);
        } else {
          setState('idle');
        }
      },
      onFailure: (failureError, attempts) => {
        const friendlyError = getUserFriendlyError(failureError, context);
        setError(friendlyError.message);
        setState('error');
        onError?.(failureError);

        if (showErrorState && errorDuration > 0) {
          setTimeout(() => {
            if (retryable) {
              setState('idle');
            }
          }, errorDuration);
        } else if (retryable) {
          setState('idle');
        }
      }
    }
  );

  const handleClick = useCallback(async () => {
    if (preventMultipleClicks && (state === 'loading' || retry.isRetrying)) {
      return;
    }

    setState('loading');
    setError(null);

    let timeoutId: NodeJS.Timeout | undefined;

    try {
      // Set up timeout if specified
      if (timeout) {
        timeoutId = setTimeout(() => {
          setState('timeout');
          setError('The operation is taking longer than expected. Please try again.');
        }, timeout);
      }

      await retry.execute();

      // Clear timeout if operation completed
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    } catch (err) {
      // Clear timeout if operation failed
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      // Error handling is done by the retry hook
    }
  }, [
    preventMultipleClicks,
    state,
    retry,
    timeout
  ]);

  const handleRetry = useCallback(() => {
    onRetry?.();
    handleClick();
  }, [handleClick, onRetry]);

  const getButtonContent = () => {
    switch (state) {
      case 'loading':
        return (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            {loadingText || 'Loading...'}
          </>
        );
      case 'success':
        return (
          <>
            <CheckCircle className="h-4 w-4 mr-2" />
            {successText || 'Success!'}
          </>
        );
      case 'error':
        return (
          <>
            <AlertCircle className="h-4 w-4 mr-2" />
            {errorText || 'Error'}
          </>
        );
      case 'timeout':
        return (
          <>
            <Clock className="h-4 w-4 mr-2" />
            Timeout
          </>
        );
      default:
        return children;
    }
  };

  const getButtonVariant = () => {
    switch (state) {
      case 'success':
        return 'default';
      case 'error':
      case 'timeout':
        return 'destructive';
      default:
        return buttonProps.variant || 'default';
    }
  };

  const isButtonDisabled = () => {
    if (disabled) return true;
    if (preventMultipleClicks && state === 'loading') return true;
    if (state === 'success' && showSuccessState) return true;
    return false;
  };

  return (
    <div className="space-y-2">
      <div className="relative">
        <Button
          {...buttonProps}
          variant={getButtonVariant()}
          className={cn(
            className,
            state === 'success' && 'bg-green-600 hover:bg-green-700',
            state === 'error' && 'bg-red-600 hover:bg-red-700',
            state === 'timeout' && 'bg-orange-600 hover:bg-orange-700'
          )}
          disabled={isButtonDisabled()}
          onClick={handleClick}
        >
          {getButtonContent()}
        </Button>

        {/* Retry count badge */}
        {retry.retryCount > 0 && (
          <Badge
            variant="secondary"
            className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
          >
            {retry.retryCount}
          </Badge>
        )}
      </div>

      {/* Error feedback with recovery guidance */}
      {error && showErrorState && (
        <div className="space-y-2">
          <Alert variant="destructive" className="text-sm">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{error}</span>
              {retryable && state === 'error' && retry.canRetry && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRetry}
                  className="h-6 px-2 ml-2"
                  disabled={retry.isRetrying}
                >
                  <RefreshCw className={cn("h-3 w-3 mr-1", retry.isRetrying && "animate-spin")} />
                  {retry.isRetrying ? 'Retrying...' : 'Retry'}
                </Button>
              )}
            </AlertDescription>
          </Alert>

          {/* Show recovery guidance for complex errors */}
          <QuickErrorRecovery
            error={error}
            context={context}
            onRetry={retryable ? handleRetry : undefined}
            onRefresh={() => window.location.reload()}
          />
        </div>
      )}

      {/* Timeout feedback */}
      {state === 'timeout' && (
        <Alert variant="destructive" className="text-sm">
          <Clock className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Operation timed out. This might take longer than usual.</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRetry}
              className="h-6 px-2 ml-2"
            >
              <Zap className="h-3 w-3 mr-1" />
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

/**
 * Quick action button for simple operations
 */
export function QuickActionButton({
  onClick,
  children,
  context,
  ...props
}: Omit<FeedbackButtonProps, 'showSuccessState' | 'showErrorState' | 'successDuration' | 'errorDuration'>) {
  return (
    <FeedbackButton
      onClick={onClick}
      context={context}
      showSuccessState={false}
      showErrorState={true}
      errorDuration={3000}
      {...props}
    >
      {children}
    </FeedbackButton>
  );
}

/**
 * Form submit button with enhanced feedback
 */
export function SubmitButton({
  onClick,
  children,
  context,
  ...props
}: Omit<FeedbackButtonProps, 'loadingText' | 'successText' | 'timeout'>) {
  return (
    <FeedbackButton
      onClick={onClick}
      context={context}
      loadingText="Submitting..."
      successText="Submitted!"
      timeout={30000} // 30 second timeout for form submissions
      {...props}
    >
      {children}
    </FeedbackButton>
  );
}

/**
 * API action button for API calls
 */
export function ApiActionButton({
  onClick,
  children,
  context,
  ...props
}: Omit<FeedbackButtonProps, 'timeout' | 'retryable'>) {
  return (
    <FeedbackButton
      onClick={onClick}
      context={context}
      timeout={15000} // 15 second timeout for API calls
      retryable={true}
      {...props}
    >
      {children}
    </FeedbackButton>
  );
}
