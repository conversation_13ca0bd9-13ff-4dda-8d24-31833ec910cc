import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

import { withCSRFProtection } from '@/lib/csrf';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

const prisma = new PrismaClient();

interface CategoryStat {
  category: string;
  count: number;
  totalRating: number;
  totalRatings: number;
  skillLevels: {
    BEGINNER: number;
    INTERMEDIATE: number;
    ADVANCED: number;
    EXPERT: number;
  };
  types: Record<string, number>;
}

interface CategoryStatsResponse {
  category: string;
  count: number;
  averageRating: number;
  totalRatings: number;
  skillLevels: {
    BEGINNER: number;
    INTERMEDIATE: number;
    ADVANCED: number;
    EXPERT: number;
  };
  types: Record<string, number>;
}

interface CategoryCreateResponse {
  error: string;
}

type SkillLevel = 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';

export const GET = withUnifiedErrorHandling(async (): Promise<NextResponse<ApiResponse<CategoryStatsResponse[]>>> => {
    // Get all resources with their ratings
    const resources = await prisma.learningResource.findMany({
      where: {
        isActive: true,
      },
      include: {
        ratings: {
          select: {
            rating: true,
          },
        },
      },
    });

    // Group by category and calculate statistics
    const categoryStats = resources.reduce((acc, resource) => {
      const category = resource.category;

      if (!acc[category]) {
        acc[category] = {
          category,
          count: 0,
          totalRating: 0,
          totalRatings: 0,
          skillLevels: {
            BEGINNER: 0,
            INTERMEDIATE: 0,
            ADVANCED: 0,
            EXPERT: 0,
          },
          types: {},
        };
      }

      // Increment count
      acc[category].count++;

      // Add ratings
      const resourceRatings = resource.ratings;
      if (resourceRatings.length > 0) {
        const avgRating = resourceRatings.reduce((sum, r) => sum + r.rating, 0) / resourceRatings.length;
        acc[category].totalRating += avgRating;
        acc[category].totalRatings += resourceRatings.length;
      }

      // Count skill levels with proper validation
      if (resource.skillLevel) {
        const validSkillLevels: SkillLevel[] = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
        const skillLevel = resource.skillLevel as SkillLevel;
        if (validSkillLevels.includes(skillLevel)) {
          acc[category].skillLevels[skillLevel]++;
        }
      }

      // Count types with proper validation
      if (resource.type && typeof resource.type === 'string') {
        const currentCount = acc[category].types[resource.type] || 0;
        acc[category].types[resource.type] = currentCount + 1;
      }

      return acc;
    }, {} as Record<string, CategoryStat>);

    // Calculate average ratings and format response
    const formattedStats = Object.values(categoryStats).map((stats) => ({
      category: stats.category,
      count: stats.count,
      averageRating: stats.count > 0 ? stats.totalRating / stats.count : 0,
      totalRatings: stats.totalRatings,
      skillLevels: stats.skillLevels,
      types: stats.types,
    }));

  // Sort by count (most resources first)
  formattedStats.sort((a, b) => b.count - a.count);

  return NextResponse.json({
    success: true,
    data: formattedStats,
  });
});

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<CategoryCreateResponse>>> => {
  return withCSRFProtection(request, async (): Promise<NextResponse<ApiResponse<CategoryCreateResponse>>> => {
    // This would be used to create custom categories in the future
    // For now, we're using predefined categories from the enum
    const error = new Error('Custom categories not yet supported') as any;
    error.statusCode = 501;
    throw error;
  }) as Promise<NextResponse<ApiResponse<CategoryCreateResponse>>>;
});
