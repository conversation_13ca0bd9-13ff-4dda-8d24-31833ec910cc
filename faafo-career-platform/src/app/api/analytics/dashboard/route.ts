import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { SecurityValidator } from '@/lib/validation';
import { analyticsService } from '@/lib/analytics-service';
import { authOptions } from '@/lib/auth';
import { getCurrentUserAdminStatus } from '@/lib/auth-utils';
import { personalAnalyticsService } from '@/lib/personal-analytics-service';
import { withCSRFProtection } from '@/lib/csrf';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface AnalyticsDashboardResponse {
  userEngagement?: any;
  learningProgress?: any;
  careerPaths?: any;
  community?: any;
  learning?: any;
  career?: any;
  goals?: any;
  generatedAt: string;
  timeRange?: string;
  type: 'platform' | 'personal';
}

interface EventTrackingResponse {
  message: string;
}

// GET /api/analytics/dashboard - Get analytics data (personal or platform-wide for admins)
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AnalyticsDashboardResponse>>> => {
  const { isAuthenticated, isAdmin, userId } = await getCurrentUserAdminStatus();

  if (!isAuthenticated || !userId) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const { searchParams } = new URL(request.url);
  const range = searchParams.get('range') || '30';
  const metric = searchParams.get('metric') || 'all';
  const type = searchParams.get('type') || 'personal'; // 'personal' or 'platform'

  // Validate and sanitize inputs
  const sanitizedRange = SecurityValidator.sanitizeInput(range);
  const sanitizedMetric = SecurityValidator.sanitizeInput(metric);
  const sanitizedType = SecurityValidator.sanitizeInput(type);

  // Parse range to number with validation
  let days = 30;
  try {
    const parsedRange = parseInt(sanitizedRange);
    if (parsedRange > 0 && parsedRange <= 365) {
      days = parsedRange;
    }
  } catch (error) {
    // Use default value
  }

  let analyticsData;

  // Check if requesting platform analytics
  if (sanitizedType === 'platform') {
    // Require admin access for platform analytics
    if (!isAdmin) {
      const error = new Error('Admin access required for platform analytics') as any;
      error.statusCode = 403;
      error.data = { message: 'You can only view your personal analytics' };
      throw error;
    }

    // Platform analytics (admin only)
    switch (sanitizedMetric) {
      case 'user-engagement':
        analyticsData = {
          userEngagement: await analyticsService.getUserEngagementMetrics(days),
          generatedAt: new Date().toISOString(),
          timeRange: `${days} days`,
          type: 'platform' as const
        };
        break;

      case 'learning-progress':
        analyticsData = {
          learningProgress: await analyticsService.getLearningProgressMetrics(days),
          generatedAt: new Date().toISOString(),
          timeRange: `${days} days`,
          type: 'platform' as const
        };
        break;

      case 'career-paths':
        analyticsData = {
          careerPaths: await analyticsService.getCareerPathMetrics(days),
          generatedAt: new Date().toISOString(),
          timeRange: `${days} days`,
          type: 'platform' as const
        };
        break;

      case 'community':
        analyticsData = {
          community: await analyticsService.getCommunityMetrics(days),
          generatedAt: new Date().toISOString(),
          timeRange: `${days} days`,
          type: 'platform' as const
        };
        break;

      case 'all':
      default:
        const comprehensiveData = await analyticsService.getComprehensiveAnalytics(days);
        analyticsData = {
          ...comprehensiveData,
          type: 'platform' as const
        };
        break;
    }
  } else {
    // Personal analytics (default for all users)
    switch (sanitizedMetric) {
      case 'learning':
        analyticsData = {
          learning: await personalAnalyticsService.getLearningMetrics(userId, days),
          generatedAt: new Date().toISOString(),
          timeRange: `${days} days`,
          type: 'personal' as const
        };
        break;

      case 'career':
        analyticsData = {
          career: await personalAnalyticsService.getCareerMetrics(userId),
          generatedAt: new Date().toISOString(),
          type: 'personal' as const
        };
        break;

      case 'community':
        analyticsData = {
          community: await personalAnalyticsService.getCommunityMetrics(userId, days),
          generatedAt: new Date().toISOString(),
          timeRange: `${days} days`,
          type: 'personal' as const
        };
        break;

      case 'goals':
        analyticsData = {
          goals: await personalAnalyticsService.getGoalsMetrics(userId),
          generatedAt: new Date().toISOString(),
          type: 'personal' as const
        };
        break;

      case 'all':
      default:
        const personalData = await personalAnalyticsService.getComprehensivePersonalAnalytics(userId, days);
        analyticsData = {
          ...personalData,
          type: 'personal' as const
        };
        break;
    }
  }

  return NextResponse.json({
    success: true,
    data: analyticsData,
  });
});

// POST /api/analytics/dashboard - Track custom analytics event
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<EventTrackingResponse>>> => {
  const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const body = await request.json();
    const { eventType, eventData, metadata } = body;

    // Validate inputs
    const sanitizedEventType = SecurityValidator.sanitizeInput(eventType);

    if (!sanitizedEventType) {
      const error = new Error('Event type is required') as any;
      error.statusCode = 400;
      throw error;
    }

    // For now, we'll just log the event
    // In a production system, you might want to store these in a separate analytics table
    console.log('Analytics event tracked:', {
      userId: session.user.id,
      eventType: sanitizedEventType,
      eventData,
      metadata,
      timestamp: new Date().toISOString(),
    });

  return NextResponse.json({
    success: true,
    data: { message: 'Event tracked successfully' }
  });
});
