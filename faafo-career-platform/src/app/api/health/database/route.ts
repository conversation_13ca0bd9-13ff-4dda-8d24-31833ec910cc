import { NextResponse } from 'next/server';

import { prisma, withDatabaseRetry } from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface DatabaseHealthResponse {
  status: 'healthy' | 'unhealthy';
  database: 'connected' | 'disconnected';
  responseTime?: string;
  timestamp: string;
  error?: string;
}

export const GET = withUnifiedErrorHandling(async () => {
  const startTime = Date.now();

  // Test database connection with retry logic
  await withDatabaseRetry(async () => {
    await prisma.$queryRaw`SELECT 1`;
  }, 2, 1000);

  const responseTime = Date.now() - startTime;

  return NextResponse.json({
    success: true,
    data: {
      status: 'healthy',
      database: 'connected',
      responseTime: `${responseTime}ms`,
      timestamp: new Date().toISOString()
    }
  });
});
