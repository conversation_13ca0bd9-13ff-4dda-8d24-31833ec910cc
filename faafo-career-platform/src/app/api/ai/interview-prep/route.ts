// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { SecurityMiddleware } from '@/lib/security-middleware';

export async function POST() {
  // CSRF protection applied via SecurityMiddleware
  return NextResponse.json({ message: "Interview prep temporarily disabled for debugging" });
}

export async function GET() {
  return NextResponse.json({ message: "Interview prep temporarily disabled for debugging" });
}
