import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma'; // Corrected import for default export
import { getCareerPathSuggestions } from '@/lib/suggestionService';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url);
  const assessmentId = searchParams.get('assessmentId');

  if (!assessmentId) {
    const error = new Error('assessmentId is required') as any;
    error.statusCode = 400;
    throw error;
  }

  // Validate that the assessment exists
  const assessment = await prisma.assessment.findUnique({
    where: { id: assessmentId },
  });

  if (!assessment) {
    const error = new Error('Assessment not found') as any;
    error.statusCode = 404;
    throw error;
  }

  const suggestions = await getCareerPathSuggestions(assessmentId);

  // Handle specific error cases
  if (!suggestions) {
    const error = new Error('Failed to fetch career suggestions due to missing related data') as any;
    error.statusCode = 404;
    throw error;
  }

  return NextResponse.json({
    success: true,
    data: suggestions
  });
});