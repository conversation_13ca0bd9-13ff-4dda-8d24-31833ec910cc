import { NextRequest, NextResponse } from 'next/server';

import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface ForumCategoriesResponse {
  id: string;
  name: string;
  description: string;
  postCount: number;
  replyCount: number;
  color: string;
  children: any[];
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ForumCategoriesResponse[]>>> => {
  // For now, return predefined categories since we don't have a categories table
  const categories = [
    {
      id: 'career-advice',
      name: 'Career Advice',
      description: 'General career guidance and tips',
      postCount: 0,
      replyCount: 0,
      color: 'blue',
      children: []
    },
    {
      id: 'job-search',
      name: 'Job Search',
      description: 'Job hunting strategies and experiences',
      postCount: 0,
      replyCount: 0,
      color: 'green',
      children: []
    },
    {
      id: 'skill-development',
      name: 'Skill Development',
      description: 'Learning new skills and technologies',
      postCount: 0,
      replyCount: 0,
      color: 'purple',
      children: []
    },
    {
      id: 'networking',
      name: 'Networking',
      description: 'Building professional connections',
      postCount: 0,
      replyCount: 0,
      color: 'orange',
      children: []
    },
    {
      id: 'career-change',
      name: 'Career Change',
      description: 'Transitioning between careers',
      postCount: 0,
      replyCount: 0,
      color: 'red',
      children: []
    },
    {
      id: 'freelancing',
      name: 'Freelancing',
      description: 'Independent work and consulting',
      postCount: 0,
      replyCount: 0,
      color: 'teal',
      children: []
    }
  ];

  return NextResponse.json({
    success: true,
    data: categories
  });
});
