import { NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';

export const GET = withUnifiedErrorHandling(async () => {
  // Test basic database connection
  const userCount = await prisma.user.count();

  return NextResponse.json({
    success: true,
    data: {
      message: 'Database connection successful',
      userCount,
    }
  });
});
