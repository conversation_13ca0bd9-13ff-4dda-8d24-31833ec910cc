import { NextResponse, NextRequest } from 'next/server';

import prisma from '@/lib/prisma';
import { <PERSON>rrorReporter } from '@/lib/errorReporting';
import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';
import { log } from '@/lib/logger';
import { trackError } from '@/lib/errorTracking';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// Force dynamic rendering to prevent static generation issues
export const dynamic = 'force-dynamic';

import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
import { CacheInvalidationService } from '@/lib/services/cache-invalidation-service';
import { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';
import { sanitizeText, validateSecurity } from '@/lib/input-validation';

// SECURITY FIX: Comprehensive profile data sanitization
function sanitizeProfileData(data: any): {
  sanitizedData: any;
  securityIssues: string[];
} {
  const securityIssues: string[] = [];
  const sanitizedData: any = {};

  // Text fields that need sanitization
  const textFields = [
    'bio', 'firstName', 'lastName', 'jobTitle', 'company',
    'location', 'phoneNumber', 'website', 'currentIndustry',
    'targetIndustry', 'mentorshipInterests', 'professionalGoals',
    'skillsToTeach'
  ];

  // Sanitize text fields
  textFields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null) {
      const fieldValue = String(data[field]);
      const validation = validateSecurity(fieldValue);

      if (!validation.isValid) {
        securityIssues.push(`${field}: ${validation.threats.join(', ')}`);
      }

      // Apply appropriate sanitization based on field type
      if (field === 'bio' || field === 'professionalGoals' || field === 'mentorshipInterests') {
        // Allow limited HTML for rich text fields
        sanitizedData[field] = sanitizeText(validation.sanitized, {
          allowHtml: true,
          maxLength: 2000,
          stripWhitespace: true
        });
      } else if (field === 'website') {
        // Special handling for URLs
        const urlPattern = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
        if (fieldValue && !urlPattern.test(fieldValue)) {
          securityIssues.push(`${field}: Invalid URL format`);
          sanitizedData[field] = '';
        } else {
          sanitizedData[field] = sanitizeText(validation.sanitized, {
            maxLength: 500,
            stripWhitespace: true
          });
        }
      } else {
        // Standard text sanitization
        sanitizedData[field] = sanitizeText(validation.sanitized, {
          maxLength: 200,
          stripWhitespace: true
        });
      }
    }
  });

  // Handle array fields (careerInterests, skillsToLearn)
  const arrayFields = ['careerInterests', 'skillsToLearn'];
  arrayFields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null) {
      if (Array.isArray(data[field])) {
        sanitizedData[field] = data[field]
          .slice(0, 20) // Limit array size
          .map((item: any) => {
            const itemStr = String(item);
            const validation = validateSecurity(itemStr);
            if (!validation.isValid) {
              securityIssues.push(`${field} item: ${validation.threats.join(', ')}`);
            }
            return sanitizeText(validation.sanitized, {
              maxLength: 100,
              stripWhitespace: true
            });
          })
          .filter((item: string) => item.length > 0);
      } else {
        // Handle string format
        const validation = validateSecurity(String(data[field]));
        if (!validation.isValid) {
          securityIssues.push(`${field}: ${validation.threats.join(', ')}`);
        }
        sanitizedData[field] = sanitizeText(validation.sanitized, {
          maxLength: 500,
          stripWhitespace: true
        });
      }
    }
  });

  // Handle boolean fields
  const booleanFields = ['emailNotifications', 'profilePublic', 'showEmail', 'showPhone', 'availabilityForMentoring'];
  booleanFields.forEach(field => {
    if (data[field] !== undefined && data[field] !== null) {
      sanitizedData[field] = Boolean(data[field]);
    }
  });

  // Handle numeric fields
  if (data.weeklyLearningGoal !== undefined && data.weeklyLearningGoal !== null) {
    const goal = Number(data.weeklyLearningGoal);
    if (isNaN(goal) || goal < 0 || goal > 168) { // Max 168 hours per week
      securityIssues.push('weeklyLearningGoal: Invalid value');
      sanitizedData.weeklyLearningGoal = null;
    } else {
      sanitizedData.weeklyLearningGoal = goal;
    }
  }

  // Handle enum fields
  const validExperienceLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
  if (data.experienceLevel && !validExperienceLevels.includes(data.experienceLevel)) {
    securityIssues.push('experienceLevel: Invalid value');
    sanitizedData.experienceLevel = null;
  } else {
    sanitizedData.experienceLevel = data.experienceLevel;
  }

  const validVisibilityOptions = ['PRIVATE', 'COMMUNITY_ONLY', 'PUBLIC'];
  if (data.profileVisibility && !validVisibilityOptions.includes(data.profileVisibility)) {
    securityIssues.push('profileVisibility: Invalid value');
    sanitizedData.profileVisibility = 'COMMUNITY_ONLY';
  } else {
    sanitizedData.profileVisibility = data.profileVisibility || 'COMMUNITY_ONLY';
  }

  // Handle profile picture URL with extra validation
  if (data.profilePictureUrl !== undefined && data.profilePictureUrl !== null) {
    const url = String(data.profilePictureUrl);
    const validation = validateSecurity(url);

    if (!validation.isValid) {
      securityIssues.push(`profilePictureUrl: ${validation.threats.join(', ')}`);
    }

    // Additional URL validation for profile pictures
    const imageUrlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i;
    if (url && !imageUrlPattern.test(url)) {
      securityIssues.push('profilePictureUrl: Invalid image URL format');
      sanitizedData.profilePictureUrl = null;
    } else {
      sanitizedData.profilePictureUrl = sanitizeText(validation.sanitized, {
        maxLength: 500,
        stripWhitespace: true
      });
    }
  }

  // Handle social media links object
  if (data.socialMediaLinks !== undefined && data.socialMediaLinks !== null) {
    if (typeof data.socialMediaLinks === 'object') {
      const sanitizedLinks: any = {};
      const allowedPlatforms = ['linkedin', 'twitter', 'github', 'portfolio', 'website'];

      Object.keys(data.socialMediaLinks).forEach(platform => {
        if (allowedPlatforms.includes(platform.toLowerCase())) {
          const url = String(data.socialMediaLinks[platform]);
          const validation = validateSecurity(url);

          if (!validation.isValid) {
            securityIssues.push(`socialMediaLinks.${platform}: ${validation.threats.join(', ')}`);
          }

          const urlPattern = /^https?:\/\/.+/;
          if (url && !urlPattern.test(url)) {
            securityIssues.push(`socialMediaLinks.${platform}: Invalid URL format`);
          } else {
            sanitizedLinks[platform] = sanitizeText(validation.sanitized, {
              maxLength: 500,
              stripWhitespace: true
            });
          }
        }
      });
      sanitizedData.socialMediaLinks = sanitizedLinks;
    }
  }

  return { sanitizedData, securityIssues };
}

// Helper function to calculate profile completion score
function calculateProfileCompletionScore(profileData: any): number {
  const fields = [
    'bio',
    'profilePictureUrl',
    'firstName',
    'lastName',
    'jobTitle',
    'company',
    'location',
    'phoneNumber',
    'website',
    'careerInterests',
    'skillsToLearn',
    'experienceLevel',
    'currentIndustry',
    'targetIndustry',
    'weeklyLearningGoal'
  ];

  let completedFields = 0;

  fields.forEach(field => {
    const value = profileData[field];
    if (value !== null && value !== undefined && value !== '') {
      if (Array.isArray(value) && value.length > 0) {
        completedFields++;
      } else if (typeof value === 'string' && value.trim().length > 0) {
        completedFields++;
      } else if (typeof value === 'number' && value > 0) {
        completedFields++;
      } else if (typeof value !== 'string' && typeof value !== 'number' && value) {
        completedFields++;
      }
    }
  });

  return Math.round((completedFields / fields.length) * 100);
}

interface ProfileResponse {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  bio?: string | null;
  profilePictureUrl?: string | null;
  socialMediaLinks?: any;
  firstName?: string | null;
  lastName?: string | null;
  jobTitle?: string | null;
  company?: string | null;
  location?: string | null;
  phoneNumber?: string | null;
  website?: string | null;
  careerInterests?: any;
  skillsToLearn?: any;
  experienceLevel?: any;
  currentIndustry?: string | null;
  targetIndustry?: string | null;
  weeklyLearningGoal?: number | null;
  emailNotifications?: boolean;
  profileVisibility?: string;
  profilePublic?: boolean;
  showEmail?: boolean;
  showPhone?: boolean;
  profileCompletionScore?: number;
  lastProfileUpdate?: Date | null;
  currentCareerPath?: string | null;
  progressLevel?: string | null;
  lastActiveAt?: Date;
  achievements?: any;
  learningStreak?: number | null;
  totalLearningHours?: number | null;
  preferredLearningStyle?: string | null;
  availabilityForMentoring?: boolean;
  mentorshipInterests?: string | null;
  professionalGoals?: string | null;
  skillsToTeach?: string | null;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ProfileResponse>>> => {
  const startTime = Date.now();

  // Enhanced authentication using unified service
  const validation = await UnifiedAuthenticationService.validateSession(request, {
    validateUserExists: true,
    checkAccountLock: true,
    refreshSession: true,
    enableSecurityLogging: true,
    sessionType: 'general'
  });

  if (!validation.isValid) {
    log.auth('profile_access_denied', undefined, false, {
      component: 'profile_api',
      action: 'fetch_profile'
    });
    const error = new Error(validation.error);
    (error as any).statusCode = validation.statusCode || 401;
    throw error;
  }

  log.info('Fetching user profile', {
    component: 'profile_api',
    action: 'fetch_profile',
    userId: validation.userId
  });

  const dbStartTime = Date.now();
  const user = await prisma.user.findUnique({
    where: { id: validation.userId! },
    include: { profile: true },
  });
  const dbDuration = Date.now() - dbStartTime;

  log.database('findUnique', 'user', dbDuration, {
    userId: validation.userId
  });

  if (!user) {
    log.warn('User not found during profile fetch', {
      component: 'profile_api',
      userId: validation.userId
    });
    const error = new Error('User not found') as any;
    error.statusCode = 404;
    throw error;
  }

  // If profile doesn't exist, create an empty one
  let profile = user.profile;
  if (!profile) {
    log.info('Creating new profile for user', {
      component: 'profile_api',
      userId: user.id
    });

    const createStartTime = Date.now();
    profile = await prisma.profile.create({
      data: {
        userId: user.id,
      },
    });
    const createDuration = Date.now() - createStartTime;

    log.database('create', 'profile', createDuration, {
      userId: user.id
    });
  }

  const totalDuration = Date.now() - startTime;
  log.api('GET', '/api/profile', 200, totalDuration, {
    component: 'profile_api',
    userId: validation.userId
  });

  return NextResponse.json({ success: true, data: profile });
});

interface ProfileUpdateResponse {
  id: string;
  userId: string;
  bio?: string | null;
  profilePictureUrl?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  jobTitle?: string | null;
  company?: string | null;
  location?: string | null;
  phoneNumber?: string | null;
  website?: string | null;
  careerInterests?: string | null;
  skillsToLearn?: string | null;
  experienceLevel?: string | null;
  currentIndustry?: string | null;
  targetIndustry?: string | null;
  weeklyLearningGoal?: number | null;
  emailNotifications?: boolean;
  profileVisibility?: string;
  profilePublic?: boolean;
  showEmail?: boolean;
  showPhone?: boolean;
  profileCompletionScore?: number;
  lastProfileUpdate?: Date;
  currentCareerPath?: string | null;
  progressLevel?: string | null;
  lastActiveAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export const PUT = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 30 },
      async () => {
        // Enhanced authentication using unified service
        const validation = await UnifiedAuthenticationService.validateSession(request, {
          validateUserExists: true,
          checkAccountLock: true,
          refreshSession: true,
          enableSecurityLogging: true,
          sessionType: 'general'
        });

        if (!validation.isValid) {
          const error = new Error(validation.error);
          (error as any).statusCode = validation.statusCode || 401;
          throw error;
        }

        const user = await prisma.user.findUnique({
          where: { id: validation.userId! },
        });

        if (!user) {
          const error = new Error('User not found') as any;
          error.statusCode = 404;
          throw error;
        }

        const rawData = await request.json();

        // SECURITY FIX: Sanitize and validate all profile data
        const { sanitizedData, securityIssues } = sanitizeProfileData(rawData);

        // If there are security issues, log them and potentially reject the request
        if (securityIssues.length > 0) {
          log.warn('Profile update security issues detected', {
            component: 'profile_api',
            userId: user.id,
            metadata: { securityIssues }
          });

          // For now, we'll sanitize and continue, but in production you might want to reject
          // Uncomment the following lines to reject requests with security issues:
          // const error = new Error('Profile data contains security violations') as any;
          // error.statusCode = 400;
          // error.data = { securityIssues };
          // throw error;
        }

        const {
          bio,
          profilePictureUrl,
          socialMediaLinks,
          firstName,
          lastName,
          jobTitle,
          company,
          location,
          phoneNumber,
          website,
          careerInterests,
          skillsToLearn,
          experienceLevel,
          currentIndustry,
          targetIndustry,
          weeklyLearningGoal,
          emailNotifications,
          profileVisibility,
          profilePublic,
          showEmail,
          showPhone
        } = sanitizedData;

        // Calculate profile completion score
        const profileCompletionScore = calculateProfileCompletionScore({
          bio,
          profilePictureUrl,
          firstName,
          lastName,
          jobTitle,
          company,
          location,
          phoneNumber,
          website,
          careerInterests,
          skillsToLearn,
          experienceLevel,
          currentIndustry,
          targetIndustry,
          weeklyLearningGoal
        });

        const updatedProfile = await prisma.profile.upsert({
          where: { userId: user.id },
          update: {
            bio,
            profilePictureUrl,
            socialMediaLinks,
            firstName,
            lastName,
            jobTitle,
            company,
            location,
            phoneNumber,
            website,
            careerInterests: careerInterests ? JSON.stringify(careerInterests) : undefined,
            skillsToLearn: skillsToLearn ? JSON.stringify(skillsToLearn) : undefined,
            experienceLevel,
            currentIndustry,
            targetIndustry,
            weeklyLearningGoal,
            emailNotifications: emailNotifications ?? true,
            profileVisibility: profileVisibility || 'COMMUNITY_ONLY',
            profilePublic: profilePublic ?? false,
            showEmail: showEmail ?? false,
            showPhone: showPhone ?? false,
            profileCompletionScore,
            lastProfileUpdate: new Date(),
            currentCareerPath: Array.isArray(careerInterests) ? careerInterests.join(', ') : careerInterests,
            progressLevel: experienceLevel,
            lastActiveAt: new Date(),
          },
          create: {
            userId: user.id,
            bio,
            profilePictureUrl,
            socialMediaLinks,
            firstName,
            lastName,
            jobTitle,
            company,
            location,
            phoneNumber,
            website,
            careerInterests: careerInterests ? JSON.stringify(careerInterests) : undefined,
            skillsToLearn: skillsToLearn ? JSON.stringify(skillsToLearn) : undefined,
            experienceLevel,
            currentIndustry,
            targetIndustry,
            weeklyLearningGoal,
            emailNotifications: emailNotifications ?? true,
            profileVisibility: profileVisibility || 'COMMUNITY_ONLY',
            profilePublic: profilePublic ?? false,
            showEmail: showEmail ?? false,
            showPhone: showPhone ?? false,
            profileCompletionScore,
            lastProfileUpdate: new Date(),
            currentCareerPath: Array.isArray(careerInterests) ? careerInterests.join(', ') : careerInterests,
            progressLevel: experienceLevel,
          },
        });

        // INTEGRATION FIX: Invalidate profile-related caches using new CacheInvalidationService
        const cacheService = new ConsolidatedCacheService();
        const cacheInvalidationService = new CacheInvalidationService(cacheService);

        try {
          await cacheInvalidationService.invalidateProfileCaches(user.id);
        } catch (cacheError) {
          // Log cache invalidation errors but don't fail the request
          log.warn('Failed to invalidate profile caches', {
            component: 'profile_api',
            userId: user.id,
            metadata: { error: cacheError }
          });
        }

        return NextResponse.json({ success: true, data: updatedProfile });
      }
    );
  });
});
