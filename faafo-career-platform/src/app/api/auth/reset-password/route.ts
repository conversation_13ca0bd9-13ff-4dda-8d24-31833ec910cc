import bcrypt from 'bcryptjs';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';
import { validatePasswordStrength } from '@/lib/rateLimit';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// Timing attack protection - consistent delay for all responses
const SECURITY_DELAY_MS = 100;

async function securityDelay(): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, SECURITY_DELAY_MS));
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> => {
  // SECURITY FIX: Apply strict rate limiting for password reset attempts
  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);

  if (!rateLimitResult.allowed) {
    const error = new Error('Too many password reset attempts. Please try again later.') as any;
    error.statusCode = 429;
    error.headers = rateLimitResult.headers;
    throw error;
  }

  const startTime = Date.now();

  try {
    const { token, password } = await request.json();

    if (!token || !password) {
      await securityDelay();
      throw new Error('Token and new password are required.');
    }

    // SECURITY FIX: Validate password strength
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      await securityDelay();
      const error = new Error('Password does not meet security requirements.') as any;
      error.statusCode = 400;
      error.data = { details: passwordValidation.errors };
      throw error;
    }

    // SECURITY FIX: Always perform database lookup and token comparison to prevent timing attacks
    const user = await prisma.user.findFirst({
      where: {
        passwordResetExpires: { gt: new Date() }, // Token must not be expired
      },
    });

    let isTokenValid = false;
    let hashedPassword = '';

    // Always perform bcrypt comparison even if user is null (timing attack protection)
    if (user?.passwordResetToken) {
      isTokenValid = await bcrypt.compare(token, user.passwordResetToken);
    } else {
      // Perform dummy bcrypt operation to maintain consistent timing
      await bcrypt.compare(token, '$2a$12$dummy.hash.to.prevent.timing.attacks.abcdefghijklmnopqrstuvwxyz');
    }

    // SECURITY FIX: Ensure consistent response time regardless of token validity
    const elapsedTime = Date.now() - startTime;
    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
    if (remainingDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingDelay));
    }

    if (!user || !isTokenValid) {
      throw new Error('Invalid or expired password reset token.');
    }

    hashedPassword = await bcrypt.hash(password, 12);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
      },
    });

    return NextResponse.json({
      success: true,
      data: { message: 'Your password has been reset successfully.' }
    });

  } catch (error) {
    // SECURITY FIX: Ensure consistent timing even for errors
    const elapsedTime = Date.now() - startTime;
    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
    if (remainingDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingDelay));
    }
    throw error;
  }
});