import bcrypt from 'bcryptjs';
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

import prisma from '@/lib/prisma';
import { CONFIG } from '@/lib/config';
import { SimpleSecurity } from '@/lib/simple-security';
import { sendPasswordResetEmail } from '@/lib/email';
import { validateInput, emailSchema } from '@/lib/validation';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface ForgotPasswordResponse {
  message: string;
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ForgotPasswordResponse>>> => {
  // Apply rate limiting
  const rateLimitResult = withRateLimit(rateLimiters.passwordReset)(request);
  if (!rateLimitResult.allowed) {
    const error = new Error('Too many password reset requests') as any;
    error.statusCode = 429;
    throw error;
  }

  const body = await request.json();

  // Enhanced email validation with security checks
  const emailValidation = SimpleSecurity.validateEmail(body.email);
  if (!emailValidation.isValid) {
    const error = new Error(emailValidation.message || 'Invalid email format') as any;
    error.statusCode = 400;
    throw error;
  }

  // Additional legacy validation for compatibility
  const validation = validateInput(emailSchema, body.email);
  if (!validation.success) {
    const error = new Error('Invalid email format') as any;
    error.statusCode = 400;
    throw error;
  }

  const { email } = validation.data;

  const user = await prisma.user.findUnique({ where: { email } });

  if (!user) {
    // For security reasons, don't reveal if the user exists or not.
    return NextResponse.json({
      success: true,
      data: {
        message: 'If an account with that email exists, a password reset link has been sent.'
      }
    });
  }

  const resetToken = uuidv4();
  const passwordResetToken = await bcrypt.hash(resetToken, CONFIG.SECURITY.BCRYPT_ROUNDS);
  const passwordResetExpires = new Date(Date.now() + CONFIG.AUTH.PASSWORD_RESET_EXPIRY_MS);

  await prisma.user.update({
    where: { id: user.id },
    data: {
      passwordResetToken,
      passwordResetExpires,
    },
  });

  const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`;

  await sendPasswordResetEmail({ to: email, url: resetUrl });

  return NextResponse.json({
    success: true,
    data: {
      message: 'If an account with that email exists, a password reset link has been sent.'
    }
  });
});