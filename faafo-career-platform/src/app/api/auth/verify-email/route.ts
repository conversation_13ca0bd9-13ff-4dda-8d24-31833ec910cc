import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// Timing attack protection - consistent delay for all responses
const SECURITY_DELAY_MS = 50;

async function securityDelay(): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, SECURITY_DELAY_MS));
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> => {
  // SECURITY FIX: Apply strict rate limiting for email verification attempts
  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);

  if (!rateLimitResult.allowed) {
    const error = new Error('Too many email verification attempts. Please try again later.') as any;
    error.statusCode = 429;
    error.headers = rateLimitResult.headers;
    throw error;
  }

  const startTime = Date.now();

  try {
    const { token, email } = await request.json();

    if (!token || !email) {
      await securityDelay();
      throw new Error('Token and email are required.');
    }

    // SECURITY FIX: Always perform database lookup to prevent timing attacks
    const verificationToken = await prisma.verificationToken.findUnique({
      where: {
        token: token,
      },
    });

    // SECURITY FIX: Ensure consistent response time regardless of token validity
    const elapsedTime = Date.now() - startTime;
    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
    if (remainingDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingDelay));
    }

    if (!verificationToken) {
      throw new Error('Invalid verification token.');
    }

    // Check if token has expired
    if (verificationToken.expires < new Date()) {
      // Clean up expired token
      await prisma.verificationToken.delete({
        where: { token: token },
      });
      throw new Error('Verification token has expired.');
    }

    // Check if the email matches
    if (verificationToken.identifier !== email) {
      throw new Error('Invalid verification token.');
    }

    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: email },
    });

    if (!user) {
      throw new Error('User not found.');
    }

    // Check if user is already verified
    if (user.emailVerified) {
      // Clean up the token
      await prisma.verificationToken.delete({
        where: { token: token },
      });
      return NextResponse.json({
        success: true,
        data: { message: 'Email is already verified.' }
      });
    }

    // Update user as verified and clean up token
    await prisma.$transaction([
      prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerified: new Date(),
        },
      }),
      prisma.verificationToken.delete({
        where: { token: token },
      }),
    ]);

    return NextResponse.json({
      success: true,
      data: { message: 'Email verified successfully.' }
    });

  } catch (error) {
    // SECURITY FIX: Ensure consistent timing even for errors
    const elapsedTime = Date.now() - startTime;
    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
    if (remainingDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingDelay));
    }
    throw error;
  }
});

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ valid: boolean; email: string; alreadyVerified: boolean }>>> => {
  // SECURITY FIX: Apply rate limiting to GET method as well
  const rateLimitResult = await enhancedRateLimiters.auth.checkLimit(request);

  if (!rateLimitResult.allowed) {
    const error = new Error('Too many email verification check attempts. Please try again later.') as any;
    error.statusCode = 429;
    error.headers = rateLimitResult.headers;
    throw error;
  }

  const startTime = Date.now();

  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const email = searchParams.get('email');

    if (!token || !email) {
      await securityDelay();
      throw new Error('Token and email are required.');
    }

    // SECURITY FIX: Always perform database lookup to prevent timing attacks
    const verificationToken = await prisma.verificationToken.findUnique({
      where: {
        token: token,
      },
    });

    // SECURITY FIX: Ensure consistent response time
    const elapsedTime = Date.now() - startTime;
    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
    if (remainingDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingDelay));
    }

    if (!verificationToken) {
      throw new Error('Invalid verification token.');
    }

    // Check if token has expired
    if (verificationToken.expires < new Date()) {
      throw new Error('Verification token has expired.');
    }

    // Check if the email matches
    if (verificationToken.identifier !== email) {
      throw new Error('Invalid verification token.');
    }

    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: email },
    });

    if (!user) {
      throw new Error('User not found.');
    }

    return NextResponse.json({
      success: true,
      data: {
        valid: true,
        email: email,
        alreadyVerified: !!user.emailVerified
      }
    });

  } catch (error) {
    // SECURITY FIX: Ensure consistent timing even for errors
    const elapsedTime = Date.now() - startTime;
    const remainingDelay = Math.max(0, SECURITY_DELAY_MS - elapsedTime);
    if (remainingDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingDelay));
    }
    throw error;
  }
});
