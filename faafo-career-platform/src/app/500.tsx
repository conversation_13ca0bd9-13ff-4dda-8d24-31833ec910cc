import Link from 'next/link';
export default function Custom500() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-lg shadow-xl p-8 text-center">
          <div className="text-6xl font-bold text-red-500 mb-4">500</div>
          <h1 className="text-2xl font-semibold text-gray-800 mb-4">
            Server Error
          </h1>
          <p className="text-gray-600 mb-6">
            We apologize for the inconvenience. A server error occurred.
          </p>
          <Link
            href="/"
            className="inline-block bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
          >
            Go Home
          </Link>
        </div>
      </div>
    </div>
  );
}
