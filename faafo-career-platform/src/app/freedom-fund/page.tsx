'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import FreedomFundCalculatorForm from '@/components/freedom-fund/FreedomFundCalculatorForm';
import FreedomFundResults from '@/components/freedom-fund/FreedomFundResults';
import { useCSRF } from '@/hooks/useCSRF';
import { FormLoadingState, PageErrorState } from '@/components/ui/page-loading-states';
import { ErrorBoundary } from '@/components/unified-error-boundary';
import PageLayout from '@/components/layout/PageLayout';

interface FreedomFundFormData {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavings?: number; // This is from the form
  monthlyContribution?: number;
  adjustForInflation?: boolean;
}

// Represents the data structure from/to the backend API
interface FreedomFundData extends FreedomFundFormData {
  id: string;
  userId: string;
  targetSavings: number;
  currentSavingsAmount?: number; // This is specifically for the DB model field name
  createdAt: string;
  updatedAt: string;
}

// API interaction functions
const fetchFreedomFundAPI = async (): Promise<FreedomFundData | null> => {
  try {
    const response = await fetch('/api/freedom-fund');
    if (response.status === 404) {
      return null;
    }
    if (!response.ok) {
      let errorMessage = 'Failed to load Freedom Fund data';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch (parseError) {
        console.warn('Could not parse error response:', parseError);
      }
      throw new Error(errorMessage);
    }
    const data = await response.json();
    return data.data || data; // Handle both unified API response format and direct data
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Network error while loading Freedom Fund data');
  }
};

const saveFreedomFundAPI = async (formData: FreedomFundFormData, getHeaders: () => Record<string, string>): Promise<FreedomFundData> => {
  try {
    // Calculate target with inflation adjustment if needed
    const baseTarget = formData.monthlyExpenses * formData.coverageMonths;
    const inflationRate = 0.03; // 3% annual inflation
    const targetSavings = formData.adjustForInflation ? baseTarget * (1 + inflationRate) : baseTarget;

    const response = await fetch('/api/freedom-fund', {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({
        monthlyExpenses: formData.monthlyExpenses,
        coverageMonths: formData.coverageMonths,
        currentSavingsAmount: formData.currentSavings, // Map form field to API field
        targetSavings, // Send calculated target
        monthlyContribution: formData.monthlyContribution,
        adjustForInflation: formData.adjustForInflation,
      }),
    });

    if (!response.ok) {
      let errorMessage = 'Failed to save Freedom Fund data';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch (parseError) {
        console.warn('Could not parse error response:', parseError);
      }
      throw new Error(errorMessage);
    }

    const responseData = await response.json();
    return responseData.data || responseData; // Handle both unified API response format and direct data
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Network error while saving Freedom Fund data');
  }
};

function FreedomFundPageContent() {
  const { status: sessionStatus } = useSession();
  const router = useRouter();
  const { getHeaders, isLoading: csrfLoading } = useCSRF();

  const [targetAmount, setTargetAmount] = useState<number | null>(null);
  const [currentSavingsDisplay, setCurrentSavingsDisplay] = useState<number | null>(null);
  const [monthlyContribution, setMonthlyContribution] = useState<number | null>(null);
  const [monthlyExpenses, setMonthlyExpenses] = useState<number | null>(null);
  const [initialFormData, setInitialFormData] = useState<Partial<FreedomFundFormData> | undefined>(undefined);

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (sessionStatus === 'authenticated') {
      setIsLoading(true);
      setError(null);
      fetchFreedomFundAPI()
        .then(data => {
          if (data) {
            setTargetAmount(data.targetSavings);
            setCurrentSavingsDisplay(data.currentSavingsAmount ?? null);
            setMonthlyExpenses(data.monthlyExpenses);
            // Note: monthlyContribution might not be in the API response yet
            setInitialFormData({
              monthlyExpenses: data.monthlyExpenses,
              coverageMonths: data.coverageMonths,
              currentSavings: data.currentSavingsAmount,
              // Add new fields when they're available in the API
              monthlyContribution: undefined, // Will be added when API is updated
              adjustForInflation: false, // Default value
            });
          } else {
            // No existing data, clear any previous state
            setTargetAmount(null);
            setCurrentSavingsDisplay(null);
            setMonthlyContribution(null);
            setMonthlyExpenses(null);
            setInitialFormData(undefined);
          }
        })
        .catch(err => {
          console.error('Error fetching Freedom Fund data:', err);
          setError(err.message || 'Could not load your saved data.');
          setTargetAmount(null); // Clear stale data on error
          setCurrentSavingsDisplay(null);
          setMonthlyContribution(null);
          setMonthlyExpenses(null);
          setInitialFormData(undefined);
        })
        .finally(() => setIsLoading(false));
    } else if (sessionStatus === 'unauthenticated') {
      router.push('/login');
    }
  }, [sessionStatus, router]);

  const handleCalculateAndSave = useCallback(async (formData: FreedomFundFormData) => {
    setIsSaving(true);
    setError(null);
    try {
      const savedData = await saveFreedomFundAPI(formData, getHeaders);
      setTargetAmount(savedData.targetSavings);
      setCurrentSavingsDisplay(savedData.currentSavingsAmount ?? null);
      setMonthlyContribution(formData.monthlyContribution ?? null);
      setMonthlyExpenses(savedData.monthlyExpenses);
      setInitialFormData({ // Update initial form data for consistency if form is re-rendered
        monthlyExpenses: savedData.monthlyExpenses,
        coverageMonths: savedData.coverageMonths,
        currentSavings: savedData.currentSavingsAmount,
        monthlyContribution: formData.monthlyContribution,
        adjustForInflation: formData.adjustForInflation,
      });
      // Replace alert with a better notification system in the future
      alert('Freedom Fund data saved successfully!');
    } catch (apiError) {
      console.error('API Error saving Freedom Fund data:', apiError);
      const message = apiError instanceof Error ? apiError.message : String(apiError);
      setError(message);
      alert(`Error: ${message}`);
    }
    setIsSaving(false);
  }, [getHeaders]);

  if (sessionStatus === 'loading' || (sessionStatus === 'authenticated' && isLoading)) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Freedom Fund Calculator
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Build your emergency savings strategy and track your progress towards complete financial security and peace of mind.
          </p>
        </div>
        <FormLoadingState message="Loading your Freedom Fund data..." />
      </div>
    );
  }
  if (sessionStatus === 'unauthenticated') {
     // Should have been redirected by useEffect, but as a fallback:
    return <p className="text-center py-10">Please <a href="/login" className="underline">login</a> to use the Freedom Fund calculator.</p>;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Freedom Fund Calculator
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Build your emergency savings strategy and track your progress towards complete financial security and peace of mind.
        </p>
      </div>

      {error && (
        <div className="mb-6">
          <PageErrorState
            title="Failed to Load Freedom Fund Data"
            message={error}
            onRetry={() => {
              setError(null);
              setIsLoading(true);
              fetchFreedomFundAPI()
                .then(data => {
                  if (data) {
                    setTargetAmount(data.targetSavings);
                    setCurrentSavingsDisplay(data.currentSavingsAmount ?? null);
                    setMonthlyExpenses(data.monthlyExpenses);
                    setInitialFormData({
                      monthlyExpenses: data.monthlyExpenses,
                      coverageMonths: data.coverageMonths,
                      currentSavings: data.currentSavingsAmount,
                      monthlyContribution: undefined,
                      adjustForInflation: false,
                    });
                  } else {
                    setTargetAmount(null);
                    setCurrentSavingsDisplay(null);
                    setMonthlyContribution(null);
                    setMonthlyExpenses(null);
                    setInitialFormData(undefined);
                  }
                })
                .catch(err => {
                  console.error('Error fetching Freedom Fund data:', err);
                  setError(err.message || 'Could not load your saved data.');
                })
                .finally(() => setIsLoading(false));
            }}
            showRetry={true}
          />
        </div>
      )}

      <FreedomFundCalculatorForm
        onSubmit={handleCalculateAndSave}
        initialData={initialFormData}
      />

      {(targetAmount !== null || initialFormData) && (
        <div className="mt-8">
          <FreedomFundResults
            targetAmount={targetAmount}
            currentSavings={currentSavingsDisplay}
            monthlyContribution={monthlyContribution}
            monthlyExpenses={monthlyExpenses}
          />
        </div>
      )}

      {isSaving && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-card p-6 rounded-lg shadow-lg border">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
              <p className="text-foreground">Saving your Freedom Fund data...</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Wrap the Freedom Fund page with error boundary
export default function FreedomFundPage() {
  return (
    <ErrorBoundary
      fallback={
        <PageLayout>
          <div className="max-w-4xl mx-auto p-6 text-center">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                Freedom Fund Calculator Error
              </h2>
              <p className="text-red-600 dark:text-red-300 mb-4">
                There was an issue loading the Freedom Fund calculator. Please try refreshing the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </PageLayout>
      }
      onError={(error, errorInfo) => {
        console.error('Freedom Fund Page Error:', { error, errorInfo });
      }}
    >
      <FreedomFundPageContent />
    </ErrorBoundary>
  );
}
