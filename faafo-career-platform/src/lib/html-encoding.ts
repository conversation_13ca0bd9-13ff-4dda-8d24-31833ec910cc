/**
 * HTML Encoding and Security Utilities
 * Provides comprehensive HTML encoding, XSS prevention, and content sanitization
 */

import DOMPurify from 'isomorphic-dompurify';

/**
 * HTML entity encoding map for special characters
 */
const HTML_ENTITIES: Record<string, string> = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '/': '&#x2F;',
  '`': '&#x60;',
  '=': '&#x3D;'
};

/**
 * Encode HTML entities to prevent XSS attacks
 */
export function encodeHtmlEntities(text: string): string {
  if (typeof text !== 'string') {
    return String(text);
  }
  
  return text.replace(/[&<>"'`=\/]/g, (char) => HTML_ENTITIES[char] || char);
}

/**
 * Decode HTML entities back to original characters
 */
export function decodeHtmlEntities(text: string): string {
  if (typeof text !== 'string') {
    return String(text);
  }
  
  const entityMap: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#x27;': "'",
    '&#x2F;': '/',
    '&#x60;': '`',
    '&#x3D;': '='
  };
  
  return text.replace(/&[#\w]+;/g, (entity) => entityMap[entity] || entity);
}

/**
 * Sanitize HTML content using DOMPurify
 */
export function sanitizeHtml(html: string, options?: {
  allowedTags?: string[];
  allowedAttributes?: string[];
  stripTags?: boolean;
}): string {
  if (typeof html !== 'string') {
    return '';
  }

  const config: any = {
    ALLOWED_TAGS: options?.allowedTags || ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li'],
    ALLOWED_ATTR: options?.allowedAttributes || ['class'],
    KEEP_CONTENT: !options?.stripTags
  };

  return DOMPurify.sanitize(html, config) as string;
}

/**
 * Strip all HTML tags from content
 */
export function stripHtmlTags(html: string): string {
  if (typeof html !== 'string') {
    return '';
  }
  
  return html.replace(/<[^>]*>/g, '');
}

/**
 * Escape content for safe insertion into HTML attributes
 */
export function escapeHtmlAttribute(value: string): string {
  if (typeof value !== 'string') {
    return String(value);
  }
  
  return value
    .replace(/&/g, '&amp;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
}

/**
 * Escape content for safe insertion into JavaScript strings
 */
export function escapeJavaScript(value: string): string {
  if (typeof value !== 'string') {
    return String(value);
  }
  
  return value
    .replace(/\\/g, '\\\\')
    .replace(/'/g, "\\'")
    .replace(/"/g, '\\"')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t')
    .replace(/\f/g, '\\f')
    .replace(/\v/g, '\\v')
    .replace(/\0/g, '\\0');
}

/**
 * Validate and sanitize URL to prevent XSS through href attributes
 */
export function sanitizeUrl(url: string): string {
  if (typeof url !== 'string') {
    return '';
  }
  
  // Remove dangerous protocols
  const dangerousProtocols = /^(javascript|data|vbscript|file|about):/i;
  if (dangerousProtocols.test(url.trim())) {
    return '';
  }
  
  // Allow only safe protocols
  const safeProtocols = /^(https?|mailto|tel|ftp):/i;
  const isRelative = url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
  
  if (!safeProtocols.test(url) && !isRelative) {
    return '';
  }
  
  return encodeURI(url);
}

/**
 * Comprehensive content sanitization for user-generated content
 */
export function sanitizeUserContent(content: string, options?: {
  allowHtml?: boolean;
  maxLength?: number;
  allowedTags?: string[];
}): string {
  if (typeof content !== 'string') {
    return '';
  }
  
  let sanitized = content;
  
  // Trim and limit length
  sanitized = sanitized.trim();
  if (options?.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }
  
  // Handle HTML content
  if (options?.allowHtml) {
    sanitized = sanitizeHtml(sanitized, {
      allowedTags: options.allowedTags,
      stripTags: false
    });
  } else {
    sanitized = encodeHtmlEntities(sanitized);
  }
  
  return sanitized;
}

/**
 * Security-focused text processing for display
 */
export class SecureTextProcessor {
  static process(text: string, context: 'html' | 'attribute' | 'javascript' | 'url' = 'html'): string {
    if (typeof text !== 'string') {
      return '';
    }
    
    switch (context) {
      case 'html':
        return encodeHtmlEntities(text);
      case 'attribute':
        return escapeHtmlAttribute(text);
      case 'javascript':
        return escapeJavaScript(text);
      case 'url':
        return sanitizeUrl(text);
      default:
        return encodeHtmlEntities(text);
    }
  }
  
  static sanitize(html: string, allowedTags?: string[]): string {
    return sanitizeHtml(html, { allowedTags });
  }
  
  static strip(html: string): string {
    return stripHtmlTags(html);
  }
}

/**
 * React component helper for safe HTML rendering
 */
export function createSafeHtml(html: string, allowedTags?: string[]): { __html: string } {
  return {
    __html: sanitizeHtml(html, { allowedTags })
  };
}

/**
 * Additional security functions for compatibility
 */
export const encodeHtml = encodeHtmlEntities;
export const encodeHtmlAttribute = escapeHtmlAttribute;
export const safeDisplayText = (text: string) => encodeHtmlEntities(text);
export const safeUserName = (name: string) => sanitizeUserContent(name, { maxLength: 100 });
export const SafeText = SecureTextProcessor;

export default {
  encodeHtmlEntities,
  decodeHtmlEntities,
  sanitizeHtml,
  stripHtmlTags,
  escapeHtmlAttribute,
  escapeJavaScript,
  sanitizeUrl,
  sanitizeUserContent,
  SecureTextProcessor,
  createSafeHtml,
  // Compatibility exports
  encodeHtml,
  encodeHtmlAttribute,
  safeDisplayText,
  safeUserName,
  SafeText
};
