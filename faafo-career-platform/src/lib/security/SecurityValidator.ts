import { performance } from 'perf_hooks';
export interface SecurityValidationResult {
  isValid: boolean;
  errorType?: 'SECURITY_ERROR' | 'VALIDATION_ERROR' | 'RESOURCE_ERROR';
  error?: string;
  securityAlert?: boolean;
  sanitizedInput?: any;
  threatType?: string;
}

export interface SecurityConfig {
  maxInputLength: number;
  maxArrayLength: number;
  enableSqlInjectionDetection: boolean;
  enableXssDetection: boolean;
  enableRateLimiting: boolean;
  rateLimitWindow: number; // milliseconds
  rateLimitMaxRequests: number;
}

export interface SecurityIncident {
  type: string;
  userId?: string;
  timestamp: Date;
  request: string;
  ipAddress?: string;
  userAgent?: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

/**
 * Comprehensive security validator for skill gap analyzer
 */
export class SecurityValidator {
  private static instance: SecurityValidator;
  private config: SecurityConfig;
  private rateLimitMap: Map<string, { count: number; windowStart: number }> = new Map();
  private securityIncidents: SecurityIncident[] = [];

  // Security patterns - using simple string patterns for better compatibility
  private readonly SQL_INJECTION_PATTERNS = [
    /DROP\s+TABLE/i,
    /DELETE\s+FROM/i,
    /INSERT\s+INTO/i,
    /UPDATE\s+SET/i,
    /UNION\s+SELECT/i,
    /OR\s+1\s*=\s*1/i,
    /AND\s+1\s*=\s*1/i,
    /'\s*OR\s*'/i,
    /--/,
    /\/\*/,
    /\*\//
  ];

  private readonly XSS_PATTERNS = [
    /<script[^>]*>.*?<\/script>/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /<object[^>]*>.*?<\/object>/gi,
    /<embed[^>]*>/gi,
    /<link[^>]*>/gi,
    /<meta[^>]*>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
    /on\w+\s*=/gi, // Event handlers like onclick, onload, etc.
    /<img[^>]*src[^>]*>/gi,
    /<svg[^>]*>.*?<\/svg>/gi
  ];

  private readonly NOSQL_INJECTION_PATTERNS = [
    /\$ne\s*:/gi,
    /\$gt\s*:/gi,
    /\$lt\s*:/gi,
    /\$regex\s*:/gi,
    /\$where\s*:/gi,
    /\$or\s*:/gi,
    /\$and\s*:/gi,
    /\$in\s*:/gi,
    /\$nin\s*:/gi
  ];

  private readonly PROTOTYPE_POLLUTION_PATTERNS = [
    /__proto__/gi,
    /constructor/gi,
    /prototype/gi
  ];

  private constructor() {
    this.config = {
      maxInputLength: 1000,
      maxArrayLength: 100,
      enableSqlInjectionDetection: true,
      enableXssDetection: true,
      enableRateLimiting: true,
      rateLimitWindow: 60000, // 1 minute
      rateLimitMaxRequests: 100
    };
  }

  public static getInstance(): SecurityValidator {
    if (!SecurityValidator.instance) {
      SecurityValidator.instance = new SecurityValidator();
    }
    return SecurityValidator.instance;
  }

  /**
   * Comprehensive input validation
   */
  validateInput(input: any, context: string = 'general'): SecurityValidationResult {
    try {
      // Check for null/undefined
      if (input === null || input === undefined) {
        return { isValid: true, sanitizedInput: input };
      }

      // Handle different input types
      if (typeof input === 'string') {
        return this.validateString(input, context);
      } else if (Array.isArray(input)) {
        return this.validateArray(input, context);
      } else if (typeof input === 'object') {
        return this.validateObject(input, context);
      } else {
        return this.validatePrimitive(input, context);
      }
    } catch (error) {
      this.logSecurityIncident({
        type: 'VALIDATION_ERROR',
        timestamp: new Date(),
        request: JSON.stringify(input),
        severity: 'MEDIUM'
      });

      return {
        isValid: false,
        errorType: 'SECURITY_ERROR',
        error: 'Input validation failed',
        securityAlert: true
      };
    }
  }

  /**
   * Validate string inputs
   */
  private validateString(input: string, context: string): SecurityValidationResult {
    // Length validation
    if (input.length > this.config.maxInputLength) {
      this.logSecurityIncident({
        type: 'DOS_ATTEMPT',
        timestamp: new Date(),
        request: `Input too long: ${input.length} characters`,
        severity: 'HIGH'
      });

      return {
        isValid: false,
        errorType: 'VALIDATION_ERROR',
        error: 'Input too long',
        securityAlert: true,
        threatType: 'DOS_ATTEMPT'
      };
    }

    // SQL Injection detection
    if (this.config.enableSqlInjectionDetection) {
      for (const pattern of this.SQL_INJECTION_PATTERNS) {
        if (pattern.test(input)) {
          this.logSecurityIncident({
            type: 'SQL_INJECTION_ATTEMPT',
            timestamp: new Date(),
            request: input,
            severity: 'CRITICAL'
          });

          return {
            isValid: false,
            errorType: 'SECURITY_ERROR',
            error: 'Malicious input detected',
            securityAlert: true,
            threatType: 'SQL_INJECTION'
          };
        }
      }
    }

    // XSS detection
    if (this.config.enableXssDetection) {
      for (const pattern of this.XSS_PATTERNS) {
        if (pattern.test(input)) {
          this.logSecurityIncident({
            type: 'XSS_ATTEMPT',
            timestamp: new Date(),
            request: input,
            severity: 'HIGH'
          });

          return {
            isValid: false,
            errorType: 'SECURITY_ERROR',
            error: 'Malicious input detected',
            securityAlert: true,
            threatType: 'XSS'
          };
        }
      }
    }

    // NoSQL Injection detection
    for (const pattern of this.NOSQL_INJECTION_PATTERNS) {
      if (pattern.test(input)) {
        this.logSecurityIncident({
          type: 'NOSQL_INJECTION_ATTEMPT',
          timestamp: new Date(),
          request: input,
          severity: 'HIGH'
        });

        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious input detected',
          securityAlert: true,
          threatType: 'NOSQL_INJECTION'
        };
      }
    }

    // Sanitize the input
    const sanitized = this.sanitizeString(input);

    return {
      isValid: true,
      sanitizedInput: sanitized
    };
  }

  /**
   * Validate array inputs
   */
  private validateArray(input: any[], context: string): SecurityValidationResult {
    // Length validation
    if (input.length > this.config.maxArrayLength) {
      this.logSecurityIncident({
        type: 'DOS_ATTEMPT',
        timestamp: new Date(),
        request: `Array too large: ${input.length} items`,
        severity: 'HIGH'
      });

      return {
        isValid: false,
        errorType: 'VALIDATION_ERROR',
        error: 'Too many skills',
        securityAlert: true,
        threatType: 'DOS_ATTEMPT'
      };
    }

    // Validate each item in the array
    const sanitizedArray = [];
    for (let i = 0; i < input.length; i++) {
      const itemValidation = this.validateInput(input[i], `${context}[${i}]`);
      
      if (!itemValidation.isValid) {
        // If any item is malicious, reject the entire array
        this.logSecurityIncident({
          type: 'ARRAY_INJECTION_ATTEMPT',
          timestamp: new Date(),
          request: JSON.stringify(input),
          severity: 'HIGH'
        });

        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious input detected in array',
          securityAlert: true,
          threatType: 'ARRAY_INJECTION'
        };
      }

      sanitizedArray.push(itemValidation.sanitizedInput);
    }

    return {
      isValid: true,
      sanitizedInput: sanitizedArray
    };
  }

  /**
   * Validate object inputs
   */
  private validateObject(input: any, context: string): SecurityValidationResult {
    // Check for prototype pollution
    for (const pattern of this.PROTOTYPE_POLLUTION_PATTERNS) {
      const inputString = JSON.stringify(input);
      if (pattern.test(inputString)) {
        this.logSecurityIncident({
          type: 'PROTOTYPE_POLLUTION_ATTEMPT',
          timestamp: new Date(),
          request: inputString,
          severity: 'CRITICAL'
        });

        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious object structure detected',
          securityAlert: true,
          threatType: 'PROTOTYPE_POLLUTION'
        };
      }
    }

    // Validate each property
    const sanitizedObject: any = {};
    for (const [key, value] of Object.entries(input)) {
      const keyValidation = this.validateInput(key, `${context}.key`);
      const valueValidation = this.validateInput(value, `${context}.${key}`);

      if (!keyValidation.isValid || !valueValidation.isValid) {
        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: 'Malicious input detected in object',
          securityAlert: true
        };
      }

      sanitizedObject[keyValidation.sanitizedInput || key] = valueValidation.sanitizedInput;
    }

    return {
      isValid: true,
      sanitizedInput: sanitizedObject
    };
  }

  /**
   * Validate primitive inputs
   */
  private validatePrimitive(input: any, context: string): SecurityValidationResult {
    // Convert to string and validate
    const stringValue = String(input);
    return this.validateString(stringValue, context);
  }

  /**
   * Sanitize string input
   */
  private sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/vbscript:/gi, '') // Remove vbscript: protocol
      .replace(/data:text\/html/gi, '') // Remove data URLs
      .replace(/on\w+\s*=/gi, ''); // Remove event handlers
  }

  /**
   * Check if input contains XSS patterns
   */
  static containsXSS(input: string): boolean {
    if (!input || typeof input !== 'string') {
      return false;
    }

    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /<object[^>]*>.*?<\/object>/gi,
      /<embed[^>]*>/gi,
      /<link[^>]*>/gi,
      /<meta[^>]*>/gi,
      /javascript:/gi,
      /vbscript:/gi,
      /data:text\/html/gi,
      /on\w+\s*=/gi, // Event handlers like onclick, onload, etc.
      /<img[^>]*src[^>]*>/gi,
      /<svg[^>]*>.*?<\/svg>/gi
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Validate and sanitize JSON fields
   */
  static validateJsonField(jsonData: any, fieldName: string): SecurityValidationResult {
    try {
      if (!jsonData) {
        return { isValid: true, sanitizedInput: jsonData };
      }

      const jsonString = JSON.stringify(jsonData);

      // Check for XSS in JSON
      if (this.containsXSS(jsonString)) {
        return {
          isValid: false,
          errorType: 'SECURITY_ERROR',
          error: `XSS detected in ${fieldName}`,
          securityAlert: true,
          threatType: 'XSS'
        };
      }

      // Check for template injection
      const templateInjectionPatterns = [
        /\$\{[^}]*\}/g, // ${...} template literals
        /\{\{[^}]*\}\}/g, // {{...}} handlebars/mustache
        /%\{[^}]*\}/g, // %{...} ruby-style
      ];

      for (const pattern of templateInjectionPatterns) {
        if (pattern.test(jsonString)) {
          return {
            isValid: false,
            errorType: 'SECURITY_ERROR',
            error: `Template injection detected in ${fieldName}`,
            securityAlert: true,
            threatType: 'TEMPLATE_INJECTION'
          };
        }
      }

      // Recursively sanitize object properties
      const sanitized = this.sanitizeJsonObject(jsonData);

      return {
        isValid: true,
        sanitizedInput: sanitized
      };
    } catch (error) {
      return {
        isValid: false,
        errorType: 'VALIDATION_ERROR',
        error: `Invalid JSON in ${fieldName}`,
        securityAlert: false
      };
    }
  }

  /**
   * Recursively sanitize JSON object
   */
  private static sanitizeJsonObject(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (typeof obj === 'string') {
      // Sanitize string values
      return obj
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/vbscript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/\$\{[^}]*\}/g, '') // Remove template literals
        .trim();
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeJsonObject(item));
    }

    if (typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // Sanitize both key and value
        const sanitizedKey = typeof key === 'string' ?
          key.replace(/[<>]/g, '').replace(/javascript:/gi, '') : key;
        sanitized[sanitizedKey] = this.sanitizeJsonObject(value);
      }
      return sanitized;
    }

    return obj;
  }

  /**
   * Rate limiting validation
   */
  validateRateLimit(identifier: string): SecurityValidationResult {
    if (!this.config.enableRateLimiting) {
      return { isValid: true };
    }

    const now = Date.now();
    const rateLimitData = this.rateLimitMap.get(identifier);

    if (!rateLimitData) {
      // First request from this identifier
      this.rateLimitMap.set(identifier, { count: 1, windowStart: now });
      return { isValid: true };
    }

    // Check if we're still in the same window
    if (now - rateLimitData.windowStart < this.config.rateLimitWindow) {
      if (rateLimitData.count >= this.config.rateLimitMaxRequests) {
        this.logSecurityIncident({
          type: 'RATE_LIMIT_EXCEEDED',
          timestamp: new Date(),
          request: `Identifier: ${identifier}, Count: ${rateLimitData.count}`,
          severity: 'MEDIUM'
        });

        return {
          isValid: false,
          errorType: 'RESOURCE_ERROR',
          error: 'Rate limit exceeded',
          securityAlert: true,
          threatType: 'RATE_LIMIT_EXCEEDED'
        };
      }

      // Increment count
      rateLimitData.count++;
    } else {
      // New window
      this.rateLimitMap.set(identifier, { count: 1, windowStart: now });
    }

    return { isValid: true };
  }

  /**
   * Log security incident
   */
  private logSecurityIncident(incident: Omit<SecurityIncident, 'timestamp'> & { timestamp: Date }): void {
    this.securityIncidents.push(incident);

    // Log to console for monitoring
    console.warn('SECURITY_ALERT', incident);

    // Keep only last 1000 incidents to prevent memory issues
    if (this.securityIncidents.length > 1000) {
      this.securityIncidents = this.securityIncidents.slice(-1000);
    }
  }

  /**
   * Get security statistics
   */
  getSecurityStatistics(): any {
    const now = Date.now();
    const last24Hours = this.securityIncidents.filter(
      incident => now - incident.timestamp.getTime() < 24 * 60 * 60 * 1000
    );

    const incidentsByType = last24Hours.reduce((acc, incident) => {
      acc[incident.type] = (acc[incident.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const incidentsBySeverity = last24Hours.reduce((acc, incident) => {
      acc[incident.severity] = (acc[incident.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalIncidents: this.securityIncidents.length,
      last24Hours: last24Hours.length,
      incidentsByType,
      incidentsBySeverity,
      rateLimitStatus: {
        activeIdentifiers: this.rateLimitMap.size,
        config: this.config
      }
    };
  }

  /**
   * Clear rate limit data (for testing)
   */
  clearRateLimitData(): void {
    this.rateLimitMap.clear();
  }

  /**
   * Update security configuration
   */
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export singleton instance
export const securityValidator = SecurityValidator.getInstance();
