import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { ComprehensiveSecurityValidator } from '@/lib/comprehensive-security-validator';
import { SecureCacheService } from '@/lib/secure-cache-service';
import { SecurityValidator } from '@/lib/validation';
import { authOptions } from '@/lib/auth';
import { enhancedRateLimiters } from '@/lib/enhanced-rate-limiter';
import { withCSRFProtection, getCSRFToken } from '@/lib/csrf';
import { withRateLimit, rateLimiters } from '@/lib/rate-limit';

export interface SecurityOptions {
  requireAuth?: boolean;
  requireCSRF?: boolean;
  rateLimitType?: 'api' | 'auth' | 'write' | 'search' | 'passwordReset';
  validateInput?: boolean;
  allowedMethods?: string[];
  maxBodySize?: number;
  useEnhancedRateLimit?: boolean;
  useComprehensiveValidation?: boolean;
  enableSecureCache?: boolean;
  threatDetection?: boolean;
}

export class SecurityMiddleware {
  /**
   * Comprehensive security middleware that applies multiple protection layers
   */
  static async protect(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: SecurityOptions = {}
  ): Promise<NextResponse> {
    const {
      requireAuth = false,
      requireCSRF = true,
      rateLimitType = 'api',
      validateInput = true,
      allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      maxBodySize = 1024 * 1024, // 1MB default
      useEnhancedRateLimit = true,
      useComprehensiveValidation = true,
      enableSecureCache = true,
      threatDetection = true
    } = options;

    try {
      // 1. Method validation
      if (!allowedMethods.includes(request.method)) {
        return NextResponse.json(
          { error: 'Method not allowed' },
          { status: 405 }
        );
      }

      // 2. Enhanced Rate limiting
      if (useEnhancedRateLimit) {
        // Map rate limit types to available limiters
        const limiterMap: Record<string, keyof typeof enhancedRateLimiters> = {
          'search': 'api',
          'auth': 'auth',
          'api': 'api',
          'write': 'write',
          'passwordReset': 'auth'
        };

        const limiterKey = limiterMap[rateLimitType] || 'api';
        const enhancedLimiter = enhancedRateLimiters[limiterKey];
        const enhancedResult = await enhancedLimiter.checkLimit(request);

        if (!enhancedResult.allowed) {
          console.warn('Enhanced rate limit exceeded:', {
            type: enhancedResult.limitType,
            limit: enhancedResult.limit,
            remaining: enhancedResult.remaining,
            retryAfter: enhancedResult.retryAfter
          });

          return NextResponse.json(
            {
              error: 'Too many requests',
              retryAfter: enhancedResult.retryAfter,
              limitType: enhancedResult.limitType
            },
            {
              status: 429,
              headers: enhancedResult.headers
            }
          );
        }
      } else {
        // Fallback to original rate limiting
        const rateLimiter = rateLimiters[rateLimitType];
        const rateLimitResult = withRateLimit(rateLimiter)(request);

        if (!rateLimitResult.allowed) {
          return NextResponse.json(
            {
              error: 'Too many requests',
              retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
            },
            {
              status: 429,
              headers: rateLimitResult.headers
            }
          );
        }
      }

      // 3. Authentication check
      if (requireAuth) {
        const session = await getServerSession(authOptions);
        if (!session?.user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }
      }

      // 4. CSRF protection for state-changing operations
      if (requireCSRF && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
        const csrfToken = request.headers.get('x-csrf-token') || 
                         request.headers.get('csrf-token');
        
        if (!csrfToken) {
          return NextResponse.json(
            { error: 'CSRF token missing' },
            { status: 403 }
          );
        }

        // Validate CSRF token using existing function
        const { validateCSRFToken } = await import('@/lib/csrf');
        const isValidCSRF = await validateCSRFToken(request, csrfToken);
        
        if (!isValidCSRF) {
          return NextResponse.json(
            { error: 'Invalid CSRF token' },
            { status: 403 }
          );
        }
      }

      // 5. Input validation and sanitization
      if (validateInput && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        const contentLength = parseInt(request.headers.get('content-length') || '0');

        if (contentLength > maxBodySize) {
          return NextResponse.json(
            { error: 'Request body too large' },
            { status: 413 }
          );
        }

        // Skip body validation to avoid consuming the stream
        // The handler will validate the body after parsing
        // This prevents the "body already read" error
      }

      // 6. Security headers
      const response = await handler();
      
      // Add security headers
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
      response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
      
      // Content Security Policy
      const csp = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "font-src 'self'",
        "connect-src 'self'",
        "frame-ancestors 'none'"
      ].join('; ');
      
      response.headers.set('Content-Security-Policy', csp);

      return response;

    } catch (error) {
      console.error('Security middleware error:', error);
      return NextResponse.json(
        { error: 'Internal security error' },
        { status: 500 }
      );
    }
  }

  /**
   * Quick security wrapper for API routes
   */
  static async secureAPI(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return this.protect(request, handler, {
      requireCSRF: true,
      rateLimitType: 'api',
      validateInput: true,
      ...options
    });
  }

  /**
   * Enhanced security for authentication endpoints
   */
  static async secureAuth(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return this.protect(request, handler, {
      requireCSRF: true,
      rateLimitType: 'auth',
      validateInput: true,
      allowedMethods: ['POST'],
      maxBodySize: 1024, // 1KB for auth requests
      ...options
    });
  }

  /**
   * Security for write operations
   */
  static async secureWrite(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return this.protect(request, handler, {
      requireAuth: true,
      requireCSRF: true,
      rateLimitType: 'write',
      validateInput: true,
      allowedMethods: ['POST', 'PUT', 'PATCH', 'DELETE'],
      ...options
    });
  }

  /**
   * Security for read operations
   */
  static async secureRead(
    request: NextRequest,
    handler: () => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    return this.protect(request, handler, {
      requireCSRF: false,
      rateLimitType: 'search',
      validateInput: false,
      allowedMethods: ['GET'],
      ...options
    });
  }

  /**
   * Body-aware security wrapper that validates after JSON parsing
   */
  static async secureWithBodyValidation(
    request: NextRequest,
    handler: (body?: any) => Promise<NextResponse>,
    options: Partial<SecurityOptions> = {}
  ): Promise<NextResponse> {
    const {
      requireAuth = false,
      requireCSRF = true,
      rateLimitType = 'api',
      allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      maxBodySize = 1024 * 1024 // 1MB default
    } = options;

    try {
      // 1. Method validation
      if (!allowedMethods.includes(request.method)) {
        return NextResponse.json(
          { error: 'Method not allowed' },
          { status: 405 }
        );
      }

      // 2. Rate limiting
      const rateLimiter = rateLimiters[rateLimitType];
      const rateLimitResult = withRateLimit(rateLimiter)(request);

      if (!rateLimitResult.allowed) {
        return NextResponse.json(
          {
            error: 'Too many requests',
            retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
          },
          {
            status: 429,
            headers: rateLimitResult.headers
          }
        );
      }

      // 3. Authentication check
      if (requireAuth) {
        const session = await getServerSession(authOptions);
        if (!session?.user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }
      }

      // 4. CSRF protection for state-changing operations
      if (requireCSRF && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
        const csrfToken = request.headers.get('x-csrf-token') ||
                         request.headers.get('csrf-token');

        if (!csrfToken) {
          return NextResponse.json(
            { error: 'CSRF token missing' },
            { status: 403 }
          );
        }

        // Validate CSRF token using existing function
        const { validateCSRFToken } = await import('@/lib/csrf');
        const isValidCSRF = await validateCSRFToken(request, csrfToken);

        if (!isValidCSRF) {
          return NextResponse.json(
            { error: 'Invalid CSRF token' },
            { status: 403 }
          );
        }
      }

      // 5. Parse and validate body if present
      let body = null;
      if (['POST', 'PUT', 'PATCH'].includes(request.method)) {
        const contentLength = parseInt(request.headers.get('content-length') || '0');

        if (contentLength > maxBodySize) {
          return NextResponse.json(
            { error: 'Request body too large' },
            { status: 413 }
          );
        }

        try {
          body = await request.json();

          // Validate parsed body for security threats
          if (body) {
            const bodyString = JSON.stringify(body);
            const securityCheck = SecurityValidator.validateSecurity(bodyString);
            if (!securityCheck.isValid) {
              console.warn('Security threats detected:', securityCheck.threats);
              return NextResponse.json(
                {
                  error: 'Invalid input detected',
                  details: 'Request contains potentially malicious content'
                },
                { status: 400 }
              );
            }
          }
        } catch (error) {
          return NextResponse.json(
            { error: 'Invalid request body' },
            { status: 400 }
          );
        }
      }

      // 6. Call handler with parsed body
      const response = await handler(body);

      // 7. Add security headers
      const secureResponse = this.addSecurityHeaders(response);

      return secureResponse;

    } catch (error) {
      console.error('Security middleware error:', error);
      return NextResponse.json(
        { error: 'Internal security error' },
        { status: 500 }
      );
    }
  }

  /**
   * Add security headers to response
   */
  private static addSecurityHeaders(response: NextResponse): NextResponse {
    // Security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

    // Content Security Policy
    const csp = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "frame-ancestors 'none'"
    ].join('; ');

    response.headers.set('Content-Security-Policy', csp);

    return response;
  }
}

/**
 * Utility function to get CSRF token for client-side use
 */
export async function getClientCSRFToken(request: NextRequest): Promise<string> {
  return await getCSRFToken(request);
}

/**
 * Session security enhancements
 */
export class SessionSecurity {
  /**
   * Generate secure session ID
   */
  static generateSecureSessionId(): string {
    return crypto.randomUUID() + '-' + Date.now().toString(36);
  }

  /**
   * Validate session integrity
   */
  static validateSessionIntegrity(sessionData: any): boolean {
    if (!sessionData || typeof sessionData !== 'object') {
      return false;
    }

    // Check for required session fields
    const requiredFields = ['user', 'expires'];
    for (const field of requiredFields) {
      if (!(field in sessionData)) {
        return false;
      }
    }

    // Check expiration
    if (new Date(sessionData.expires) < new Date()) {
      return false;
    }

    return true;
  }

  /**
   * Regenerate session ID for security
   */
  static async regenerateSession(request: NextRequest): Promise<string> {
    // This would integrate with your session management system
    // For now, return a new secure session ID
    return this.generateSecureSessionId();
  }
}

export default SecurityMiddleware;
