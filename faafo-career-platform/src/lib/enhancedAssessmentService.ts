import prisma from '@/lib/prisma';
import { AssessmentResponse, AssessmentInsights, SkillGap, CareerPathAnalysis } from '@/lib/assessmentScoring';
import { generateAssessmentInsights } from '@/lib/assessmentScoring';

export interface EnhancedAssessmentResults {
  insights: AssessmentInsights;
  careerPathRecommendations: CareerPathRecommendation[];
  learningPath: LearningPathRecommendation;
  skillDevelopmentPlan: SkillDevelopmentPlan;
  nextSteps: ActionableStep[];
}

export interface CareerPathRecommendation {
  id: string;
  name: string;
  slug: string;
  matchPercentage: number;
  matchReason: string;
  salaryRange: {
    min: number;
    max: number;
    currency: string;
  };
  jobGrowthRate: string;
  skillAlignment: {
    strongSkills: string[];
    skillGaps: SkillGap[];
  };
  timeToTransition: string;
  difficultyLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  recommendedResources: LearningResourceRecommendation[];
  nextSteps: string[];
}

export interface LearningResourceRecommendation {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  duration: string;
  cost: string;
  format: string;
  averageRating: number;
  relevanceScore: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  skillsAddressed: string[];
}

export interface LearningPathRecommendation {
  title: string;
  description: string;
  estimatedDuration: string;
  phases: LearningPhase[];
  milestones: Milestone[];
}

export interface LearningPhase {
  phase: number;
  title: string;
  description: string;
  duration: string;
  skills: string[];
  resources: LearningResourceRecommendation[];
  prerequisites: string[];
}

export interface Milestone {
  title: string;
  description: string;
  estimatedWeek: number;
  skills: string[];
  deliverables: string[];
}

export interface SkillDevelopmentPlan {
  prioritySkills: PrioritySkill[];
  learningSchedule: LearningScheduleItem[];
  estimatedTimeToCompetency: string;
}

export interface PrioritySkill {
  skill: string;
  currentLevel: number;
  targetLevel: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  estimatedLearningTime: string;
  resources: LearningResourceRecommendation[];
  prerequisites: string[];
}

export interface LearningScheduleItem {
  week: number;
  focus: string;
  skills: string[];
  resources: string[];
  timeCommitment: string;
}

export interface ActionableStep {
  category: 'IMMEDIATE' | 'SHORT_TERM' | 'LONG_TERM';
  title: string;
  description: string;
  estimatedTime: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  resources: string[];
  dependencies: string[];
}

export class EnhancedAssessmentService {
  static async generateEnhancedResults(
    assessmentId: string,
    responses: AssessmentResponse
  ): Promise<EnhancedAssessmentResults> {
    // Generate base insights
    const insights = await generateAssessmentInsights(responses);
    
    // Get career path recommendations with detailed analysis
    const careerPathRecommendations = await this.generateCareerPathRecommendations(
      assessmentId, 
      responses, 
      insights
    );
    
    // Generate personalized learning path
    const learningPath = await this.generateLearningPath(
      responses, 
      insights, 
      careerPathRecommendations
    );
    
    // Create skill development plan
    const skillDevelopmentPlan = await this.generateSkillDevelopmentPlan(
      responses, 
      insights, 
      careerPathRecommendations
    );
    
    // Generate actionable next steps
    const nextSteps = this.generateActionableSteps(
      insights, 
      careerPathRecommendations, 
      skillDevelopmentPlan
    );
    
    return {
      insights,
      careerPathRecommendations,
      learningPath,
      skillDevelopmentPlan,
      nextSteps
    };
  }

  private static async generateCareerPathRecommendations(
    assessmentId: string,
    responses: AssessmentResponse,
    insights: AssessmentInsights
  ): Promise<CareerPathRecommendation[]> {
    // Get career path suggestions from existing service
    const { getCareerPathSuggestions } = await import('@/lib/suggestionService');
    const suggestions = await getCareerPathSuggestions(assessmentId);
    
    // Enhance each suggestion with detailed analysis
    const enhancedRecommendations: CareerPathRecommendation[] = [];
    
    for (const suggestion of suggestions.slice(0, 5)) { // Top 5 recommendations
      const careerPath = suggestion.careerPath;
      
      // Calculate skill gaps for this career path
      const skillGaps = await this.calculateSkillGaps(
        careerPath.id,
        insights.topSkills,
        responses
      );
      
      // Get recommended resources for this career path
      const recommendedResources = await this.getCareerPathResources(
        careerPath.id,
        skillGaps
      );
      
      // Generate next steps
      const nextSteps = this.generateCareerPathNextSteps(
        careerPath,
        skillGaps,
        insights
      );
      
      enhancedRecommendations.push({
        id: careerPath.id,
        name: careerPath.name,
        slug: careerPath.slug,
        matchPercentage: Math.round(suggestion.score),
        matchReason: suggestion.matchReason || 'Good match based on your assessment responses',
        salaryRange: this.getSalaryRange(careerPath.name),
        jobGrowthRate: this.getJobGrowthRate(careerPath.name),
        skillAlignment: {
          strongSkills: this.getAlignedSkills(careerPath, insights.topSkills),
          skillGaps
        },
        timeToTransition: this.estimateTransitionTime(skillGaps, insights),
        difficultyLevel: this.assessDifficultyLevel(skillGaps, insights),
        recommendedResources,
        nextSteps
      });
    }
    
    return enhancedRecommendations;
  }

  private static async calculateSkillGaps(
    careerPathId: string,
    userSkills: string[],
    responses: AssessmentResponse
  ): Promise<SkillGap[]> {
    // Get required skills for this career path
    const careerPath = await prisma.careerPath.findUnique({
      where: { id: careerPathId },
      include: {
        relatedSkills: true
      }
    });

    if (!careerPath) return [];

    const skillGaps: SkillGap[] = [];

    for (const skill of careerPath.relatedSkills) {
      const userHasSkill = userSkills.includes(skill.name.toLowerCase());
      const currentLevel = userHasSkill ? 3 : 1; // Simplified logic
      const requiredLevel = 4; // Most careers require intermediate to advanced
      
      if (currentLevel < requiredLevel) {
        skillGaps.push({
          skill: skill.name,
          currentLevel,
          requiredLevel,
          priority: this.getSkillPriority(skill.name, careerPath.name),
          estimatedLearningTime: this.estimateSkillLearningTime(
            skill.name,
            requiredLevel - currentLevel
          ),
          recommendedResources: [] // Will be populated later
        });
      }
    }
    
    return skillGaps.sort((a, b) => {
      const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  private static async getCareerPathResources(
    careerPathId: string,
    skillGaps: SkillGap[]
  ): Promise<LearningResourceRecommendation[]> {
    const resources = await prisma.learningResource.findMany({
      where: {
        isActive: true,
        careerPaths: {
          some: {
            id: careerPathId
          }
        }
      },
      include: {
        ratings: {
          select: {
            rating: true
          }
        }
      },
      take: 10
    });
    
    return resources.map(resource => ({
      id: resource.id,
      title: resource.title,
      description: resource.description,
      url: resource.url,
      type: resource.type,
      category: resource.category,
      skillLevel: resource.skillLevel,
      duration: resource.duration || 'Not specified',
      cost: resource.cost,
      format: resource.format,
      averageRating: resource.ratings.length > 0 
        ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
        : 0,
      relevanceScore: this.calculateResourceRelevance(resource, skillGaps),
      priority: this.getResourcePriority(resource, skillGaps),
      skillsAddressed: this.getResourceSkills(resource, skillGaps)
    })).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  // Helper methods for calculations
  private static getSalaryRange(careerPathName: string) {
    // Simplified salary data - in production, this would come from a salary API
    const salaryData: Record<string, { min: number; max: number }> = {
      'Software Developer': { min: 70000, max: 150000 },
      'Data Scientist': { min: 80000, max: 160000 },
      'Product Manager': { min: 90000, max: 180000 },
      'UX Designer': { min: 65000, max: 130000 },
      'Digital Marketer': { min: 50000, max: 100000 }
    };
    
    return {
      ...salaryData[careerPathName] || { min: 50000, max: 100000 },
      currency: 'USD'
    };
  }

  private static getJobGrowthRate(careerPathName: string): string {
    // Simplified job growth data
    const growthData: Record<string, string> = {
      'Software Developer': '22% (Much faster than average)',
      'Data Scientist': '35% (Much faster than average)',
      'Product Manager': '19% (Much faster than average)',
      'UX Designer': '13% (Faster than average)',
      'Digital Marketer': '10% (Faster than average)'
    };
    
    return growthData[careerPathName] || '8% (As fast as average)';
  }

  private static getAlignedSkills(careerPath: any, userSkills: string[]): string[] {
    // Simplified skill alignment logic
    return userSkills.slice(0, 3); // Return top 3 user skills as aligned
  }

  private static estimateTransitionTime(skillGaps: SkillGap[], insights: AssessmentInsights): string {
    const totalGaps = skillGaps.length;
    const urgency = insights.scores.urgencyLevel;
    
    if (totalGaps <= 2 && urgency >= 4) return '3-6 months';
    if (totalGaps <= 4 && urgency >= 3) return '6-12 months';
    if (totalGaps <= 6) return '12-18 months';
    return '18-24 months';
  }

  private static assessDifficultyLevel(skillGaps: SkillGap[], insights: AssessmentInsights): 'LOW' | 'MEDIUM' | 'HIGH' {
    const highPriorityGaps = skillGaps.filter(gap => gap.priority === 'HIGH').length;
    const readinessScore = insights.scores.readinessScore;
    
    if (highPriorityGaps <= 1 && readinessScore >= 70) return 'LOW';
    if (highPriorityGaps <= 3 && readinessScore >= 50) return 'MEDIUM';
    return 'HIGH';
  }

  private static getSkillPriority(skillName: string, careerPathName: string): 'HIGH' | 'MEDIUM' | 'LOW' {
    // Simplified priority logic - in production, this would be more sophisticated
    const coreSkills = ['Programming', 'Data Analysis', 'Project Management', 'Communication'];
    return coreSkills.some(core => skillName.includes(core)) ? 'HIGH' : 'MEDIUM';
  }

  private static estimateSkillLearningTime(skillName: string, levelGap: number): string {
    const baseTime = levelGap * 4; // 4 weeks per level
    return `${baseTime}-${baseTime + 4} weeks`;
  }

  private static calculateResourceRelevance(resource: any, skillGaps: SkillGap[]): number {
    // Simplified relevance calculation
    return Math.random() * 100; // In production, this would be more sophisticated
  }

  private static getResourcePriority(resource: any, skillGaps: SkillGap[]): 'HIGH' | 'MEDIUM' | 'LOW' {
    return resource.cost === 'FREE' ? 'HIGH' : 'MEDIUM';
  }

  private static getResourceSkills(resource: any, skillGaps: SkillGap[]): string[] {
    return skillGaps.slice(0, 2).map(gap => gap.skill);
  }

  private static generateCareerPathNextSteps(
    careerPath: any,
    skillGaps: SkillGap[],
    insights: AssessmentInsights
  ): string[] {
    const steps = [
      'Complete a skills assessment to identify specific gaps',
      'Start with foundational courses in your priority skill areas',
      'Build a portfolio project to demonstrate your capabilities'
    ];
    
    if (skillGaps.length > 3) {
      steps.push('Consider a structured bootcamp or certification program');
    }
    
    if (insights.scores.urgencyLevel >= 4) {
      steps.unshift('Begin networking in your target industry immediately');
    }
    
    return steps;
  }

  private static async generateLearningPath(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    careerPathRecommendations: CareerPathRecommendation[]
  ): Promise<LearningPathRecommendation> {
    const topCareer = careerPathRecommendations[0];
    if (!topCareer) {
      return {
        title: 'General Career Development Path',
        description: 'A foundational learning journey to build essential career skills',
        estimatedDuration: '6-12 months',
        phases: [],
        milestones: []
      };
    }

    const phases: LearningPhase[] = [
      {
        phase: 1,
        title: 'Foundation Building',
        description: 'Build core skills and understanding',
        duration: '4-6 weeks',
        skills: topCareer.skillAlignment.strongSkills.slice(0, 2),
        resources: topCareer.recommendedResources.slice(0, 3),
        prerequisites: []
      },
      {
        phase: 2,
        title: 'Skill Development',
        description: 'Address priority skill gaps',
        duration: '8-12 weeks',
        skills: topCareer.skillAlignment.skillGaps
          .filter(gap => gap.priority === 'HIGH')
          .map(gap => gap.skill),
        resources: topCareer.recommendedResources.slice(3, 6),
        prerequisites: ['Complete Phase 1']
      },
      {
        phase: 3,
        title: 'Practical Application',
        description: 'Apply skills through projects and real-world scenarios',
        duration: '6-8 weeks',
        skills: topCareer.skillAlignment.skillGaps
          .filter(gap => gap.priority === 'MEDIUM')
          .map(gap => gap.skill),
        resources: topCareer.recommendedResources.slice(6, 9),
        prerequisites: ['Complete Phase 2']
      },
      {
        phase: 4,
        title: 'Career Transition',
        description: 'Prepare for career transition and job search',
        duration: '4-6 weeks',
        skills: ['Networking', 'Interview Skills', 'Portfolio Development'],
        resources: [],
        prerequisites: ['Complete Phase 3']
      }
    ];

    const milestones: Milestone[] = [
      {
        title: 'Foundation Complete',
        description: 'Core skills established and ready for advanced learning',
        estimatedWeek: 6,
        skills: phases[0].skills,
        deliverables: ['Skills assessment', 'Learning plan review']
      },
      {
        title: 'Key Skills Developed',
        description: 'Priority skill gaps addressed with demonstrable competency',
        estimatedWeek: 18,
        skills: phases[1].skills,
        deliverables: ['Portfolio project', 'Skill certification']
      },
      {
        title: 'Practical Experience',
        description: 'Real-world application of skills through projects',
        estimatedWeek: 26,
        skills: phases[2].skills,
        deliverables: ['Capstone project', 'Industry presentation']
      },
      {
        title: 'Career Ready',
        description: 'Fully prepared for career transition',
        estimatedWeek: 32,
        skills: phases[3].skills,
        deliverables: ['Updated resume', 'Professional network', 'Job applications']
      }
    ];

    return {
      title: `${topCareer.name} Learning Path`,
      description: `A comprehensive learning journey to transition into ${topCareer.name} role`,
      estimatedDuration: topCareer.timeToTransition,
      phases,
      milestones
    };
  }

  private static async generateSkillDevelopmentPlan(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    careerPathRecommendations: CareerPathRecommendation[]
  ): Promise<SkillDevelopmentPlan> {
    const topCareer = careerPathRecommendations[0];
    if (!topCareer) {
      return {
        prioritySkills: [],
        learningSchedule: [],
        estimatedTimeToCompetency: '6-12 months'
      };
    }

    // Generate priority skills from skill gaps
    const prioritySkills: PrioritySkill[] = topCareer.skillAlignment.skillGaps.map(gap => ({
      skill: gap.skill,
      currentLevel: gap.currentLevel,
      targetLevel: gap.requiredLevel,
      priority: gap.priority,
      estimatedLearningTime: gap.estimatedLearningTime,
      resources: topCareer.recommendedResources.filter(resource =>
        resource.skillsAddressed.includes(gap.skill)
      ),
      prerequisites: gap.currentLevel <= 1 ? ['Basic computer literacy'] : []
    }));

    // Generate learning schedule
    const learningSchedule: LearningScheduleItem[] = [];
    let currentWeek = 1;

    // High priority skills first
    const highPrioritySkills = prioritySkills.filter(skill => skill.priority === 'HIGH');
    highPrioritySkills.forEach((skill, index) => {
      const weeksNeeded = Math.ceil(parseInt(skill.estimatedLearningTime.split('-')[0]) / 7);
      for (let week = 0; week < weeksNeeded; week++) {
        learningSchedule.push({
          week: currentWeek + week,
          focus: `${skill.skill} Development`,
          skills: [skill.skill],
          resources: skill.resources.slice(0, 2).map(r => r.title),
          timeCommitment: '10-15 hours/week'
        });
      }
      currentWeek += weeksNeeded;
    });

    // Medium priority skills
    const mediumPrioritySkills = prioritySkills.filter(skill => skill.priority === 'MEDIUM');
    mediumPrioritySkills.forEach((skill, index) => {
      const weeksNeeded = Math.ceil(parseInt(skill.estimatedLearningTime.split('-')[0]) / 7);
      for (let week = 0; week < Math.min(weeksNeeded, 4); week++) {
        learningSchedule.push({
          week: currentWeek + week,
          focus: `${skill.skill} Fundamentals`,
          skills: [skill.skill],
          resources: skill.resources.slice(0, 1).map(r => r.title),
          timeCommitment: '5-10 hours/week'
        });
      }
      currentWeek += Math.min(weeksNeeded, 4);
    });

    return {
      prioritySkills,
      learningSchedule: learningSchedule.slice(0, 24), // Limit to 6 months
      estimatedTimeToCompetency: topCareer.timeToTransition
    };
  }

  private static generateActionableSteps(
    insights: AssessmentInsights,
    careerPathRecommendations: CareerPathRecommendation[],
    skillDevelopmentPlan: SkillDevelopmentPlan
  ): ActionableStep[] {
    const steps: ActionableStep[] = [];
    const topCareer = careerPathRecommendations[0];

    // Immediate actions (this week)
    steps.push({
      category: 'IMMEDIATE',
      title: 'Complete Skills Assessment',
      description: 'Take a detailed skills assessment to validate your current competencies and identify specific areas for improvement.',
      estimatedTime: '2-3 hours',
      priority: 'HIGH',
      resources: ['Skills assessment tools', 'Self-evaluation frameworks'],
      dependencies: []
    });

    if (insights.scores.urgencyLevel >= 4) {
      steps.push({
        category: 'IMMEDIATE',
        title: 'Start Networking',
        description: 'Begin building professional connections in your target industry through LinkedIn, industry events, and professional associations.',
        estimatedTime: '1 hour daily',
        priority: 'HIGH',
        resources: ['LinkedIn optimization guide', 'Industry networking events'],
        dependencies: []
      });
    }

    if (topCareer) {
      steps.push({
        category: 'IMMEDIATE',
        title: 'Research Target Role',
        description: `Deep dive into ${topCareer.name} role requirements, day-to-day responsibilities, and career progression paths.`,
        estimatedTime: '3-4 hours',
        priority: 'HIGH',
        resources: ['Job descriptions', 'Industry reports', 'Professional interviews'],
        dependencies: []
      });
    }

    // Short-term actions (2-4 weeks)
    if (skillDevelopmentPlan.prioritySkills.length > 0) {
      const topSkill = skillDevelopmentPlan.prioritySkills[0];
      steps.push({
        category: 'SHORT_TERM',
        title: `Begin ${topSkill.skill} Learning`,
        description: `Start structured learning for ${topSkill.skill}, your highest priority skill gap.`,
        estimatedTime: topSkill.estimatedLearningTime,
        priority: topSkill.priority,
        resources: topSkill.resources.map(r => r.title),
        dependencies: ['Complete Skills Assessment']
      });
    }

    steps.push({
      category: 'SHORT_TERM',
      title: 'Create Learning Schedule',
      description: 'Establish a consistent learning routine with specific time blocks for skill development and progress tracking.',
      estimatedTime: '2-3 hours setup',
      priority: 'MEDIUM',
      resources: ['Calendar apps', 'Learning management tools', 'Progress tracking templates'],
      dependencies: ['Complete Skills Assessment']
    });

    steps.push({
      category: 'SHORT_TERM',
      title: 'Build Professional Portfolio',
      description: 'Start creating a portfolio that showcases your current skills and documents your learning journey.',
      estimatedTime: '5-10 hours',
      priority: 'MEDIUM',
      resources: ['Portfolio platforms', 'Project templates', 'Design tools'],
      dependencies: ['Research Target Role']
    });

    // Long-term actions (3-6 months)
    steps.push({
      category: 'LONG_TERM',
      title: 'Complete Core Skill Development',
      description: 'Finish learning and practicing all high-priority skills identified in your development plan.',
      estimatedTime: '3-4 months',
      priority: 'HIGH',
      resources: ['Advanced courses', 'Practice projects', 'Mentorship programs'],
      dependencies: ['Begin skill learning', 'Create Learning Schedule']
    });

    if (topCareer) {
      steps.push({
        category: 'LONG_TERM',
        title: 'Gain Practical Experience',
        description: `Seek opportunities to apply ${topCareer.name} skills through projects, internships, or volunteer work.`,
        estimatedTime: '2-3 months',
        priority: 'HIGH',
        resources: ['Project platforms', 'Volunteer opportunities', 'Internship programs'],
        dependencies: ['Complete Core Skill Development']
      });
    }

    steps.push({
      category: 'LONG_TERM',
      title: 'Prepare for Career Transition',
      description: 'Update resume, practice interviews, and begin active job searching or career transition planning.',
      estimatedTime: '4-6 weeks',
      priority: 'MEDIUM',
      resources: ['Resume templates', 'Interview prep guides', 'Job search platforms'],
      dependencies: ['Gain Practical Experience', 'Build Professional Portfolio']
    });

    steps.push({
      category: 'LONG_TERM',
      title: 'Establish Professional Presence',
      description: 'Build thought leadership through content creation, speaking opportunities, or industry contributions.',
      estimatedTime: 'Ongoing',
      priority: 'LOW',
      resources: ['Content platforms', 'Speaking opportunities', 'Industry publications'],
      dependencies: ['Complete Core Skill Development']
    });

    return steps;
  }
}
