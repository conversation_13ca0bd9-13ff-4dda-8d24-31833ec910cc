/**
 * SEO Utility Functions for FAAFO Career Platform
 * Provides structured data generation, breadcrumbs, and SEO optimization utilities
 */

import { BreadcrumbItem } from '@/lib/seo/seo-config';

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com';
const siteName = 'FAAFO Career Platform';

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbStructuredData(breadcrumbs: BreadcrumbItem[]): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url.startsWith('http') ? item.url : `${baseUrl}${item.url}`,
    })),
  };
}

/**
 * Generate FAQ structured data
 */
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

/**
 * Generate Article structured data
 */
export function generateArticleStructuredData(article: {
  title: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  image?: string;
  url: string;
}): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    author: {
      '@type': 'Person',
      name: article.author,
    },
    publisher: {
      '@type': 'Organization',
      name: siteName,
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/logo.png`,
      },
    },
    datePublished: article.datePublished,
    dateModified: article.dateModified || article.datePublished,
    image: article.image ? {
      '@type': 'ImageObject',
      url: article.image,
    } : undefined,
    url: article.url,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': article.url,
    },
  };
}

/**
 * Generate Course structured data
 */
export function generateCourseStructuredData(course: {
  name: string;
  description: string;
  provider: string;
  url: string;
  image?: string;
  duration?: string;
  skillLevel?: 'Beginner' | 'Intermediate' | 'Advanced';
  price?: number;
  currency?: string;
}): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name: course.name,
    description: course.description,
    provider: {
      '@type': 'Organization',
      name: course.provider,
    },
    url: course.url,
    image: course.image,
    timeRequired: course.duration,
    educationalLevel: course.skillLevel,
    offers: course.price !== undefined ? {
      '@type': 'Offer',
      price: course.price.toString(),
      priceCurrency: course.currency || 'USD',
    } : undefined,
  };
}

/**
 * Generate JobPosting structured data
 */
export function generateJobPostingStructuredData(job: {
  title: string;
  description: string;
  company: string;
  location: string;
  employmentType: string;
  datePosted: string;
  validThrough?: string;
  salary?: {
    min: number;
    max: number;
    currency: string;
  };
}): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'JobPosting',
    title: job.title,
    description: job.description,
    hiringOrganization: {
      '@type': 'Organization',
      name: job.company,
    },
    jobLocation: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        addressLocality: job.location,
      },
    },
    employmentType: job.employmentType,
    datePosted: job.datePosted,
    validThrough: job.validThrough,
    baseSalary: job.salary ? {
      '@type': 'MonetaryAmount',
      currency: job.salary.currency,
      value: {
        '@type': 'QuantitativeValue',
        minValue: job.salary.min,
        maxValue: job.salary.max,
        unitText: 'YEAR',
      },
    } : undefined,
  };
}

/**
 * Generate WebApplication structured data
 */
export function generateWebApplicationStructuredData(app: {
  name: string;
  description: string;
  url: string;
  category: string;
  price?: number;
  currency?: string;
  features?: string[];
}): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: app.name,
    description: app.description,
    url: app.url,
    applicationCategory: app.category,
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: app.price?.toString() || '0',
      priceCurrency: app.currency || 'USD',
    },
    featureList: app.features,
    provider: {
      '@type': 'Organization',
      name: siteName,
      url: baseUrl,
    },
  };
}

/**
 * Generate HowTo structured data
 */
export function generateHowToStructuredData(howTo: {
  name: string;
  description: string;
  image?: string;
  totalTime?: string;
  steps: Array<{
    name: string;
    text: string;
    image?: string;
  }>;
}): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'HowTo',
    name: howTo.name,
    description: howTo.description,
    image: howTo.image,
    totalTime: howTo.totalTime,
    step: howTo.steps.map((step, index) => ({
      '@type': 'HowToStep',
      position: index + 1,
      name: step.name,
      text: step.text,
      image: step.image,
    })),
  };
}

/**
 * Generate Organization structured data
 */
export function generateOrganizationStructuredData(): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteName,
    url: baseUrl,
    logo: `${baseUrl}/images/logo.png`,
    description: 'Career development platform providing AI-powered assessments and personalized career guidance.',
    foundingDate: '2024',
    sameAs: [
      'https://twitter.com/faafo_platform',
      'https://linkedin.com/company/faafo-career',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      email: '<EMAIL>',
      url: `${baseUrl}/contact`,
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'US',
    },
  };
}

/**
 * Generate WebSite structured data with search functionality
 */
export function generateWebSiteStructuredData(): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteName,
    url: baseUrl,
    description: 'Empowering career transitions through personalized assessments, financial planning, and community support.',
    potentialAction: {
      '@type': 'SearchAction',
      target: `${baseUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: siteName,
      url: baseUrl,
    },
  };
}

/**
 * Generate Review structured data
 */
export function generateReviewStructuredData(review: {
  itemName: string;
  reviewBody: string;
  author: string;
  datePublished: string;
  ratingValue: number;
  bestRating?: number;
  worstRating?: number;
}): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'Review',
    itemReviewed: {
      '@type': 'Thing',
      name: review.itemName,
    },
    reviewBody: review.reviewBody,
    author: {
      '@type': 'Person',
      name: review.author,
    },
    datePublished: review.datePublished,
    reviewRating: {
      '@type': 'Rating',
      ratingValue: review.ratingValue,
      bestRating: review.bestRating || 5,
      worstRating: review.worstRating || 1,
    },
  };
}

/**
 * Generate Event structured data
 */
export function generateEventStructuredData(event: {
  name: string;
  description: string;
  startDate: string;
  endDate?: string;
  location: string;
  organizer: string;
  url?: string;
  image?: string;
  price?: number;
  currency?: string;
}): object {
  return {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: event.name,
    description: event.description,
    startDate: event.startDate,
    endDate: event.endDate,
    location: {
      '@type': 'Place',
      name: event.location,
    },
    organizer: {
      '@type': 'Organization',
      name: event.organizer,
    },
    url: event.url,
    image: event.image,
    offers: event.price !== undefined ? {
      '@type': 'Offer',
      price: event.price.toString(),
      priceCurrency: event.currency || 'USD',
    } : undefined,
  };
}

/**
 * Inject structured data into page head
 */
export function injectStructuredData(structuredData: object | object[]): void {
  if (typeof window === 'undefined') return;

  const data = Array.isArray(structuredData) ? structuredData : [structuredData];
  
  data.forEach((item, index) => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.id = `structured-data-${index}`;
    script.textContent = JSON.stringify(item);
    
    // Remove existing script with same ID
    const existing = document.getElementById(script.id);
    if (existing) {
      existing.remove();
    }
    
    document.head.appendChild(script);
  });
}

/**
 * Generate SEO-friendly URL slug
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

/**
 * Generate canonical URL
 */
export function generateCanonicalUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
}

/**
 * Extract meta description from content
 */
export function extractMetaDescription(content: string, maxLength: number = 160): string {
  // Remove HTML tags and extra whitespace
  const cleanContent = content
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim();
  
  if (cleanContent.length <= maxLength) {
    return cleanContent;
  }
  
  // Find the last complete sentence within the limit
  const truncated = cleanContent.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return cleanContent.substring(0, lastSentence + 1);
  }
  
  // Find the last complete word
  const lastSpace = truncated.lastIndexOf(' ');
  return cleanContent.substring(0, lastSpace) + '...';
}
