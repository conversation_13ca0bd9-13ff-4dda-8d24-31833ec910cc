/**
 * Enhanced Alerting Service for Performance Monitoring
 * 
 * Provides comprehensive alerting with multiple notification channels,
 * escalation rules, and integration with existing monitoring infrastructure.
 */

import { EventEmitter } from 'events';
// Removed circular dependency: import { performanceMonitor } from '@/lib/performance-monitor';
import { advancedCacheManager } from '@/lib/advanced-cache-manager';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { aiServiceMonitor } from '@/lib/ai-service-monitor';

export interface PerformanceAlert {
  id: string;
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'PERFORMANCE' | 'MEMORY' | 'CACHE' | 'ERROR' | 'THRESHOLD';
  source: string;
  timestamp: number;
  metadata: Record<string, any>;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: number;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: number;
  escalated: boolean;
  escalationLevel: number;
}

export interface AlertThreshold {
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  value: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  duration: number; // milliseconds
  enabled: boolean;
}

export interface NotificationChannel {
  id: string;
  type: 'email' | 'slack' | 'webhook' | 'console' | 'database';
  config: Record<string, any>;
  enabled: boolean;
  severityFilter: ('low' | 'medium' | 'high' | 'critical')[];
  rateLimitMinutes: number;
  lastNotification: number;
}

export interface EscalationRule {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timeToEscalate: number; // milliseconds
  maxEscalationLevel: number;
  escalationChannels: string[];
  enabled: boolean;
}

export class EnhancedAlertingService extends EventEmitter {
  private alerts: Map<string, PerformanceAlert> = new Map();
  private thresholds: Map<string, AlertThreshold> = new Map();
  private channels: Map<string, NotificationChannel> = new Map();
  private escalationRules: Map<string, EscalationRule> = new Map();
  private escalationTimers: Map<string, NodeJS.Timeout> = new Map();
  private monitoringInterval?: NodeJS.Timeout;
  private isMonitoring: boolean = false;

  constructor() {
    super();
    this.setupDefaultThresholds();
    this.setupDefaultChannels();
    this.setupDefaultEscalationRules();
  }

  /**
   * Start the alerting service
   */
  start(): void {
    if (this.isMonitoring) {
      console.log('Enhanced alerting service already running');
      return;
    }

    this.isMonitoring = true;
    console.log('🚨 Starting enhanced alerting service...');

    // Monitor performance metrics every 30 seconds
    this.monitoringInterval = setInterval(() => {
      this.checkThresholds();
    }, 30000);

    // Initial threshold check
    this.checkThresholds();
  }

  /**
   * Stop the alerting service
   */
  stop(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    console.log('🚨 Stopping enhanced alerting service...');

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    // Clear all escalation timers
    Array.from(this.escalationTimers.values()).forEach(timer => {
      clearTimeout(timer);
    });
    this.escalationTimers.clear();

    // Clear alerts for testing
    if (process.env.NODE_ENV === 'test') {
      this.alerts.clear();
    }
  }

  /**
   * Create a new alert
   */
  async createAlert(alertData: Omit<PerformanceAlert, 'id' | 'timestamp' | 'acknowledged' | 'resolved' | 'escalated' | 'escalationLevel'>): Promise<string> {
    const alert: PerformanceAlert = {
      ...alertData,
      id: this.generateAlertId(),
      timestamp: Date.now(),
      acknowledged: false,
      resolved: false,
      escalated: false,
      escalationLevel: 0
    };

    this.alerts.set(alert.id, alert);

    // Send notifications
    await this.sendNotifications(alert);

    // Setup escalation
    this.setupEscalation(alert);

    // Emit alert event
    this.emit('alert_created', alert);

    console.log(`🚨 Alert created: ${alert.title} (${alert.severity})`);
    return alert.id;
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.acknowledged) return false;

    alert.acknowledged = true;
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = Date.now();

    // Cancel escalation
    this.cancelEscalation(alertId);

    this.emit('alert_acknowledged', alert);
    console.log(`✅ Alert acknowledged: ${alert.title} by ${acknowledgedBy}`);
    return true;
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string, resolvedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.resolved) return false;

    alert.resolved = true;
    alert.resolvedBy = resolvedBy;
    alert.resolvedAt = Date.now();

    // Cancel escalation
    this.cancelEscalation(alertId);

    this.emit('alert_resolved', alert);
    console.log(`✅ Alert resolved: ${alert.title} by ${resolvedBy}`);
    return true;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): PerformanceAlert[] {
    return Array.from(this.alerts.values())
      .filter(alert => !alert.resolved)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get alert statistics
   */
  getAlertStats(): {
    total: number;
    active: number;
    acknowledged: number;
    resolved: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
  } {
    const alerts = Array.from(this.alerts.values());
    return {
      total: alerts.length,
      active: alerts.filter(a => !a.resolved).length,
      acknowledged: alerts.filter(a => a.acknowledged && !a.resolved).length,
      resolved: alerts.filter(a => a.resolved).length,
      critical: alerts.filter(a => a.severity === 'critical').length,
      high: alerts.filter(a => a.severity === 'high').length,
      medium: alerts.filter(a => a.severity === 'medium').length,
      low: alerts.filter(a => a.severity === 'low').length,
    };
  }

  /**
   * Check performance thresholds
   */
  private async checkThresholds(): Promise<void> {
    try {
      // Get current metrics (removed circular dependency with performanceMonitor)
      const cacheStats = advancedCacheManager.getStats();
      const requestStats = await consolidatedCache.getMetrics();
      const aiServiceStats = aiServiceMonitor.getMetrics();

      // Check each threshold
      for (const threshold of Array.from(this.thresholds.values())) {
        if (!threshold.enabled) continue;

        const currentValue = this.getMetricValue(threshold.metric, {
          cache: cacheStats,
          requests: requestStats,
          aiService: aiServiceStats
        });

        if (currentValue !== null && this.evaluateThreshold(currentValue, threshold)) {
          await this.createThresholdAlert(threshold, currentValue);
        }
      }
    } catch (error) {
      console.error('Error checking thresholds:', error);
    }
  }

  /**
   * Get metric value from collected stats
   */
  private getMetricValue(metric: string, stats: any): number | null {
    switch (metric) {
      case 'response_time':
        // Use AI service response time as proxy since performance monitor is not available
        return stats.aiService?.averageResponseTime || 0;
      case 'error_rate':
        return stats.aiService?.totalRequests > 0 
          ? (stats.aiService.failedRequests / stats.aiService.totalRequests) * 100 
          : 0;
      case 'cache_hit_rate':
        return (stats.cache?.hitRate || 0) * 100;
      case 'memory_usage':
        return stats.cache?.memoryUsage || 0;
      case 'throughput':
        return stats.requests?.throughputPerSecond || 0;
      case 'queue_length':
        return 0; // No longer tracked by consolidatedCache
      case 'active_requests':
        return 0; // No longer tracked by consolidatedCache
      default:
        return null;
    }
  }

  /**
   * Evaluate if threshold is breached
   */
  private evaluateThreshold(value: number, threshold: AlertThreshold): boolean {
    switch (threshold.operator) {
      case 'gt': return value > threshold.value;
      case 'gte': return value >= threshold.value;
      case 'lt': return value < threshold.value;
      case 'lte': return value <= threshold.value;
      case 'eq': return value === threshold.value;
      default: return false;
    }
  }

  /**
   * Create threshold breach alert
   */
  private async createThresholdAlert(threshold: AlertThreshold, currentValue: number): Promise<void> {
    // Check if we already have a recent alert for this threshold
    const recentAlert = Array.from(this.alerts.values()).find(alert => 
      alert.type === 'THRESHOLD' &&
      alert.metadata.metric === threshold.metric &&
      !alert.resolved &&
      (Date.now() - alert.timestamp) < threshold.duration
    );

    if (recentAlert) return; // Don't spam alerts

    await this.createAlert({
      title: `Threshold Breach: ${threshold.metric}`,
      message: `${threshold.metric} is ${currentValue} (threshold: ${threshold.operator} ${threshold.value})`,
      severity: threshold.severity,
      type: 'THRESHOLD',
      source: 'threshold_monitor',
      metadata: {
        metric: threshold.metric,
        currentValue,
        threshold: threshold.value,
        operator: threshold.operator
      }
    });
  }

  /**
   * Send notifications for an alert
   */
  private async sendNotifications(alert: PerformanceAlert): Promise<void> {
    const applicableChannels = Array.from(this.channels.values()).filter(channel =>
      channel.enabled &&
      channel.severityFilter.includes(alert.severity) &&
      this.canSendNotification(channel)
    );

    for (const channel of applicableChannels) {
      try {
        await this.sendToChannel(channel, alert);
        channel.lastNotification = Date.now();
      } catch (error) {
        console.error(`Failed to send notification to ${channel.type}:`, error);
      }
    }
  }

  /**
   * Check if we can send notification (rate limiting)
   */
  private canSendNotification(channel: NotificationChannel): boolean {
    if (channel.rateLimitMinutes === 0) return true;
    
    const timeSinceLastNotification = Date.now() - channel.lastNotification;
    const rateLimitMs = channel.rateLimitMinutes * 60 * 1000;
    
    return timeSinceLastNotification >= rateLimitMs;
  }

  /**
   * Send notification to specific channel
   */
  private async sendToChannel(channel: NotificationChannel, alert: PerformanceAlert): Promise<void> {
    switch (channel.type) {
      case 'console':
        console.log(`🚨 ALERT [${alert.severity.toUpperCase()}]: ${alert.title} - ${alert.message}`);
        break;
      
      case 'webhook':
        if (channel.config.url) {
          await fetch(channel.config.url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              alert,
              timestamp: new Date().toISOString(),
              source: 'faafo-performance-monitor'
            })
          });
        }
        break;
      
      case 'database':
        // Store alert in database for dashboard display
        // This would integrate with your database layer
        break;
      
      default:
        console.log(`Notification channel ${channel.type} not implemented yet`);
    }
  }

  /**
   * Setup escalation for an alert
   */
  private setupEscalation(alert: PerformanceAlert): void {
    const rule = this.escalationRules.get(alert.severity);
    if (!rule || !rule.enabled) return;

    const timer = setTimeout(async () => {
      if (!alert.acknowledged && !alert.resolved && alert.escalationLevel < rule.maxEscalationLevel) {
        await this.escalateAlert(alert, rule);
      }
    }, rule.timeToEscalate);

    this.escalationTimers.set(alert.id, timer);
  }

  /**
   * Cancel escalation for an alert
   */
  private cancelEscalation(alertId: string): void {
    const timer = this.escalationTimers.get(alertId);
    if (timer) {
      clearTimeout(timer);
      this.escalationTimers.delete(alertId);
    }
  }

  /**
   * Escalate an alert
   */
  private async escalateAlert(alert: PerformanceAlert, rule: EscalationRule): Promise<void> {
    alert.escalated = true;
    alert.escalationLevel++;

    const escalatedAlert: PerformanceAlert = {
      ...alert,
      id: this.generateAlertId(),
      title: `ESCALATED (Level ${alert.escalationLevel}): ${alert.title}`,
      message: `This alert has been escalated due to lack of acknowledgment. Original: ${alert.message}`,
      timestamp: Date.now(),
      escalationLevel: alert.escalationLevel
    };

    this.alerts.set(escalatedAlert.id, escalatedAlert);

    // Send to escalation channels
    const escalationChannels = rule.escalationChannels
      .map(id => this.channels.get(id))
      .filter(channel => channel && channel.enabled) as NotificationChannel[];

    for (const channel of escalationChannels) {
      try {
        await this.sendToChannel(channel, escalatedAlert);
      } catch (error) {
        console.error(`Failed to send escalation to ${channel.type}:`, error);
      }
    }

    this.emit('alert_escalated', escalatedAlert);
    console.log(`🚨 Alert escalated: ${alert.title} (Level ${alert.escalationLevel})`);

    // Setup next escalation if not at max level
    if (alert.escalationLevel < rule.maxEscalationLevel) {
      this.setupEscalation(escalatedAlert);
    }
  }

  /**
   * Setup default thresholds
   */
  private setupDefaultThresholds(): void {
    const defaultThresholds: AlertThreshold[] = [
      {
        metric: 'response_time',
        operator: 'gt',
        value: 2000, // 2 seconds
        severity: 'high',
        duration: 300000, // 5 minutes
        enabled: true
      },
      {
        metric: 'error_rate',
        operator: 'gt',
        value: 5, // 5%
        severity: 'critical',
        duration: 60000, // 1 minute
        enabled: true
      },
      {
        metric: 'cache_hit_rate',
        operator: 'lt',
        value: 70, // 70%
        severity: 'medium',
        duration: 600000, // 10 minutes
        enabled: true
      },
      {
        metric: 'memory_usage',
        operator: 'gt',
        value: 100 * 1024 * 1024, // 100MB
        severity: 'high',
        duration: 300000, // 5 minutes
        enabled: true
      },
      {
        metric: 'queue_length',
        operator: 'gt',
        value: 50,
        severity: 'medium',
        duration: 120000, // 2 minutes
        enabled: true
      }
    ];

    defaultThresholds.forEach(threshold => {
      this.thresholds.set(threshold.metric, threshold);
    });
  }

  /**
   * Setup default notification channels
   */
  private setupDefaultChannels(): void {
    // Console channel for development
    this.channels.set('console', {
      id: 'console',
      type: 'console',
      config: {},
      enabled: true,
      severityFilter: ['low', 'medium', 'high', 'critical'],
      rateLimitMinutes: 1,
      lastNotification: 0
    });

    // Database channel for dashboard
    this.channels.set('database', {
      id: 'database',
      type: 'database',
      config: {},
      enabled: true,
      severityFilter: ['low', 'medium', 'high', 'critical'],
      rateLimitMinutes: 0,
      lastNotification: 0
    });
  }

  /**
   * Setup default escalation rules
   */
  private setupDefaultEscalationRules(): void {
    this.escalationRules.set('critical', {
      id: 'critical_escalation',
      severity: 'critical',
      timeToEscalate: 300000, // 5 minutes
      maxEscalationLevel: 3,
      escalationChannels: ['console', 'database'],
      enabled: true
    });

    this.escalationRules.set('high', {
      id: 'high_escalation',
      severity: 'high',
      timeToEscalate: 900000, // 15 minutes
      maxEscalationLevel: 2,
      escalationChannels: ['console', 'database'],
      enabled: true
    });
  }

  /**
   * Generate unique alert ID
   */
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const enhancedAlertingService = new EnhancedAlertingService();
