import { NextResponse } from 'next/server';
// Standard API response types
export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

export interface ApiErrorResponse {
  success: false;
  error: string;
  details?: string[];
  code?: string;
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

// Response builder functions
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  meta?: ApiSuccessResponse<T>['meta']
): NextResponse<ApiSuccessResponse<T>> {
  const response: ApiSuccessResponse<T> = {
    success: true,
    data,
    ...(message && { message }),
    ...(meta && { meta }),
  };
  
  return NextResponse.json(response);
}

export function createErrorResponse(
  error: string,
  status: number = 400,
  details?: string[],
  code?: string
): NextResponse<ApiErrorResponse> {
  const response: ApiErrorResponse = {
    success: false,
    error,
    ...(details && { details }),
    ...(code && { code }),
  };
  
  return NextResponse.json(response, { status });
}

// Common error responses
export const ErrorResponses = {
  unauthorized: () => createErrorResponse('Unauthorized', 401, undefined, 'UNAUTHORIZED'),
  forbidden: () => createErrorResponse('Forbidden', 403, undefined, 'FORBIDDEN'),
  notFound: (resource?: string) => 
    createErrorResponse(
      resource ? `${resource} not found` : 'Resource not found',
      404,
      undefined,
      'NOT_FOUND'
    ),
  badRequest: (message?: string, details?: string[]) =>
    createErrorResponse(
      message || 'Bad request',
      400,
      details,
      'BAD_REQUEST'
    ),
  validationError: (details: string[]) =>
    createErrorResponse(
      'Validation failed',
      400,
      details,
      'VALIDATION_ERROR'
    ),
  conflict: (message?: string) =>
    createErrorResponse(
      message || 'Resource already exists',
      409,
      undefined,
      'CONFLICT'
    ),
  tooManyRequests: () =>
    createErrorResponse(
      'Too many requests',
      429,
      undefined,
      'RATE_LIMIT_EXCEEDED'
    ),
  internalError: (message?: string) =>
    createErrorResponse(
      message || 'Internal server error',
      500,
      undefined,
      'INTERNAL_ERROR'
    ),
  serviceUnavailable: () =>
    createErrorResponse(
      'Service temporarily unavailable',
      503,
      undefined,
      'SERVICE_UNAVAILABLE'
    ),
};

// Pagination helper
export function createPaginationMeta(
  page: number,
  limit: number,
  total: number
) {
  return {
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit),
  };
}

// Error handling wrapper for API routes
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse<ApiResponse<R>>>
) {
  return async (...args: T): Promise<NextResponse<ApiResponse<R>>> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);
      
      if (error instanceof Error) {
        // Handle validation errors
        if (error instanceof Error ? error.message : String(error).includes('Validation failed')) {
          return ErrorResponses.validationError([error instanceof Error ? error.message : String(error)]);
        }
        
        // Handle known errors
        if (error instanceof Error ? error.message : String(error).includes('Unauthorized')) {
          return ErrorResponses.unauthorized();
        }
        
        if (error instanceof Error ? error.message : String(error).includes('Not found')) {
          return ErrorResponses.notFound();
        }
        
        // Handle database errors
        if (error instanceof Error ? error.message : String(error).includes('Unique constraint')) {
          return ErrorResponses.conflict('Resource already exists');
        }
        
        // Generic error with message
        return ErrorResponses.internalError(
          process.env.NODE_ENV === 'development' ? error instanceof Error ? error.message : String(error) : undefined
        );
      }
      
      return ErrorResponses.internalError();
    }
  };
}

// Type guards
export function isSuccessResponse<T>(
  response: ApiResponse<T>
): response is ApiSuccessResponse<T> {
  return response.success === true;
}

export function isErrorResponse(
  response: ApiResponse
): response is ApiErrorResponse {
  return response.success === false;
}
