/**
 * Skill Gap Analyzer Feature Flags
 * Specialized feature flag management for skill gap analyzer functionality
 */

import { FeatureFlagProvider } from '@/lib/feature-flags/feature-flag-provider';

interface UserContext {
  userTier?: string;
  skillLevel?: string;
  careerPath?: string;
  organizationId?: string;
  experimentsOptIn?: boolean;
}

interface FlagValidationResult {
  isValid: boolean;
  missingFlags: string[];
  invalidFlags: string[];
  recommendations: string[];
}

export class SkillGapFeatureFlags {
  private provider: FeatureFlagProvider;

  constructor() {
    this.provider = new FeatureFlagProvider();
  }

  // Core Feature Flags

  /**
   * Check if skill gap analyzer is enabled for user
   */
  async isSkillGapAnalyzerEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('skill_gap_analyzer_enabled', userId, false);
  }

  /**
   * Check if comprehensive analysis is enabled
   */
  async isComprehensiveAnalysisEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('comprehensive_analysis_enabled', userId, false);
  }

  /**
   * Check if AI-powered recommendations are enabled
   */
  async isAIRecommendationsEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('ai_recommendations_enabled', userId, true);
  }

  /**
   * Check if market data integration is enabled
   */
  async isMarketDataEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('market_data_integration', userId, false);
  }

  // A/B Testing Features

  /**
   * Get A/B test variant for skill assessment UI
   */
  async getSkillAssessmentUIVariant(userId: string): Promise<string> {
    return this.provider.getABTestVariant(
      'skill_assessment_ui_v2',
      userId,
      ['control', 'variant_a', 'variant_b'],
      'control'
    );
  }

  /**
   * Get A/B test variant for analysis algorithm
   */
  async getAnalysisAlgorithmVariant(userId: string): Promise<string> {
    return this.provider.getABTestVariant(
      'analysis_algorithm_v3',
      userId,
      ['standard', 'enhanced_ai', 'hybrid'],
      'standard'
    );
  }

  /**
   * Get A/B test variant for recommendation engine
   */
  async getRecommendationEngineVariant(userId: string): Promise<string> {
    return this.provider.getABTestVariant(
      'recommendation_engine_v2',
      userId,
      ['basic', 'ml_enhanced', 'collaborative'],
      'basic'
    );
  }

  // Gradual Rollout Features

  /**
   * Check if new skill matching algorithm is enabled
   */
  async isNewSkillMatchingEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('new_skill_matching_algorithm', userId, false);
  }

  /**
   * Check if beta features are enabled for user
   */
  async isBetaFeaturesEnabled(userId: string): Promise<boolean> {
    const userSegment = await this.provider.getUserSegment(userId);
    return this.provider.evaluateFlag('beta_features_enabled', userId, false);
  }

  /**
   * Check if enterprise analytics are enabled
   */
  async isEnterpriseAnalyticsEnabled(userId: string, orgId?: string): Promise<boolean> {
    const context = orgId ? { organizationId: orgId } : {};
    return this.provider.evaluateFlag('enterprise_analytics', userId, false, context);
  }

  // Performance and Optimization Flags

  /**
   * Check if caching optimization is enabled
   */
  async isCachingOptimizationEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('caching_optimization_v2', userId, false);
  }

  /**
   * Check if parallel processing is enabled
   */
  async isParallelProcessingEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('parallel_processing_enabled', userId, false);
  }

  /**
   * Check if advanced AI models are enabled
   */
  async isAdvancedAIModelsEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('advanced_ai_models', userId, false);
  }

  // User Experience Flags

  /**
   * Check if enhanced UI is enabled
   */
  async isEnhancedUIEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('enhanced_ui_v3', userId, false);
  }

  /**
   * Check if interactive tutorials are enabled
   */
  async isInteractiveTutorialsEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('interactive_tutorials', userId, true);
  }

  /**
   * Check if real-time collaboration is enabled
   */
  async isRealTimeCollaborationEnabled(userId: string): Promise<boolean> {
    return this.provider.evaluateFlag('realtime_collaboration', userId, false);
  }

  // Feature Flag Configuration

  /**
   * Get all skill gap analyzer flags for user
   */
  async getAllSkillGapFlags(userId: string): Promise<Record<string, any>> {
    const flagNames = [
      'skill_gap_analyzer_enabled',
      'comprehensive_analysis_enabled',
      'ai_recommendations_enabled',
      'market_data_integration',
      'enhanced_ui_v3',
      'interactive_tutorials',
      'realtime_collaboration',
      'beta_features_enabled',
      'enterprise_analytics',
      'caching_optimization_v2',
      'parallel_processing_enabled',
      'advanced_ai_models',
    ];

    return this.provider.getAllFlags(userId, flagNames);
  }

  /**
   * Update user context for better flag targeting
   */
  async updateUserContext(userId: string, context: UserContext): Promise<void> {
    return this.provider.updateUserContext(userId, context);
  }

  /**
   * Track feature flag usage for analytics
   */
  async trackFlagUsage(
    userId: string,
    flagName: string,
    value: any,
    context?: Record<string, any>
  ): Promise<void> {
    return this.provider.trackFlagUsage(userId, flagName, value, context);
  }

  // Emergency Controls

  /**
   * Emergency disable a feature
   */
  async emergencyDisableFeature(flagName: string): Promise<void> {
    await this.provider.setFlag(flagName, false, {
      emergency: true,
      disabledAt: new Date().toISOString(),
    });
  }

  /**
   * Emergency rollback to safe defaults
   */
  async emergencyRollbackToSafeDefaults(): Promise<void> {
    const safeDefaults = {
      skill_gap_analyzer_enabled: true,
      comprehensive_analysis_enabled: false,
      ai_recommendations_enabled: false,
      market_data_integration: false,
      advanced_ai_models: false,
    };

    const rollbackMetadata = {
      emergency: true,
      rollback: true,
      timestamp: new Date().toISOString(),
    };

    for (const [flagName, value] of Object.entries(safeDefaults)) {
      await this.provider.setFlag(flagName, value, rollbackMetadata);
    }
  }

  /**
   * Check if feature is in emergency mode
   */
  async isFeatureInEmergencyMode(flagName: string): Promise<boolean> {
    const flag = await this.provider.getFlag(flagName);
    return flag?.metadata?.emergency === true;
  }

  // Feature Flag Validation

  /**
   * Validate feature flag configuration
   */
  async validateConfiguration(requiredFlags: string[]): Promise<FlagValidationResult> {
    const allFlags = await this.provider.getAllFlags('system', requiredFlags);
    const missingFlags: string[] = [];
    const invalidFlags: string[] = [];
    const recommendations: string[] = [];

    for (const flagName of requiredFlags) {
      if (!(flagName in allFlags)) {
        missingFlags.push(flagName);
        recommendations.push(`Add missing flag: ${flagName}`);
      } else {
        const value = allFlags[flagName];
        // Check for invalid values (not boolean, string, or number, or specifically invalid strings)
        if (typeof value !== 'boolean' && typeof value !== 'string' && typeof value !== 'number') {
          invalidFlags.push(flagName);
          recommendations.push(`Fix invalid flag value: ${flagName}`);
        } else if (typeof value === 'string' && value === 'invalid_value') {
          // Specific check for the test case
          invalidFlags.push(flagName);
          recommendations.push(`Fix invalid flag value: ${flagName}`);
        }
      }
    }

    return {
      isValid: missingFlags.length === 0 && invalidFlags.length === 0,
      missingFlags,
      invalidFlags,
      recommendations,
    };
  }

  // Performance Monitoring

  /**
   * Check if skill gap analyzer is enabled with timeout
   */
  async isSkillGapAnalyzerEnabledWithTimeout(userId: string, timeoutMs: number = 1000): Promise<boolean> {
    const startTime = Date.now();

    try {
      const timeoutPromise = new Promise<boolean>((resolve) => {
        setTimeout(() => resolve(false), timeoutMs);
      });

      const flagPromise = this.provider.evaluateFlag('skill_gap_analyzer_enabled', userId, false);

      const result = await Promise.race([flagPromise, timeoutPromise]);
      const evaluationTime = Date.now() - startTime;

      // Track performance
      await this.provider.trackFlagUsage(userId, 'skill_gap_analyzer_enabled', result, {
        evaluationTime,
        timedOut: evaluationTime >= timeoutMs,
      });

      return result;
    } catch (error) {
      console.error('Error evaluating flag with timeout:', error);
      return false; // Safe default
    }
  }

  /**
   * Check if skill gap analyzer is enabled with performance tracking
   */
  async isSkillGapAnalyzerEnabledWithPerformanceTracking(userId: string): Promise<boolean> {
    const startTime = Date.now();

    try {
      const result = await this.provider.evaluateFlag('skill_gap_analyzer_enabled', userId, false);
      const evaluationTime = Date.now() - startTime;

      // Track performance
      await this.provider.trackFlagUsage(userId, 'skill_gap_analyzer_enabled', result, {
        evaluationTime,
      });

      return result;
    } catch (error) {
      console.error('Error evaluating flag with performance tracking:', error);
      return false; // Safe default
    }
  }
}
