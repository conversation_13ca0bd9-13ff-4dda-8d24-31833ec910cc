import swaggerJSDoc from 'swagger-jsdoc';
const options: swaggerJSDoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'FAAFO Career Platform API',
      version: '1.0.0',
      description: 'Comprehensive API for the FAAFO Career Platform - AI-powered career transition and learning management system',
      contact: {
        name: 'FAAFO Development Team',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: process.env.NEXTAUTH_URL || 'http://localhost:3000',
        description: 'Development server',
      },
      {
        url: 'https://faafo-career-platform.vercel.app',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        sessionAuth: {
          type: 'apiKey',
          in: 'cookie',
          name: 'next-auth.session-token',
        },
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            email: { type: 'string', format: 'email' },
            name: { type: 'string' },
            image: { type: 'string', format: 'uri' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        Assessment: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            userId: { type: 'string', format: 'uuid' },
            status: { 
              type: 'string', 
              enum: ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED'] 
            },
            startedAt: { type: 'string', format: 'date-time' },
            completedAt: { type: 'string', format: 'date-time' },
            responses: {
              type: 'array',
              items: { $ref: '#/components/schemas/AssessmentResponse' }
            },
          },
        },
        AssessmentResponse: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            questionKey: { type: 'string' },
            answerValue: { type: 'object' },
            createdAt: { type: 'string', format: 'date-time' },
          },
        },
        LearningResource: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            title: { type: 'string' },
            description: { type: 'string' },
            type: { 
              type: 'string',
              enum: ['ARTICLE', 'VIDEO', 'COURSE', 'BOOK', 'PODCAST', 'TOOL', 'CERTIFICATION']
            },
            url: { type: 'string', format: 'uri' },
            author: { type: 'string' },
            duration: { type: 'integer' },
            skillLevel: {
              type: 'string',
              enum: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
            },
            category: {
              type: 'string',
              enum: ['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP', 'UX_UI_DESIGN', 'PRODUCT_MANAGEMENT', 'DEVOPS']
            },
            isActive: { type: 'boolean' },
            averageRating: { type: 'number', minimum: 0, maximum: 5 },
            createdAt: { type: 'string', format: 'date-time' },
          },
        },
        LearningPath: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            title: { type: 'string' },
            description: { type: 'string' },
            slug: { type: 'string' },
            difficulty: {
              type: 'string',
              enum: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
            },
            estimatedHours: { type: 'integer' },
            category: {
              type: 'string',
              enum: ['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP', 'UX_UI_DESIGN', 'PRODUCT_MANAGEMENT', 'DEVOPS']
            },
            isActive: { type: 'boolean' },
            stepCount: { type: 'integer' },
            enrollmentCount: { type: 'integer' },
            skills: {
              type: 'array',
              items: { $ref: '#/components/schemas/Skill' }
            },
            steps: {
              type: 'array',
              items: { $ref: '#/components/schemas/LearningPathStep' }
            },
          },
        },
        LearningPathStep: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            title: { type: 'string' },
            description: { type: 'string' },
            stepOrder: { type: 'integer' },
            stepType: {
              type: 'string',
              enum: ['RESOURCE', 'QUIZ', 'ASSIGNMENT', 'PROJECT', 'DISCUSSION', 'REFLECTION', 'EXTERNAL_LINK', 'VIDEO', 'READING', 'PRACTICE']
            },
            estimatedMinutes: { type: 'integer' },
            isRequired: { type: 'boolean' },
          },
        },
        CareerPath: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            name: { type: 'string' },
            slug: { type: 'string' },
            overview: { type: 'string' },
            averageSalary: { type: 'integer' },
            jobGrowthRate: { type: 'number' },
            isActive: { type: 'boolean' },
            relatedSkills: {
              type: 'array',
              items: { $ref: '#/components/schemas/Skill' }
            },
          },
        },
        Skill: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            name: { type: 'string' },
            description: { type: 'string' },
            category: { type: 'string' },
          },
        },
        ForumPost: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            title: { type: 'string' },
            content: { type: 'string' },
            categoryId: { type: 'string', format: 'uuid' },
            authorId: { type: 'string', format: 'uuid' },
            isActive: { type: 'boolean' },
            isPinned: { type: 'boolean' },
            viewCount: { type: 'integer' },
            createdAt: { type: 'string', format: 'date-time' },
          },
        },
        AIResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            cached: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
        ResumeAnalysisResult: {
          type: 'object',
          properties: {
            strengths: {
              type: 'array',
              items: { type: 'string' }
            },
            weaknesses: {
              type: 'array',
              items: { type: 'string' }
            },
            suggestions: {
              type: 'array',
              items: { type: 'string' }
            },
            skillsIdentified: {
              type: 'array',
              items: { type: 'string' }
            },
            experienceLevel: {
              type: 'string',
              enum: ['entry', 'mid', 'senior', 'executive']
            },
            industryFit: {
              type: 'array',
              items: { type: 'string' }
            },
            overallScore: {
              type: 'number',
              minimum: 1,
              maximum: 100
            },
          },
        },
        CareerRecommendation: {
          type: 'object',
          properties: {
            careerPath: { type: 'string' },
            matchScore: { type: 'number', minimum: 1, maximum: 100 },
            reasoning: { type: 'string' },
            requiredSkills: {
              type: 'array',
              items: { type: 'string' }
            },
            timeToTransition: { type: 'string' },
            salaryRange: { type: 'string' },
            growthPotential: { type: 'string' },
          },
        },
        Error: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: false },
            error: { type: 'string' },
            details: { type: 'array', items: { type: 'object' } },
          },
        },
        PaginatedResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                items: { type: 'array' },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'integer' },
                    limit: { type: 'integer' },
                    total: { type: 'integer' },
                    pages: { type: 'integer' },
                  },
                },
              },
            },
          },
        },
      },
      responses: {
        UnauthorizedError: {
          description: 'Authentication required',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' },
              example: {
                success: false,
                error: 'Authentication required'
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Insufficient permissions',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' },
              example: {
                success: false,
                error: 'Admin access required'
              }
            }
          }
        },
        NotFoundError: {
          description: 'Resource not found',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' },
              example: {
                success: false,
                error: 'Resource not found'
              }
            }
          }
        },
        ValidationError: {
          description: 'Invalid request data',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' },
              example: {
                success: false,
                error: 'Invalid request data',
                details: [
                  {
                    field: 'email',
                    message: 'Invalid email format'
                  }
                ]
              }
            }
          }
        },
        RateLimitError: {
          description: 'Rate limit exceeded',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' },
              example: {
                success: false,
                error: 'Rate limit exceeded. Please try again later.'
              }
            }
          }
        },
      },
      parameters: {
        PageParam: {
          name: 'page',
          in: 'query',
          description: 'Page number for pagination',
          schema: {
            type: 'integer',
            minimum: 1,
            default: 1
          }
        },
        LimitParam: {
          name: 'limit',
          in: 'query',
          description: 'Number of items per page',
          schema: {
            type: 'integer',
            minimum: 1,
            maximum: 100,
            default: 10
          }
        },
        SearchParam: {
          name: 'search',
          in: 'query',
          description: 'Search query string',
          schema: {
            type: 'string',
            maxLength: 200
          }
        },
      },
    },
    security: [
      {
        sessionAuth: []
      }
    ],
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and session management'
      },
      {
        name: 'Assessments',
        description: 'Career assessment operations'
      },
      {
        name: 'Learning Resources',
        description: 'Learning resource management'
      },
      {
        name: 'Learning Paths',
        description: 'Structured learning path operations'
      },
      {
        name: 'Career Paths',
        description: 'Career path information and recommendations'
      },
      {
        name: 'AI Services',
        description: 'AI-powered features and recommendations'
      },
      {
        name: 'Forum',
        description: 'Community forum operations'
      },
      {
        name: 'User Progress',
        description: 'User learning progress and analytics'
      },
      {
        name: 'Admin',
        description: 'Administrative operations'
      },
    ],
  },
  apis: [
    './src/app/api/**/*.ts',
    './src/lib/swagger/paths/*.ts',
  ],
};

export const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
