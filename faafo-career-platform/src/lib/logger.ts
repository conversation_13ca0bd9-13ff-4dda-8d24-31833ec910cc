/**
 * Centralized Logging Service
 * Provides structured logging with different levels and contexts
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, any>;
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

class Logger {
  private isProduction = process.env.NODE_ENV === 'production';
  private logLevel: LogLevel = (process.env.LOG_LEVEL as LogLevel) || (this.isProduction ? 'warn' : 'debug');
  private context: LogContext = {};

  private levelPriority: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3
  };

  /**
   * Set global context for all log entries
   */
  setContext(context: Partial<LogContext>): void {
    this.context = { ...this.context, ...context };
  }

  /**
   * Clear global context
   */
  clearContext(): void {
    this.context = {};
  }

  /**
   * Create a child logger with additional context
   */
  child(context: Partial<LogContext>): Logger {
    const childLogger = new Logger();
    childLogger.context = { ...this.context, ...context };
    childLogger.logLevel = this.logLevel;
    childLogger.isProduction = this.isProduction;
    return childLogger;
  }

  /**
   * Check if a log level should be logged
   */
  private shouldLog(level: LogLevel): boolean {
    return this.levelPriority[level] >= this.levelPriority[this.logLevel];
  }

  /**
   * Format log entry for output
   */
  private formatLogEntry(entry: LogEntry): string {
    if (this.isProduction) {
      // Structured JSON logging for production
      return JSON.stringify(entry);
    } else {
      // Human-readable logging for development
      const timestamp = new Date(entry.timestamp).toLocaleTimeString();
      const level = entry.level.toUpperCase().padEnd(5);
      const context = entry.context ? ` [${Object.entries(entry.context).map(([k, v]) => `${k}=${v}`).join(', ')}]` : '';
      return `${timestamp} ${level} ${entry.message}${context}`;
    }
  }

  /**
   * Write log entry to appropriate output
   */
  private writeLog(entry: LogEntry): void {
    const formatted = this.formatLogEntry(entry);

    switch (entry.level) {
      case 'debug':
        console.debug(formatted);
        break;
      case 'info':
        console.info(formatted);
        break;
      case 'warn':
        console.warn(formatted);
        break;
      case 'error':
        console.error(formatted);
        break;
    }

    // In production, also send to external logging service
    if (this.isProduction && entry.level === 'error') {
      this.sendToExternalService(entry);
    }
  }

  /**
   * Send error logs to external service (e.g., Sentry, DataDog)
   */
  private sendToExternalService(entry: LogEntry): void {
    // This would integrate with your external logging service
    // For now, we'll use the error tracker
    try {
      if (entry.error) {
        const error = new Error(entry.error instanceof Error ? entry.error.message : String(entry.error));
        if (entry.error instanceof Error) {
          error.name = entry.error.name;
          error.stack = entry.error.stack;
        }

        // Use the error tracker we created earlier
        if (typeof window !== 'undefined' && (window as any).Sentry) {
          (window as any).Sentry.captureException(error, {
            extra: {
              logEntry: entry,
              context: entry.context
            },
            tags: {
              source: 'logger',
              component: entry.context?.component || 'unknown'
            }
          });
        }
      }
    } catch (error) {
      // Fallback - don't let logging errors break the application
      console.error('Failed to send log to external service:', error);
    }
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: Partial<LogContext>): void {
    if (!this.shouldLog('debug')) return;

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'debug',
      message,
      context: { ...this.context, ...context }
    });
  }

  /**
   * Log info message
   */
  info(message: string, context?: Partial<LogContext>): void {
    if (!this.shouldLog('info')) return;

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'info',
      message,
      context: { ...this.context, ...context }
    });
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: Partial<LogContext>): void {
    if (!this.shouldLog('warn')) return;

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'warn',
      message,
      context: { ...this.context, ...context }
    });
  }

  /**
   * Log error message
   */
  error(message: string, error?: Error, context?: Partial<LogContext>): void {
    if (!this.shouldLog('error')) return;

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'error',
      message,
      context: { ...this.context, ...context },
      error: error ? {
        name: error instanceof Error ? error.name : String(error),
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : String(error)
      } : undefined
    });
  }

  /**
   * Log API request/response
   */
  api(method: string, url: string, statusCode: number, duration: number, context?: Partial<LogContext>): void {
    const level: LogLevel = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
    
    this.writeLog({
      timestamp: new Date().toISOString(),
      level,
      message: `${method} ${url} ${statusCode} ${duration}ms`,
      context: {
        ...this.context,
        ...context,
        metadata: {
          method,
          url,
          statusCode: statusCode.toString(),
          duration: duration.toString()
        }
      }
    });
  }

  /**
   * Log database operation
   */
  database(operation: string, table: string, duration: number, context?: Partial<LogContext>): void {
    this.debug(`DB ${operation} ${table} ${duration}ms`, {
      ...context,
      metadata: {
        operation,
        table,
        duration: duration.toString()
      }
    });
  }

  /**
   * Log authentication event
   */
  auth(event: string, userId?: string, success: boolean = true, context?: Partial<LogContext>): void {
    const level: LogLevel = success ? 'info' : 'warn';
    
    this.writeLog({
      timestamp: new Date().toISOString(),
      level,
      message: `Auth ${event} ${success ? 'success' : 'failed'}${userId ? ` for user ${userId}` : ''}`,
      context: {
        ...this.context,
        ...context,
        userId,
        metadata: {
          event,
          success: success.toString()
        }
      }
    });
  }

  /**
   * Log performance metric
   */
  performance(metric: string, value: number, unit: string = 'ms', context?: Partial<LogContext>): void {
    this.info(`Performance ${metric}: ${value}${unit}`, {
      ...context,
      metadata: {
        metric,
        value: value.toString(),
        unit
      }
    });
  }
}

// Create singleton logger instance
const logger = new Logger();

// Convenience functions for common logging patterns
export const log = {
  debug: (message: string, context?: Partial<LogContext>) => logger.debug(message, context),
  info: (message: string, context?: Partial<LogContext>) => logger.info(message, context),
  warn: (message: string, context?: Partial<LogContext>) => logger.warn(message, context),
  error: (message: string, error?: Error, context?: Partial<LogContext>) => logger.error(message, error, context),
  api: (method: string, url: string, statusCode: number, duration: number, context?: Partial<LogContext>) => 
    logger.api(method, url, statusCode, duration, context),
  database: (operation: string, table: string, duration: number, context?: Partial<LogContext>) => 
    logger.database(operation, table, duration, context),
  auth: (event: string, userId?: string, success?: boolean, context?: Partial<LogContext>) => 
    logger.auth(event, userId, success, context),
  performance: (metric: string, value: number, unit?: string, context?: Partial<LogContext>) => 
    logger.performance(metric, value, unit, context)
};

export { logger, Logger };
export type { LogLevel, LogContext, LogEntry };
