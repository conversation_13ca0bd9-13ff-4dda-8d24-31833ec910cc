import DOMPurify from 'isomorphic-dompurify';
import { z } from 'zod';
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
}

export interface ValidationRule {
  field: string;
  schema: z.ZodSchema;
  sanitize?: boolean;
  required?: boolean;
}

export class DataValidationPipeline {
  private rules: ValidationRule[] = [];

  constructor(rules: ValidationRule[] = []) {
    this.rules = rules;
  }

  addRule(rule: ValidationRule): void {
    this.rules.push(rule);
  }

  /**
   * Comprehensive validation and sanitization pipeline
   */
  async validate(data: any): Promise<ValidationResult> {
    const errors: string[] = [];
    const sanitizedData: any = { ...data };

    // Step 1: Basic type and structure validation
    if (!data || typeof data !== 'object') {
      return {
        isValid: false,
        errors: ['Invalid data structure provided']
      };
    }

    // Step 2: Apply field-specific validation rules
    for (const rule of this.rules) {
      const fieldValue = data[rule.field];

      // Check required fields
      if (rule.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {
        errors.push(`${rule.field} is required`);
        continue;
      }

      // Skip validation for optional empty fields
      if (!rule.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {
        continue;
      }

      // Apply Zod schema validation
      try {
        const validatedValue = rule.schema.parse(fieldValue);
        
        // Apply sanitization if requested
        if (rule.sanitize && typeof validatedValue === 'string') {
          sanitizedData[rule.field] = this.sanitizeString(validatedValue);
        } else {
          sanitizedData[rule.field] = validatedValue;
        }
      } catch (error) {
        if (error instanceof z.ZodError) {
          errors.push(...error.errors.map(err => `${rule.field}: ${err.message}`));
        } else {
          errors.push(`${rule.field}: Validation failed`);
        }
      }
    }

    // Step 3: Additional security checks
    const securityErrors = this.performSecurityChecks(sanitizedData);
    errors.push(...securityErrors);

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: errors.length === 0 ? sanitizedData : undefined
    };
  }

  /**
   * Sanitize string input to prevent XSS and other attacks
   */
  private sanitizeString(input: string): string {
    // Remove HTML tags and potentially dangerous content
    const cleaned = DOMPurify.sanitize(input, { 
      ALLOWED_TAGS: [], 
      ALLOWED_ATTR: [] 
    });
    
    // Additional sanitization for common attack patterns
    return cleaned
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocols
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  /**
   * Perform additional security checks
   */
  private performSecurityChecks(data: any): string[] {
    const errors: string[] = [];

    // Check for SQL injection patterns
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/|;)/,
      /(\b(OR|AND)\b.*=.*)/i
    ];

    // Check for XSS patterns
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe/gi,
      /<object/gi,
      /<embed/gi
    ];

    // Recursively check all string values
    this.checkObjectForPatterns(data, sqlPatterns, 'Potential SQL injection detected', errors);
    this.checkObjectForPatterns(data, xssPatterns, 'Potential XSS attack detected', errors);

    return errors;
  }

  /**
   * Recursively check object for dangerous patterns
   */
  private checkObjectForPatterns(obj: any, patterns: RegExp[], errorMessage: string, errors: string[]): void {
    if (typeof obj === 'string') {
      for (const pattern of patterns) {
        if (pattern.test(obj)) {
          errors.push(errorMessage);
          break;
        }
      }
    } else if (Array.isArray(obj)) {
      obj.forEach(item => this.checkObjectForPatterns(item, patterns, errorMessage, errors));
    } else if (obj && typeof obj === 'object') {
      Object.values(obj).forEach(value => this.checkObjectForPatterns(value, patterns, errorMessage, errors));
    }
  }
}

/**
 * Pre-configured validation pipelines for common use cases
 */
export class ValidationPipelines {
  static createPersonalInfoPipeline(): DataValidationPipeline {
    return new DataValidationPipeline([
      {
        field: 'firstName',
        schema: z.string()
          .min(1, 'First name is required')
          .max(50, 'First name must be less than 50 characters')
          .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
        sanitize: true,
        required: true
      },
      {
        field: 'lastName',
        schema: z.string()
          .min(1, 'Last name is required')
          .max(50, 'Last name must be less than 50 characters')
          .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
        sanitize: true,
        required: true
      },
      {
        field: 'email',
        schema: z.string()
          .email('Please enter a valid email address')
          .max(254, 'Email is too long'),
        sanitize: true,
        required: true
      },
      {
        field: 'phone',
        schema: z.string()
          .max(20, 'Phone number is too long')
          .regex(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/, 'Please enter a valid phone number'),
        sanitize: true,
        required: false
      },
      {
        field: 'location',
        schema: z.string().max(100, 'Location must be less than 100 characters'),
        sanitize: true,
        required: false
      },
      {
        field: 'website',
        schema: z.string()
          .url('Please enter a valid website URL')
          .max(500, 'Website URL is too long'),
        sanitize: true,
        required: false
      },
      {
        field: 'linkedIn',
        schema: z.string()
          .url('Please enter a valid LinkedIn URL')
          .max(500, 'LinkedIn URL is too long'),
        sanitize: true,
        required: false
      }
    ]);
  }

  static createResumePipeline(): DataValidationPipeline {
    return new DataValidationPipeline([
      {
        field: 'title',
        schema: z.string()
          .min(1, 'Resume title is required')
          .max(200, 'Resume title must be less than 200 characters'),
        sanitize: true,
        required: true
      },
      {
        field: 'summary',
        schema: z.string()
          .max(2000, 'Summary must be less than 2000 characters'),
        sanitize: true,
        required: false
      },
      {
        field: 'template',
        schema: z.string()
          .max(50, 'Template name is too long'),
        sanitize: true,
        required: false
      }
    ]);
  }

  static createUserPipeline(): DataValidationPipeline {
    return new DataValidationPipeline([
      {
        field: 'email',
        schema: z.string()
          .email('Please enter a valid email address')
          .max(254, 'Email is too long'),
        sanitize: true,
        required: true
      },
      {
        field: 'password',
        schema: z.string()
          .min(8, 'Password must be at least 8 characters')
          .max(128, 'Password is too long')
          .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
        sanitize: false, // Don't sanitize passwords
        required: true
      },
      {
        field: 'name',
        schema: z.string()
          .max(100, 'Name must be less than 100 characters'),
        sanitize: true,
        required: false
      }
    ]);
  }
}
