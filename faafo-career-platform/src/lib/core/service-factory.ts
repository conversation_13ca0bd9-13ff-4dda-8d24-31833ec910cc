/**
 * Service Factory
 * 
 * Creates and configures services with proper dependency injection.
 * This factory helps prevent circular dependencies by managing service
 * creation order and dependencies through the service registry.
 */

import {
  ServiceFactory as IServiceFactory,
  CacheServiceInterface,
  AIServiceInterface,
  DatabaseServiceInterface,
  PerformanceMonitorInterface,
  SecurityServiceInterface,
  ServiceConfiguration
} from '@/lib/interfaces/service-interfaces';

import { serviceRegistry, registerServiceFactory } from '@/lib/core/service-registry';

export class ServiceFactory implements IServiceFactory {
  private configuration: ServiceConfiguration;

  constructor(configuration: ServiceConfiguration = {}) {
    this.configuration = configuration;
    this.registerAllServices();
  }

  /**
   * Register all service factories with their dependencies
   */
  private registerAllServices(): void {
    // Register cache service (no dependencies)
    registerServiceFactory(
      'cache',
      () => this.createCacheService(),
      []
    );

    // Register database service (no dependencies)
    registerServiceFactory(
      'database',
      () => this.createDatabaseService(),
      []
    );

    // Register performance monitor (no dependencies)
    registerServiceFactory(
      'performance',
      () => this.createPerformanceMonitor(),
      []
    );

    // Register security service (depends on cache)
    registerServiceFactory(
      'security',
      () => this.createSecurityService(),
      ['cache']
    );

    // Register AI service (depends on cache and performance monitor)
    registerServiceFactory(
      'ai',
      () => this.createAIService(),
      ['cache', 'performance']
    );
  }

  /**
   * Create cache service instance
   */
  createCacheService(): CacheServiceInterface {
    const config = this.configuration.cache || {
      provider: 'memory',
      ttl: 3600
    };

    // Import and create cache service based on configuration
    if (config.provider === 'redis') {
      // Use Redis cache implementation
      const { RedisCacheService } = require('@/lib/services/redis-cache-service');
      return new RedisCacheService(config);
    } else if (config.provider === 'hybrid') {
      // Use enhanced cache service
      const { EnhancedCacheService } = require('@/lib/services/enhanced-cache-service');
      return new EnhancedCacheService(config);
    } else {
      // Use memory cache service
      const { MemoryCacheService } = require('@/lib/services/memory-cache-service');
      return new MemoryCacheService(config);
    }
  }

  /**
   * Create AI service instance
   */
  createAIService(): AIServiceInterface {
    const config = this.configuration.ai || {
      provider: 'gemini',
      model: 'gemini-1.5-flash',
      apiKey: process.env.GOOGLE_GEMINI_API_KEY || '',
      timeout: 30000,
      rateLimitPerMinute: 60
    };

    // Get dependencies from registry
    const cacheService = serviceRegistry.get<CacheServiceInterface>('cache');
    const performanceMonitor = serviceRegistry.get<PerformanceMonitorInterface>('performance');

    // Create AI service with dependencies
    if (config.provider === 'gemini') {
      const { GeminiAIService } = require('@/lib/services/gemini-ai-service');
      return new GeminiAIService(config, cacheService, performanceMonitor);
    } else if (config.provider === 'openai') {
      const { OpenAIService } = require('@/lib/services/openai-service');
      return new OpenAIService(config, cacheService, performanceMonitor);
    } else {
      throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }

  /**
   * Create database service instance
   */
  createDatabaseService(): DatabaseServiceInterface {
    const config = this.configuration.database || {
      provider: 'postgresql',
      connectionString: process.env.DATABASE_URL || '',
      poolSize: 10,
      timeout: 30000
    };

    // Create database service based on configuration
    if (config.provider === 'postgresql') {
      const { PostgreSQLDatabaseService } = require('@/lib/services/postgresql-database-service');
      return new PostgreSQLDatabaseService(config);
    } else {
      throw new Error(`Unsupported database provider: ${config.provider}`);
    }
  }

  /**
   * Create performance monitor instance
   */
  createPerformanceMonitor(): PerformanceMonitorInterface {
    const config = this.configuration.performance || {
      enableMetrics: true,
      metricsInterval: 60000,
      alertThresholds: {
        responseTime: 5000,
        errorRate: 0.05,
        memoryUsage: 0.8
      }
    };

    const { PerformanceMonitorService } = require('@/lib/services/performance-monitor-service');
    return new PerformanceMonitorService(config);
  }

  /**
   * Create security service instance
   */
  createSecurityService(): SecurityServiceInterface {
    const config = this.configuration.security || {
      enableAuditLog: true,
      encryptionKey: process.env.ENCRYPTION_KEY || '',
      sessionTimeout: 3600,
      maxLoginAttempts: 5
    };

    // Get dependencies from registry
    const cacheService = serviceRegistry.get<CacheServiceInterface>('cache');

    const { SecurityService } = require('@/lib/services/security-service');
    return new SecurityService(config, cacheService);
  }

  /**
   * Get service configuration
   */
  getConfiguration(): ServiceConfiguration {
    return { ...this.configuration };
  }

  /**
   * Update service configuration
   */
  updateConfiguration(config: Partial<ServiceConfiguration>): void {
    this.configuration = { ...this.configuration, ...config };
  }
}

// Create default service factory instance
export const serviceFactory = new ServiceFactory();

// Helper functions to get services through the factory
export function getCacheService(): CacheServiceInterface {
  return serviceRegistry.get<CacheServiceInterface>('cache');
}

export function getAIService(): AIServiceInterface {
  return serviceRegistry.get<AIServiceInterface>('ai');
}

export function getDatabaseService(): DatabaseServiceInterface {
  return serviceRegistry.get<DatabaseServiceInterface>('database');
}

export function getPerformanceMonitor(): PerformanceMonitorInterface {
  return serviceRegistry.get<PerformanceMonitorInterface>('performance');
}

export function getSecurityService(): SecurityServiceInterface {
  return serviceRegistry.get<SecurityServiceInterface>('security');
}

// Service initialization function
export async function initializeAllServices(config?: ServiceConfiguration): Promise<void> {
  if (config) {
    serviceFactory.updateConfiguration(config);
  }

  // Validate service registry before initialization
  const validation = serviceRegistry.validate();
  if (!validation.valid) {
    throw new Error(`Service validation failed: ${validation.errors.join(', ')}`);
  }

  // Initialize all services
  await serviceRegistry.initializeAll();
  
  console.log('[ServiceFactory] All services initialized successfully');
}

// Service shutdown function
export async function shutdownAllServices(): Promise<void> {
  await serviceRegistry.shutdown();
  console.log('[ServiceFactory] All services shutdown successfully');
}
