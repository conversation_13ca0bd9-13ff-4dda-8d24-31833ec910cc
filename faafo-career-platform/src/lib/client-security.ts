'use client';

import React from 'react';
import { SecurityValidator } from '@/lib/validation';

export class ClientSecurity {
  private static csrfToken: string | null = null;
  private static tokenExpiry: number = 0;

  /**
   * Get CSRF token from server
   */
  static async getCSRFToken(): Promise<string> {
    // Check if we have a valid cached token
    if (this.csrfToken && Date.now() < this.tokenExpiry) {
      return this.csrfToken;
    }

    try {
      const response = await fetch('/api/csrf-token', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch CSRF token');
      }

      const data = await response.json();
      this.csrfToken = data.csrfToken;
      this.tokenExpiry = Date.now() + (50 * 60 * 1000); // Cache for 50 minutes

      if (!this.csrfToken) {
        throw new Error('Invalid CSRF token received');
      }

      return this.csrfToken;
    } catch (error) {
      console.error('Error fetching CSRF token:', error);
      throw new Error('Unable to secure request');
    }
  }

  /**
   * Make a secure API request with CSRF protection
   */
  static async secureRequest(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const method = options.method || 'GET';
    const needsCSRF = ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase());

    // Prepare headers
    const headers = new Headers(options.headers);
    headers.set('Content-Type', 'application/json');

    // Add CSRF token for state-changing operations
    if (needsCSRF) {
      const csrfToken = await this.getCSRFToken();
      headers.set('X-CSRF-Token', csrfToken);
    }

    // Validate and sanitize request body
    if (options.body && typeof options.body === 'string') {
      const securityCheck = SecurityValidator.validateSecurity(options.body);
      if (!securityCheck.isValid) {
        throw new Error(`Security validation failed: ${securityCheck.threats.join(', ')}`);
      }
    }

    return fetch(url, {
      ...options,
      headers,
      credentials: 'same-origin',
    });
  }

  /**
   * Secure form submission helper
   */
  static async submitForm(
    url: string,
    formData: Record<string, any>,
    method: 'POST' | 'PUT' | 'PATCH' = 'POST'
  ): Promise<Response> {
    // Sanitize form data
    const sanitizedData = this.sanitizeFormData(formData);

    return this.secureRequest(url, {
      method,
      body: JSON.stringify(sanitizedData),
    });
  }

  /**
   * Sanitize form data before submission
   */
  static sanitizeFormData(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitized[key] = SecurityValidator.sanitizeInput(value, {
          maxLength: 10000,
          preserveNewlines: true
        });
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'string' 
            ? SecurityValidator.sanitizeInput(item, { maxLength: 1000 })
            : item
        );
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Validate input on the client side
   */
  static validateInput(input: string): { isValid: boolean; message?: string } {
    const securityCheck = SecurityValidator.validateSecurity(input);
    
    if (!securityCheck.isValid) {
      return {
        isValid: false,
        message: `Invalid input: ${securityCheck.threats.join(', ')}`
      };
    }

    return { isValid: true };
  }

  /**
   * Secure file upload helper
   */
  static async uploadFile(
    url: string,
    file: File,
    additionalData: Record<string, string> = {}
  ): Promise<Response> {
    // Validate file type and size
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain'
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error('File type not allowed');
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error('File size too large');
    }

    // Create form data
    const formData = new FormData();
    formData.append('file', file);

    // Add additional data (sanitized)
    for (const [key, value] of Object.entries(additionalData)) {
      formData.append(key, SecurityValidator.sanitizeInput(value));
    }

    // Get CSRF token
    const csrfToken = await this.getCSRFToken();

    return fetch(url, {
      method: 'POST',
      body: formData,
      credentials: 'same-origin',
      headers: {
        'X-CSRF-Token': csrfToken,
      },
    });
  }

  /**
   * Clear cached security tokens
   */
  static clearTokens(): void {
    this.csrfToken = null;
    this.tokenExpiry = 0;
  }

  /**
   * Content Security Policy helper for dynamic content
   */
  static sanitizeHTML(html: string): string {
    return SecurityValidator.escapeHtml(html);
  }

  /**
   * Safe JSON parsing
   */
  static safeJSONParse(jsonString: string): { success: boolean; data?: any; error?: string } {
    return SecurityValidator.safeJsonParse(jsonString);
  }
}

/**
 * React hook for secure API calls
 */
export function useSecureAPI() {
  const secureRequest = async (
    url: string,
    options: RequestInit = {}
  ): Promise<Response> => {
    return ClientSecurity.secureRequest(url, options);
  };

  const submitForm = async (
    url: string,
    formData: Record<string, any>,
    method: 'POST' | 'PUT' | 'PATCH' = 'POST'
  ): Promise<Response> => {
    return ClientSecurity.submitForm(url, formData, method);
  };

  const uploadFile = async (
    url: string,
    file: File,
    additionalData: Record<string, string> = {}
  ): Promise<Response> => {
    return ClientSecurity.uploadFile(url, file, additionalData);
  };

  return {
    secureRequest,
    submitForm,
    uploadFile,
    validateInput: ClientSecurity.validateInput,
    sanitizeHTML: ClientSecurity.sanitizeHTML,
  };
}

/**
 * Higher-order component for secure forms
 */
export function withSecurity<T extends Record<string, any>>(
  WrappedComponent: React.ComponentType<T>
): React.ComponentType<T> {
  const SecurityWrapper = (props: T) => {
    const secureAPI = useSecureAPI();

    return React.createElement(WrappedComponent, {
      ...props,
      secureAPI
    });
  };

  SecurityWrapper.displayName = `withSecurity(${WrappedComponent.displayName || WrappedComponent.name})`;

  return SecurityWrapper;
}

export default ClientSecurity;
