/**
 * Enhanced Rate Limiter with User-Based and IP-Based Limiting
 * Addresses shared network limitations and provides more granular control
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

interface RateLimitConfig {
  // IP-based limits (for unauthenticated users)
  ipRequests: number;
  ipWindowMs: number;
  
  // User-based limits (for authenticated users)
  userRequests: number;
  userWindowMs: number;
  
  // Burst limits (short-term)
  burstRequests: number;
  burstWindowMs: number;
  
  // Shared network protection
  sharedNetworkMultiplier: number;
  
  // Skip limits for certain conditions
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
  isSharedNetwork?: boolean;
}

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  headers: Record<string, string>;
  limitType: 'ip' | 'user' | 'burst' | 'shared-network';
}

export class EnhancedRateLimiter {
  private ipStore = new Map<string, RateLimitEntry>();
  private userStore = new Map<string, RateLimitEntry>();
  private burstStore = new Map<string, RateLimitEntry>();
  private sharedNetworkDetection = new Map<string, Set<string>>();
  
  constructor(private config: RateLimitConfig) {
    // Cleanup expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  async checkLimit(request: NextRequest): Promise<RateLimitResult> {
    const session = await getServerSession(authOptions);
    const ip = this.getClientIP(request);
    const userId = session?.user?.id;
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Detect shared networks
    const isSharedNetwork = this.detectSharedNetwork(ip, userId || undefined, userAgent);

    // Check burst limits first (applies to all requests)
    const burstResult = this.checkBurstLimit(ip, userId || undefined);
    if (!burstResult.allowed) {
      return burstResult;
    }

    // For authenticated users, use user-based limiting
    if (userId) {
      const userResult = this.checkUserLimit(userId, isSharedNetwork);
      if (!userResult.allowed) {
        return userResult;
      }
    }

    // Always check IP-based limits as a fallback/additional protection
    const ipResult = this.checkIPLimit(ip, isSharedNetwork, !!userId);
    
    // Return the most restrictive result
    return ipResult;
  }

  private checkUserLimit(userId: string, isSharedNetwork: boolean): RateLimitResult {
    const key = `user:${userId}`;
    const now = Date.now();
    
    let entry = this.userStore.get(key);
    const windowMs = this.config.userWindowMs;
    const limit = isSharedNetwork 
      ? Math.floor(this.config.userRequests * this.config.sharedNetworkMultiplier)
      : this.config.userRequests;

    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + windowMs,
        firstRequest: now,
        isSharedNetwork
      };
      this.userStore.set(key, entry);
    }

    const allowed = entry.count < limit;
    const remaining = Math.max(0, limit - entry.count - 1);

    if (allowed) {
      entry.count++;
    }

    return {
      allowed,
      limit,
      remaining,
      resetTime: entry.resetTime,
      retryAfter: allowed ? undefined : Math.ceil((entry.resetTime - now) / 1000),
      headers: this.generateHeaders('user', limit, remaining, entry.resetTime),
      limitType: isSharedNetwork ? 'shared-network' : 'user'
    };
  }

  private checkIPLimit(ip: string, isSharedNetwork: boolean, isAuthenticated: boolean): RateLimitResult {
    const key = `ip:${ip}`;
    const now = Date.now();
    
    let entry = this.ipStore.get(key);
    const windowMs = this.config.ipWindowMs;
    
    // Adjust limits based on context
    let baseLimit = this.config.ipRequests;
    
    // Increase limits for authenticated users from shared networks
    if (isAuthenticated && isSharedNetwork) {
      baseLimit = Math.floor(baseLimit * this.config.sharedNetworkMultiplier);
    }
    
    // Decrease limits for unauthenticated users from shared networks
    if (!isAuthenticated && isSharedNetwork) {
      baseLimit = Math.floor(baseLimit * 0.5);
    }

    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + windowMs,
        firstRequest: now,
        isSharedNetwork
      };
      this.ipStore.set(key, entry);
    }

    const allowed = entry.count < baseLimit;
    const remaining = Math.max(0, baseLimit - entry.count - 1);

    if (allowed) {
      entry.count++;
    }

    return {
      allowed,
      limit: baseLimit,
      remaining,
      resetTime: entry.resetTime,
      retryAfter: allowed ? undefined : Math.ceil((entry.resetTime - now) / 1000),
      headers: this.generateHeaders('ip', baseLimit, remaining, entry.resetTime),
      limitType: isSharedNetwork ? 'shared-network' : 'ip'
    };
  }

  private checkBurstLimit(ip: string, userId?: string): RateLimitResult {
    const key = userId ? `burst:user:${userId}` : `burst:ip:${ip}`;
    const now = Date.now();
    
    let entry = this.burstStore.get(key);
    const windowMs = this.config.burstWindowMs;
    const limit = this.config.burstRequests;

    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + windowMs,
        firstRequest: now
      };
      this.burstStore.set(key, entry);
    }

    const allowed = entry.count < limit;
    const remaining = Math.max(0, limit - entry.count - 1);

    if (allowed) {
      entry.count++;
    }

    return {
      allowed,
      limit,
      remaining,
      resetTime: entry.resetTime,
      retryAfter: allowed ? undefined : Math.ceil((entry.resetTime - now) / 1000),
      headers: this.generateHeaders('burst', limit, remaining, entry.resetTime),
      limitType: 'burst'
    };
  }

  private detectSharedNetwork(ip: string, userId?: string, userAgent?: string): boolean {
    if (!userId) return false;

    const networkKey = this.getNetworkKey(ip);
    
    if (!this.sharedNetworkDetection.has(networkKey)) {
      this.sharedNetworkDetection.set(networkKey, new Set());
    }

    const users = this.sharedNetworkDetection.get(networkKey)!;
    users.add(userId);

    // Consider it a shared network if more than 3 different users from same IP
    const isShared = users.size > 3;

    // Clean up old entries periodically
    if (users.size > 100) {
      users.clear();
    }

    return isShared;
  }

  private getNetworkKey(ip: string): string {
    // For IPv4, use /24 subnet (first 3 octets)
    // For IPv6, use /64 subnet (first 4 groups)
    if (ip.includes(':')) {
      // IPv6
      const parts = ip.split(':');
      return parts.slice(0, 4).join(':');
    } else {
      // IPv4
      const parts = ip.split('.');
      return parts.slice(0, 3).join('.');
    }
  }

  private getClientIP(request: NextRequest): string {
    // Try multiple headers to get the real client IP
    const forwarded = request.headers.get('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }

    const realIP = request.headers.get('x-real-ip');
    if (realIP) {
      return realIP;
    }

    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    if (cfConnectingIP) {
      return cfConnectingIP;
    }

    // Fallback to a default IP
    return '127.0.0.1';
  }

  private generateHeaders(type: string, limit: number, remaining: number, resetTime: number): Record<string, string> {
    return {
      'X-RateLimit-Limit': limit.toString(),
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString(),
      'X-RateLimit-Type': type
    };
  }

  private cleanup(): void {
    const now = Date.now();
    
    // Clean up expired IP entries
    const ipKeysToDelete: string[] = [];
    this.ipStore.forEach((entry, key) => {
      if (now > entry.resetTime) {
        ipKeysToDelete.push(key);
      }
    });
    ipKeysToDelete.forEach(key => this.ipStore.delete(key));

    // Clean up expired user entries
    const userKeysToDelete: string[] = [];
    this.userStore.forEach((entry, key) => {
      if (now > entry.resetTime) {
        userKeysToDelete.push(key);
      }
    });
    userKeysToDelete.forEach(key => this.userStore.delete(key));

    // Clean up expired burst entries
    const burstKeysToDelete: string[] = [];
    this.burstStore.forEach((entry, key) => {
      if (now > entry.resetTime) {
        burstKeysToDelete.push(key);
      }
    });
    burstKeysToDelete.forEach(key => this.burstStore.delete(key));

    console.log(`RateLimiter: Cleaned up expired entries. Active: IP=${this.ipStore.size}, User=${this.userStore.size}, Burst=${this.burstStore.size}`);
  }
}

// Pre-configured rate limiters for different endpoint types
export const enhancedRateLimiters = {
  api: new EnhancedRateLimiter({
    ipRequests: 100,
    ipWindowMs: 15 * 60 * 1000, // 15 minutes
    userRequests: 200,
    userWindowMs: 15 * 60 * 1000,
    burstRequests: 20,
    burstWindowMs: 60 * 1000, // 1 minute
    sharedNetworkMultiplier: 2.0
  }),

  auth: new EnhancedRateLimiter({
    ipRequests: 10,
    ipWindowMs: 15 * 60 * 1000,
    userRequests: 15,
    userWindowMs: 15 * 60 * 1000,
    burstRequests: 5,
    burstWindowMs: 60 * 1000,
    sharedNetworkMultiplier: 1.5
  }),

  write: new EnhancedRateLimiter({
    ipRequests: 50,
    ipWindowMs: 15 * 60 * 1000,
    userRequests: 100,
    userWindowMs: 15 * 60 * 1000,
    burstRequests: 10,
    burstWindowMs: 60 * 1000,
    sharedNetworkMultiplier: 1.8
  })
};

export default EnhancedRateLimiter;
