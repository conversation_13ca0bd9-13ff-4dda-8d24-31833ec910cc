/**
 * Audit System Tests
 * 
 * Basic tests to verify the audit system components work correctly.
 */

import { AuditService } from '@/lib/audit/audit-service';
import { CoreAuditEngine } from '@/lib/audit/core-audit-engine';
import { ESLintAnalyzer } from '@/lib/audit/analyzers/eslint-analyzer';
import { SecurityAnalyzer } from '@/lib/audit/analyzers/security-analyzer';
import {
  AuditRunConfig,
  IssueSeverity,
  IssueCategory,
  AnalysisContext
} from '@/lib/audit/types';

// Mock dependencies
jest.mock('../../logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}));

jest.mock('../storage/audit-storage', () => ({
  AuditStorage: jest.fn().mockImplementation(() => ({
    createAuditRun: jest.fn(),
    updateAuditRun: jest.fn(),
    storeIssues: jest.fn(),
    getAuditRun: jest.fn(),
    getAuditRuns: jest.fn(),
    getIssues: jest.fn(),
    disconnect: jest.fn()
  }))
}));

describe('Audit System', () => {
  let auditService: AuditService;
  let mockContext: AnalysisContext;

  beforeEach(() => {
    auditService = new AuditService();
    mockContext = {
      projectRoot: '/test/project',
      sourceDir: '/test/project/src',
      configFiles: {
        tsconfig: '/test/project/tsconfig.json',
        eslint: '/test/project/.eslintrc.json',
        prisma: '/test/project/prisma/schema.prisma',
        package: '/test/project/package.json'
      },
      excludePatterns: ['node_modules/**', '**/*.test.ts'],
      includePatterns: ['src/**/*.ts', 'src/**/*.tsx']
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('AuditService', () => {
    it('should create an instance', () => {
      expect(auditService).toBeInstanceOf(AuditService);
    });

    it('should have all required methods', () => {
      expect(typeof auditService.startAudit).toBe('function');
      expect(typeof auditService.getAuditRun).toBe('function');
      expect(typeof auditService.getAuditRuns).toBe('function');
      expect(typeof auditService.getIssues).toBe('function');
      expect(typeof auditService.updateIssueStatus).toBe('function');
      expect(typeof auditService.getAuditStatistics).toBe('function');
    });
  });

  describe('CoreAuditEngine', () => {
    it('should create an instance with context', () => {
      const engine = new CoreAuditEngine(mockContext);
      expect(engine).toBeInstanceOf(CoreAuditEngine);
    });

    it('should have all analyzer methods', () => {
      const engine = new CoreAuditEngine(mockContext);
      expect(typeof engine.analyzeTypeScript).toBe('function');
      expect(typeof engine.analyzeSecurity).toBe('function');
      expect(typeof engine.analyzePerformance).toBe('function');
      expect(typeof engine.integrateESLint).toBe('function');
      expect(typeof engine.runAudit).toBe('function');
    });
  });

  describe('ESLintAnalyzer', () => {
    it('should create an instance', () => {
      const analyzer = new ESLintAnalyzer(mockContext);
      expect(analyzer).toBeInstanceOf(ESLintAnalyzer);
    });

    it('should have analyze method', () => {
      const analyzer = new ESLintAnalyzer(mockContext);
      expect(typeof analyzer.analyze).toBe('function');
    });

    it('should return empty array when no ESLint config found', async () => {
      const analyzer = new ESLintAnalyzer(mockContext);
      const issues = await analyzer.analyze();
      expect(Array.isArray(issues)).toBe(true);
    });
  });

  describe('SecurityAnalyzer', () => {
    it('should create an instance', () => {
      const analyzer = new SecurityAnalyzer(mockContext);
      expect(analyzer).toBeInstanceOf(SecurityAnalyzer);
    });

    it('should have analyze method', () => {
      const analyzer = new SecurityAnalyzer(mockContext);
      expect(typeof analyzer.analyze).toBe('function');
    });

    it('should return empty array when no files found', async () => {
      const analyzer = new SecurityAnalyzer(mockContext);
      const issues = await analyzer.analyze();
      expect(Array.isArray(issues)).toBe(true);
    });
  });

  describe('Audit Configuration', () => {
    it('should accept valid audit configuration', () => {
      const config: AuditRunConfig = {
        includeCategories: [IssueCategory.SECURITY, IssueCategory.PERFORMANCE],
        excludeCategories: [IssueCategory.DOCUMENTATION],
        includePaths: ['src/**/*.ts'],
        excludePaths: ['**/*.test.ts'],
        maxIssues: 100,
        severityThreshold: IssueSeverity.MEDIUM,
        enableAIAnalysis: true,
        enablePerformanceAnalysis: true,
        enableSecurityScan: true
      };

      expect(config.includeCategories).toContain(IssueCategory.SECURITY);
      expect(config.severityThreshold).toBe(IssueSeverity.MEDIUM);
      expect(config.enableSecurityScan).toBe(true);
    });

    it('should handle empty configuration', () => {
      const config: AuditRunConfig = {};
      expect(config).toBeDefined();
      expect(config.includeCategories).toBeUndefined();
      expect(config.maxIssues).toBeUndefined();
    });
  });

  describe('Issue Types', () => {
    it('should validate issue severity enum', () => {
      expect(Object.values(IssueSeverity)).toContain('CRITICAL');
      expect(Object.values(IssueSeverity)).toContain('HIGH');
      expect(Object.values(IssueSeverity)).toContain('MEDIUM');
      expect(Object.values(IssueSeverity)).toContain('LOW');
    });

    it('should validate issue category enum', () => {
      expect(Object.values(IssueCategory)).toContain('SECURITY');
      expect(Object.values(IssueCategory)).toContain('PERFORMANCE');
      expect(Object.values(IssueCategory)).toContain('MAINTAINABILITY');
      expect(Object.values(IssueCategory)).toContain('TESTING');
    });
  });

  describe('Error Handling', () => {
    it('should handle analyzer errors gracefully', async () => {
      const analyzer = new ESLintAnalyzer(mockContext);
      
      // Mock fs to throw an error
      jest.doMock('fs/promises', () => ({
        readdir: jest.fn().mockRejectedValue(new Error('File system error'))
      }));

      const issues = await analyzer.analyze();
      expect(Array.isArray(issues)).toBe(true);
      expect(issues.length).toBe(0);
    });
  });

  describe('Integration', () => {
    it('should integrate all components without errors', () => {
      expect(() => {
        const service = new AuditService();
        const engine = new CoreAuditEngine(mockContext);
        const eslintAnalyzer = new ESLintAnalyzer(mockContext);
        const securityAnalyzer = new SecurityAnalyzer(mockContext);
      }).not.toThrow();
    });
  });
});
