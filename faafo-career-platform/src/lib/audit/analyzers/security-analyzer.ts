/**
 * Security Analyzer
 * 
 * Analyzes code for security vulnerabilities including
 * common security anti-patterns, dependency vulnerabilities,
 * and potential security risks.
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import {
  SecurityVulnerability,
  AnalysisContext,
  IssueSeverity,
  IssueCategory
} from '@/lib/audit/types';
import { logger } from '@/lib/logger';

export class SecurityAnalyzer {
  private context: AnalysisContext;

  constructor(context: AnalysisContext) {
    this.context = context;
  }

  /**
   * Analyze code for security vulnerabilities
   */
  async analyze(): Promise<SecurityVulnerability[]> {
    try {
      logger.info('Starting security analysis');
      
      const issues: SecurityVulnerability[] = [];

      // Analyze source files for security patterns
      const sourceFiles = await this.getSourceFiles();
      for (const file of sourceFiles) {
        const fileIssues = await this.analyzeFile(file);
        issues.push(...fileIssues);
      }

      // Check dependencies for known vulnerabilities
      const dependencyIssues = await this.analyzeDependencies();
      issues.push(...dependencyIssues);

      // Check configuration files
      const configIssues = await this.analyzeConfiguration();
      issues.push(...configIssues);

      logger.info(`Security analysis completed: ${issues.length} vulnerabilities found`);
      return issues;

    } catch (error) {
      logger.error('Security analysis failed:', error as Error);
      return [];
    }
  }

  /**
   * Get all source files to analyze
   */
  private async getSourceFiles(): Promise<string[]> {
    const files: string[] = [];
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    
    const getAllFiles = async (dir: string): Promise<void> => {
      try {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory() && !['node_modules', '.next', 'dist'].includes(entry.name)) {
            await getAllFiles(fullPath);
          } else if (entry.isFile() && extensions.includes(path.extname(entry.name))) {
            const relativePath = path.relative(this.context.projectRoot, fullPath);
            if (!this.shouldExcludeFile(relativePath)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        logger.error(`Failed to read directory ${dir}:`, error as Error);
      }
    };

    await getAllFiles(this.context.sourceDir);
    return files;
  }

  /**
   * Analyze individual file for security issues
   */
  private async analyzeFile(filePath: string): Promise<SecurityVulnerability[]> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const issues: SecurityVulnerability[] = [];
      const relativePath = path.relative(this.context.projectRoot, filePath);

      // Check for dangerous patterns
      const patterns = [
        {
          regex: /eval\s*\(/g,
          title: 'Use of eval() function',
          description: 'eval() can execute arbitrary code and is a security risk',
          severity: IssueSeverity.CRITICAL,
          cweId: 'CWE-95',
          recommendation: 'Avoid using eval(). Use JSON.parse() for JSON data or other safe alternatives'
        },
        {
          regex: /innerHTML\s*=/g,
          title: 'Direct innerHTML assignment',
          description: 'Direct innerHTML assignment can lead to XSS vulnerabilities',
          severity: IssueSeverity.HIGH,
          cweId: 'CWE-79',
          recommendation: 'Use textContent or sanitize HTML content before assignment'
        },
        {
          regex: /document\.write\s*\(/g,
          title: 'Use of document.write()',
          description: 'document.write() can be exploited for XSS attacks',
          severity: IssueSeverity.HIGH,
          cweId: 'CWE-79',
          recommendation: 'Use modern DOM manipulation methods instead'
        },
        {
          regex: /dangerouslySetInnerHTML/g,
          title: 'Use of dangerouslySetInnerHTML',
          description: 'dangerouslySetInnerHTML can introduce XSS vulnerabilities if not properly sanitized',
          severity: IssueSeverity.MEDIUM,
          cweId: 'CWE-79',
          recommendation: 'Ensure content is properly sanitized before using dangerouslySetInnerHTML'
        },
        {
          regex: /localStorage\.setItem|sessionStorage\.setItem/g,
          title: 'Storing sensitive data in browser storage',
          description: 'Browser storage is accessible to scripts and may not be secure for sensitive data',
          severity: IssueSeverity.MEDIUM,
          cweId: 'CWE-922',
          recommendation: 'Avoid storing sensitive data in localStorage/sessionStorage. Use secure HTTP-only cookies'
        }
      ];

      const lines = content.split('\n');
      
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.regex.exec(content)) !== null) {
          const lineNumber = content.substring(0, match.index).split('\n').length;
          const line = lines[lineNumber - 1];
          
          issues.push({
            severity: pattern.severity,
            category: IssueCategory.SECURITY,
            title: pattern.title,
            description: pattern.description,
            filePath: relativePath,
            lineNumber,
            codeSnippet: this.getCodeSnippet(lines, lineNumber),
            recommendation: pattern.recommendation,
            cweId: pattern.cweId,
            cvssScore: this.calculateCVSSScore(pattern.severity),
            exploitability: this.getExploitability(pattern.severity),
            metadata: {
              pattern: pattern.regex.source,
              matchedText: match[0]
            }
          });
        }
      }

      return issues;
    } catch (error) {
      logger.error(`Failed to analyze file ${filePath}:`, error as Error);
      return [];
    }
  }

  /**
   * Analyze dependencies for known vulnerabilities
   */
  private async analyzeDependencies(): Promise<SecurityVulnerability[]> {
    try {
      const packageJsonPath = this.context.configFiles.package;
      if (!packageJsonPath || !await this.fileExists(packageJsonPath)) {
        return [];
      }

      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
      const issues: SecurityVulnerability[] = [];

      // Check for known vulnerable packages (simplified - in real implementation, use vulnerability database)
      const vulnerablePackages = [
        {
          name: 'lodash',
          versions: ['<4.17.21'],
          vulnerability: 'Prototype pollution vulnerability',
          cweId: 'CWE-1321',
          severity: IssueSeverity.HIGH
        },
        {
          name: 'axios',
          versions: ['<0.21.1'],
          vulnerability: 'SSRF vulnerability in axios',
          cweId: 'CWE-918',
          severity: IssueSeverity.MEDIUM
        }
      ];

      const allDependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      for (const [depName, version] of Object.entries(allDependencies)) {
        const vulnerable = vulnerablePackages.find(vp => vp.name === depName);
        if (vulnerable) {
          issues.push({
            severity: vulnerable.severity,
            category: IssueCategory.SECURITY,
            title: `Vulnerable dependency: ${depName}`,
            description: vulnerable.vulnerability,
            filePath: 'package.json',
            recommendation: `Update ${depName} to a secure version`,
            cweId: vulnerable.cweId,
            cvssScore: this.calculateCVSSScore(vulnerable.severity),
            exploitability: this.getExploitability(vulnerable.severity),
            metadata: {
              dependencyName: depName,
              currentVersion: version as string,
              vulnerableVersions: vulnerable.versions
            }
          });
        }
      }

      return issues;
    } catch (error) {
      logger.error('Failed to analyze dependencies:', error as Error);
      return [];
    }
  }

  /**
   * Analyze configuration files for security issues
   */
  private async analyzeConfiguration(): Promise<SecurityVulnerability[]> {
    const issues: SecurityVulnerability[] = [];

    try {
      // Check Next.js configuration
      const nextConfigPath = path.join(this.context.projectRoot, 'next.config.js');
      if (await this.fileExists(nextConfigPath)) {
        const configIssues = await this.analyzeNextConfig(nextConfigPath);
        issues.push(...configIssues);
      }

      // Check environment files
      const envFiles = ['.env', '.env.local', '.env.development', '.env.production'];
      for (const envFile of envFiles) {
        const envPath = path.join(this.context.projectRoot, envFile);
        if (await this.fileExists(envPath)) {
          const envIssues = await this.analyzeEnvFile(envPath);
          issues.push(...envIssues);
        }
      }

    } catch (error) {
      logger.error('Failed to analyze configuration:', error as Error);
    }

    return issues;
  }

  /**
   * Analyze Next.js configuration
   */
  private async analyzeNextConfig(configPath: string): Promise<SecurityVulnerability[]> {
    try {
      const content = await fs.readFile(configPath, 'utf-8');
      const issues: SecurityVulnerability[] = [];

      // Check for insecure configurations
      if (content.includes('dangerouslyAllowSVG: true')) {
        issues.push({
          severity: IssueSeverity.MEDIUM,
          category: IssueCategory.SECURITY,
          title: 'Dangerous SVG configuration',
          description: 'dangerouslyAllowSVG can lead to XSS vulnerabilities',
          filePath: 'next.config.js',
          recommendation: 'Only enable dangerouslyAllowSVG if absolutely necessary and ensure SVG content is trusted',
          cweId: 'CWE-79',
          cvssScore: 5.4,
          exploitability: 'MEDIUM'
        });
      }

      return issues;
    } catch (error) {
      logger.error(`Failed to analyze Next.js config ${configPath}:`, error as Error);
      return [];
    }
  }

  /**
   * Analyze environment files
   */
  private async analyzeEnvFile(envPath: string): Promise<SecurityVulnerability[]> {
    try {
      const content = await fs.readFile(envPath, 'utf-8');
      const issues: SecurityVulnerability[] = [];
      const lines = content.split('\n');

      lines.forEach((line, index) => {
        const lineNumber = index + 1;
        
        // Check for potential secrets in environment variables
        if (line.includes('password') || line.includes('secret') || line.includes('key')) {
          if (line.includes('=') && !line.startsWith('#')) {
            const [key, value] = line.split('=');
            if (value && value.length > 0 && !value.startsWith('$')) {
              issues.push({
                severity: IssueSeverity.MEDIUM,
                category: IssueCategory.SECURITY,
                title: 'Potential secret in environment file',
                description: `Environment variable ${key.trim()} may contain sensitive information`,
                filePath: path.basename(envPath),
                lineNumber,
                recommendation: 'Ensure sensitive values are properly secured and not committed to version control',
                cweId: 'CWE-798',
                cvssScore: 4.3,
                exploitability: 'LOW',
                metadata: {
                  variableName: key.trim()
                }
              });
            }
          }
        }
      });

      return issues;
    } catch (error) {
      logger.error(`Failed to analyze env file ${envPath}:`, error as Error);
      return [];
    }
  }

  /**
   * Get code snippet around line
   */
  private getCodeSnippet(lines: string[], lineNumber: number, contextLines: number = 2): string {
    const startLine = Math.max(0, lineNumber - contextLines - 1);
    const endLine = Math.min(lines.length, lineNumber + contextLines);
    
    return lines.slice(startLine, endLine)
      .map((line, index) => {
        const actualLineNumber = startLine + index + 1;
        const marker = actualLineNumber === lineNumber ? '> ' : '  ';
        return `${marker}${actualLineNumber.toString().padStart(3)}: ${line}`;
      })
      .join('\n');
  }

  /**
   * Calculate CVSS score based on severity
   */
  private calculateCVSSScore(severity: IssueSeverity): number {
    switch (severity) {
      case IssueSeverity.CRITICAL:
        return 9.0;
      case IssueSeverity.HIGH:
        return 7.5;
      case IssueSeverity.MEDIUM:
        return 5.0;
      case IssueSeverity.LOW:
        return 2.5;
      default:
        return 0.0;
    }
  }

  /**
   * Get exploitability level
   */
  private getExploitability(severity: IssueSeverity): 'LOW' | 'MEDIUM' | 'HIGH' {
    switch (severity) {
      case IssueSeverity.CRITICAL:
        return 'HIGH';
      case IssueSeverity.HIGH:
        return 'HIGH';
      case IssueSeverity.MEDIUM:
        return 'MEDIUM';
      case IssueSeverity.LOW:
        return 'LOW';
      default:
        return 'LOW';
    }
  }

  /**
   * Check if file should be excluded
   */
  private shouldExcludeFile(relativePath: string): boolean {
    return this.context.excludePatterns.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
      return regex.test(relativePath);
    });
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
