/**
 * ESLint Analyzer
 * 
 * Integrates with ESLint to analyze code quality issues and convert
 * them to the audit system's standardized issue format.
 */

import { ESLint } from 'eslint';
import * as path from 'path';
import { promises as fs } from 'fs';
import {
  LintIssue,
  AnalysisContext,
  IssueSeverity,
  IssueCategory
} from '@/lib/audit/types';
import { logger } from '@/lib/logger';

export class ESLintAnalyzer {
  private context: AnalysisContext;
  private eslint: ESLint | null = null;

  constructor(context: AnalysisContext) {
    this.context = context;
  }

  /**
   * Initialize ESLint with project configuration
   */
  private async initializeESLint(): Promise<void> {
    try {
      const eslintConfigPath = this.context.configFiles.eslint;
      
      // Check if ESLint config exists
      // Simplified ESLint configuration to avoid type issues
      this.eslint = new ESLint({
        cwd: this.context.projectRoot
      });

      logger.info('ESLint analyzer initialized');
    } catch (error) {
      logger.error('Failed to initialize ESLint:', error as Error);
      throw error;
    }
  }

  /**
   * Analyze code using ESLint
   */
  async analyze(): Promise<LintIssue[]> {
    try {
      await this.initializeESLint();

      if (!this.eslint) {
        logger.warn('ESLint not initialized, skipping analysis');
        return [];
      }

      const filesToAnalyze = await this.getFilesToAnalyze();
      
      if (filesToAnalyze.length === 0) {
        logger.info('No files found for ESLint analysis');
        return [];
      }

      logger.info(`Analyzing ${filesToAnalyze.length} files with ESLint`);

      const results = await this.eslint.lintFiles(filesToAnalyze);
      const issues: LintIssue[] = [];

      for (const result of results) {
        if (result.messages.length > 0) {
          const fileIssues = await this.convertESLintMessagesToIssues(result);
          issues.push(...fileIssues);
        }
      }

      logger.info(`ESLint analysis completed: ${issues.length} issues found`);
      return issues;

    } catch (error) {
      logger.error('ESLint analysis failed:', error as Error);
      return [];
    }
  }

  /**
   * Get list of files to analyze
   */
  private async getFilesToAnalyze(): Promise<string[]> {
    const files: string[] = [];
    
    try {
      // Get all TypeScript and JavaScript files in src directory
      const srcDir = this.context.sourceDir;
      const allFiles = await this.getAllFiles(srcDir, ['.ts', '.tsx', '.js', '.jsx']);
      
      // Filter files based on include/exclude patterns
      for (const file of allFiles) {
        const relativePath = path.relative(this.context.projectRoot, file);
        
        // Check exclude patterns
        if (this.shouldExcludeFile(relativePath)) {
          continue;
        }
        
        // Check include patterns
        if (this.shouldIncludeFile(relativePath)) {
          files.push(file);
        }
      }

      return files;
    } catch (error) {
      logger.error('Failed to get files for analysis:', error as Error);
      return [];
    }
  }

  /**
   * Recursively get all files with specified extensions
   */
  private async getAllFiles(dir: string, extensions: string[]): Promise<string[]> {
    const files: string[] = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          // Skip node_modules and other common directories
          if (!['node_modules', '.next', 'dist', 'build', 'coverage'].includes(entry.name)) {
            const subFiles = await this.getAllFiles(fullPath, extensions);
            files.push(...subFiles);
          }
        } else if (entry.isFile()) {
          const ext = path.extname(entry.name);
          if (extensions.includes(ext)) {
            files.push(fullPath);
          }
        }
      }
    } catch (error) {
      logger.error(`Failed to read directory ${dir}:`, error as Error);
    }
    
    return files;
  }

  /**
   * Check if file should be excluded
   */
  private shouldExcludeFile(relativePath: string): boolean {
    return this.context.excludePatterns.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
      return regex.test(relativePath);
    });
  }

  /**
   * Check if file should be included
   */
  private shouldIncludeFile(relativePath: string): boolean {
    if (this.context.includePatterns.length === 0) {
      return true;
    }
    
    return this.context.includePatterns.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
      return regex.test(relativePath);
    });
  }

  /**
   * Convert ESLint messages to audit issues
   */
  private async convertESLintMessagesToIssues(result: ESLint.LintResult): Promise<LintIssue[]> {
    const issues: LintIssue[] = [];
    const relativePath = path.relative(this.context.projectRoot, result.filePath);

    for (const message of result.messages) {
      const codeSnippet = await this.getCodeSnippet(result.filePath, message.line);

      const issue: LintIssue = {
        severity: this.mapESLintSeverityToIssueSeverity(message.severity),
        category: IssueCategory.MAINTAINABILITY,
        title: `ESLint: ${message.ruleId || 'Unknown rule'}`,
        description: message.message,
        filePath: relativePath,
        lineNumber: message.line,
        columnNumber: message.column,
        codeSnippet,
        recommendation: this.generateRecommendation(message),
        fixExample: message.fix ? this.generateFixExample(message) : undefined,
        ruleId: message.ruleId || 'unknown',
        ruleName: message.ruleId || 'Unknown Rule',
        fixable: message.fix !== undefined,
        metadata: {
          eslintSeverity: message.severity,
          nodeType: message.nodeType,
          source: (message as any).source,
          endLine: message.endLine,
          endColumn: message.endColumn
        }
      };

      issues.push(issue);
    }

    return issues;
  }

  /**
   * Map ESLint severity to audit issue severity
   */
  private mapESLintSeverityToIssueSeverity(eslintSeverity: number): IssueSeverity {
    switch (eslintSeverity) {
      case 2: // Error
        return IssueSeverity.HIGH;
      case 1: // Warning
        return IssueSeverity.MEDIUM;
      default:
        return IssueSeverity.LOW;
    }
  }

  /**
   * Get code snippet around the issue line
   */
  private async getCodeSnippet(filePath: string, lineNumber: number, contextLines: number = 2): Promise<string | undefined> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      
      const startLine = Math.max(0, lineNumber - contextLines - 1);
      const endLine = Math.min(lines.length, lineNumber + contextLines);
      
      const snippet = lines.slice(startLine, endLine)
        .map((line, index) => {
          const actualLineNumber = startLine + index + 1;
          const marker = actualLineNumber === lineNumber ? '> ' : '  ';
          return `${marker}${actualLineNumber.toString().padStart(3)}: ${line}`;
        })
        .join('\n');
      
      return snippet;
    } catch (error) {
      logger.error(`Failed to get code snippet for ${filePath}:${lineNumber}:`, error as Error);
      return undefined;
    }
  }

  /**
   * Generate recommendation based on ESLint message
   */
  private generateRecommendation(message: any): string {
    const ruleId = message.ruleId;
    
    // Common ESLint rule recommendations
    const recommendations: Record<string, string> = {
      'no-unused-vars': 'Remove unused variables or prefix with underscore if intentionally unused',
      '@typescript-eslint/no-unused-vars': 'Remove unused variables or use TypeScript\'s noUnusedLocals compiler option',
      'no-console': 'Replace console statements with proper logging using the logger service',
      'prefer-const': 'Use const for variables that are never reassigned',
      'no-var': 'Use let or const instead of var for block-scoped variables',
      'eqeqeq': 'Use strict equality (===) instead of loose equality (==)',
      'no-undef': 'Define the variable or import it from the appropriate module',
      'react-hooks/exhaustive-deps': 'Add missing dependencies to the useEffect dependency array',
      '@typescript-eslint/no-explicit-any': 'Replace any with specific types for better type safety',
      'no-empty': 'Add code to the empty block or remove it if unnecessary'
    };

    if (ruleId && recommendations[ruleId]) {
      return recommendations[ruleId];
    }

    return `Fix the ${ruleId || 'linting'} issue: ${message.message}`;
  }

  /**
   * Generate fix example based on ESLint fix
   */
  private generateFixExample(message: any): string | undefined {
    if (!message.fix) {
      return undefined;
    }

    const fix = message.fix;
    return `Apply fix: Replace text at range [${fix.range[0]}, ${fix.range[1]}] with "${fix.text}"`;
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
