/**
 * Audit Service
 * 
 * Main service class that provides a high-level interface for the audit system.
 * This service is used by the web dashboard and API endpoints.
 */

import { CoreAuditEngine } from '@/lib/audit/core-audit-engine';
import { AuditStorage } from '@/lib/audit/storage/audit-storage';
import {
  AuditRunConfig,
  AuditRunResult,
  AuditRunSummary,
  BaseIssue,
  IssueFilter,
  IssueSortOptions,
  PaginatedIssues,
  AuditAPIResponse,
  AuditProgress,
  AuditProgressCallback,
  IssueStatus,
  AnalysisContext
} from '@/lib/audit/types';
import { logger } from '@/lib/logger';

export class AuditService {
  private storage: AuditStorage;
  private activeAudits: Map<string, CoreAuditEngine> = new Map();

  constructor() {
    this.storage = new AuditStorage();
  }

  /**
   * Start a new audit run
   */
  async startAudit(
    config: AuditRunConfig = {},
    progressCallback?: AuditProgressCallback,
    triggeredBy?: string
  ): Promise<string> {
    try {
      logger.info('Starting new audit run');

      // Create analysis context
      const context: Partial<AnalysisContext> = {
        projectRoot: process.cwd(),
        excludePatterns: [
          'node_modules/**',
          '.next/**',
          'dist/**',
          'build/**',
          'coverage/**',
          '**/*.test.ts',
          '**/*.test.tsx',
          '**/*.spec.ts',
          '**/*.spec.tsx'
        ]
      };

      // Initialize audit engine
      const engine = new CoreAuditEngine(context, progressCallback);
      
      // Add metadata
      const auditConfig = {
        ...config
      };

      // Start audit (non-blocking)
      const auditPromise = engine.runAudit(auditConfig);
      
      // Get audit ID from the promise (this is a simplified approach)
      const result = await auditPromise;
      
      logger.info(`Audit started with ID: ${result.id}`);
      return result.id;

    } catch (error) {
      logger.error('Failed to start audit:', error as Error);
      throw error;
    }
  }

  /**
   * Get audit run by ID
   */
  async getAuditRun(auditId: string): Promise<AuditRunResult | null> {
    try {
      return await this.storage.getAuditRun(auditId);
    } catch (error) {
      logger.error(`Failed to get audit run ${auditId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get audit run summaries with pagination and filtering
   */
  async getAuditRuns(options: {
    page?: number;
    limit?: number;
    status?: string;
    triggeredBy?: string;
  } = {}): Promise<AuditAPIResponse<AuditRunSummary[]>> {
    try {
      const { page = 1, limit = 20, status, triggeredBy } = options;
      return await this.storage.getAuditRuns(page, limit, { status, triggeredBy });
    } catch (error) {
      logger.error('Failed to get audit runs:', error as Error);
      return {
        success: false,
        error: 'Failed to retrieve audit runs'
      };
    }
  }

  /**
   * Get issues with filtering and pagination
   */
  async getIssues(
    filter: IssueFilter = {},
    sort: IssueSortOptions = { field: 'severity', direction: 'desc' }
  ): Promise<PaginatedIssues> {
    try {
      return await this.storage.getIssues(filter, sort);
    } catch (error) {
      logger.error('Failed to get issues:', error as Error);
      throw error;
    }
  }

  /**
   * Get specific issue by ID
   */
  async getIssue(issueId: string): Promise<any | null> {
    try {
      return await this.storage.getIssue(issueId);
    } catch (error) {
      logger.error(`Failed to get issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Update issue status (simplified method for tests)
   */
  async updateIssueStatus(issueId: string, status: string, userId?: string): Promise<any | null> {
    return this.updateIssue(issueId, { status: status as IssueStatus });
  }

  /**
   * Update issue
   */
  async updateIssue(
    issueId: string,
    updateData: {
      status?: IssueStatus;
      assignedToId?: string | null;
      falsePositive?: boolean;
      resolution?: string;
    }
  ): Promise<any | null> {
    try {
      const updatedIssue = await this.storage.updateIssue(issueId, updateData);
      logger.info(`Updated issue ${issueId}`);
      return updatedIssue;
    } catch (error) {
      logger.error(`Failed to update issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Delete issue
   */
  async deleteIssue(issueId: string): Promise<boolean> {
    try {
      const result = await this.storage.deleteIssue(issueId);
      logger.info(`Deleted issue ${issueId}`);
      return result;
    } catch (error) {
      logger.error(`Failed to delete issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Add comment to issue
   */
  async addIssueComment(issueId: string, commentData: { comment: string; userId: string }): Promise<any | null> {
    try {
      const newComment = await this.storage.addIssueComment(issueId, commentData.userId, commentData.comment);
      logger.info(`Added comment to issue ${issueId}`);
      return newComment;
    } catch (error) {
      logger.error(`Failed to add comment to issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get issue comments with pagination
   */
  async getIssueComments(issueId: string, options: { page?: number; limit?: number } = {}): Promise<any> {
    try {
      const { page = 1, limit = 20 } = options;
      return await this.storage.getIssueComments(issueId, page, limit);
    } catch (error) {
      logger.error(`Failed to get comments for issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get audit statistics
   */
  async getAuditStatistics(days: number = 30): Promise<any> {
    try {
      return await this.storage.getAuditStatistics(days);
    } catch (error) {
      logger.error('Failed to get audit statistics:', error as Error);
      throw error;
    }
  }

  /**
   * Cancel running audit
   */
  async cancelAudit(auditId: string): Promise<boolean> {
    try {
      const engine = this.activeAudits.get(auditId);
      if (engine) {
        // In a real implementation, you'd need to add cancellation support to the engine
        this.activeAudits.delete(auditId);
        logger.info(`Cancelled audit ${auditId}`);
        return true;
      }

      // Try to cancel in storage as well
      const result = await this.storage.cancelAuditRun(auditId);
      return result;
    } catch (error) {
      logger.error(`Failed to cancel audit ${auditId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get audit progress for running audits
   */
  async getAuditProgress(auditId: string): Promise<AuditProgress | null> {
    try {
      const engine = this.activeAudits.get(auditId);
      if (engine) {
        // In a real implementation, you'd get progress from the engine
        return {
          phase: 'Running analysis...',
          progress: 50,
          issuesFound: 0,
          estimatedTimeRemaining: 120
        };
      }
      return null;
    } catch (error) {
      logger.error(`Failed to get progress for audit ${auditId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Clean up old audit runs
   */
  async cleanupOldAudits(daysToKeep: number = 90): Promise<number> {
    try {
      const deletedCount = await this.storage.cleanupOldAudits(daysToKeep);
      logger.info(`Cleaned up ${deletedCount} old audit runs`);
      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup old audits:', error as Error);
      throw error;
    }
  }

  /**
   * Get issue trends over time
   */
  async getIssueTrends(days: number = 30): Promise<any> {
    try {
      // This would be implemented to show trends in issue counts over time
      // For now, return basic statistics
      return await this.getAuditStatistics(days);
    } catch (error) {
      logger.error('Failed to get issue trends:', error as Error);
      throw error;
    }
  }

  /**
   * Export audit results
   */
  async exportAuditResults(auditId: string, format: 'json' | 'csv' | 'pdf' = 'json'): Promise<Buffer | string> {
    try {
      const auditRun = await this.getAuditRun(auditId);
      if (!auditRun) {
        throw new Error(`Audit run ${auditId} not found`);
      }

      switch (format) {
        case 'json':
          return JSON.stringify(auditRun, null, 2);
        
        case 'csv':
          return this.convertToCSV(auditRun.issues);
        
        case 'pdf':
          // Would implement PDF generation
          throw new Error('PDF export not implemented yet');
        
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      logger.error(`Failed to export audit results for ${auditId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Convert issues to CSV format
   */
  private convertToCSV(issues: BaseIssue[]): string {
    const headers = [
      'Severity',
      'Category',
      'Title',
      'Description',
      'File Path',
      'Line Number',
      'Recommendation'
    ];

    const rows = issues.map(issue => [
      issue.severity,
      issue.category,
      `"${issue.title.replace(/"/g, '""')}"`,
      `"${issue.description.replace(/"/g, '""')}"`,
      issue.filePath,
      issue.lineNumber || '',
      `"${(issue.recommendation || '').replace(/"/g, '""')}"`
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }

  /**
   * Get audit health score
   */
  async getHealthScore(auditId?: string): Promise<number> {
    try {
      let issues: BaseIssue[];

      if (auditId) {
        const auditRun = await this.getAuditRun(auditId);
        if (!auditRun) {
          throw new Error(`Audit run ${auditId} not found`);
        }
        issues = auditRun.issues;
      } else {
        // Get issues from the most recent audit
        const recentAudits = await this.getAuditRuns({ page: 1, limit: 1 });
        if (!recentAudits.success || !recentAudits.data || recentAudits.data.length === 0) {
          return 100; // No audits = perfect score
        }
        
        const latestAudit = await this.getAuditRun(recentAudits.data[0].id);
        if (!latestAudit) {
          return 100;
        }
        issues = latestAudit.issues;
      }

      // Calculate health score based on issue severity
      const weights = {
        CRITICAL: 10,
        HIGH: 5,
        MEDIUM: 2,
        LOW: 1
      };

      const totalWeight = issues.reduce((sum, issue) => {
        return sum + (weights[issue.severity as keyof typeof weights] || 0);
      }, 0);

      // Convert to 0-100 scale (lower weight = higher score)
      const maxPossibleWeight = 1000; // Arbitrary max for scaling
      const score = Math.max(0, Math.min(100, 100 - (totalWeight / maxPossibleWeight) * 100));

      return Math.round(score);
    } catch (error) {
      logger.error('Failed to calculate health score:', error as Error);
      return 0;
    }
  }

  /**
   * Get recommendations based on audit results
   */
  async getRecommendations(auditId: string): Promise<string[]> {
    try {
      const auditRun = await this.getAuditRun(auditId);
      if (!auditRun) {
        throw new Error(`Audit run ${auditId} not found`);
      }

      const recommendations: string[] = [];

      // Analyze issue patterns and generate recommendations
      const criticalIssues = auditRun.issues.filter(i => i.severity === 'CRITICAL');
      const securityIssues = auditRun.issues.filter(i => i.category === 'SECURITY');
      const performanceIssues = auditRun.issues.filter(i => i.category === 'PERFORMANCE');

      if (criticalIssues.length > 0) {
        recommendations.push(`Address ${criticalIssues.length} critical issues immediately`);
      }

      if (securityIssues.length > 0) {
        recommendations.push(`Review ${securityIssues.length} security vulnerabilities`);
      }

      if (performanceIssues.length > 0) {
        recommendations.push(`Optimize ${performanceIssues.length} performance issues`);
      }

      if (auditRun.totalIssues > 100) {
        recommendations.push('Consider implementing automated code quality gates');
      }

      if (recommendations.length === 0) {
        recommendations.push('Great job! No major issues found');
      }

      return recommendations;
    } catch (error) {
      logger.error(`Failed to get recommendations for ${auditId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Close database connections
   */
  async disconnect(): Promise<void> {
    await this.storage.disconnect();
  }
}
