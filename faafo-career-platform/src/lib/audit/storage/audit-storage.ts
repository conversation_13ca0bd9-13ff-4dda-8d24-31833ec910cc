/**
 * Audit Storage Service
 * 
 * Handles all database operations for the audit system including
 * storing audit runs, issues, and providing query capabilities.
 */

import { PrismaClient } from '@prisma/client';
import {
  AuditRunResult,
  AuditRunSummary,
  BaseIssue,
  IssueFilter,
  IssueSortOptions,
  PaginatedIssues,
  AuditAPIResponse,
  AuditStatus,
  IssueSeverity,
  IssueCategory,
  IssueStatus
} from '@/lib/audit/types';
import { logger } from '@/lib/logger';

export class AuditStorage {
  private prisma: PrismaClient;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
  }

  /**
   * Create a new audit run record
   */
  async createAuditRun(auditRun: Partial<AuditRunResult>): Promise<void> {
    try {
      await this.prisma.auditRun.create({
        data: {
          id: auditRun.id!,
          status: auditRun.status || AuditStatus.PENDING,
          startedAt: auditRun.startedAt || new Date(),
          completedAt: auditRun.completedAt,
          totalIssues: auditRun.totalIssues || 0,
          criticalCount: auditRun.criticalCount || 0,
          highCount: auditRun.highCount || 0,
          mediumCount: auditRun.mediumCount || 0,
          lowCount: auditRun.lowCount || 0,
          triggeredBy: auditRun.metadata?.triggeredBy,
          metadata: auditRun.metadata ? auditRun.metadata : undefined
        }
      });

      logger.info(`Created audit run record: ${auditRun.id}`);
    } catch (error) {
      logger.error('Failed to create audit run:', error as Error);
      throw error;
    }
  }

  /**
   * Update an existing audit run record
   */
  async updateAuditRun(auditRunId: string, updates: Partial<AuditRunResult>): Promise<void> {
    try {
      await this.prisma.auditRun.update({
        where: { id: auditRunId },
        data: {
          status: updates.status,
          completedAt: updates.completedAt,
          totalIssues: updates.totalIssues,
          criticalCount: updates.criticalCount,
          highCount: updates.highCount,
          mediumCount: updates.mediumCount,
          lowCount: updates.lowCount,
          metadata: updates.metadata ? JSON.stringify(updates.metadata) : undefined
        }
      });

      logger.info(`Updated audit run: ${auditRunId}`);
    } catch (error) {
      logger.error(`Failed to update audit run ${auditRunId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Store issues for an audit run
   */
  async storeIssues(auditRunId: string, issues: BaseIssue[]): Promise<void> {
    try {
      const issueData = issues.map(issue => ({
        auditRunId,
        severity: issue.severity as IssueSeverity,
        category: issue.category as IssueCategory,
        title: issue.title,
        description: issue.description,
        filePath: issue.filePath,
        lineNumber: issue.lineNumber,
        columnNumber: issue.columnNumber,
        codeSnippet: issue.codeSnippet,
        recommendation: issue.recommendation,
        fixExample: issue.fixExample,
        status: IssueStatus.OPEN,
        metadata: issue.metadata ? issue.metadata : undefined
      }));

      await this.prisma.auditIssue.createMany({
        data: issueData
      });

      logger.info(`Stored ${issues.length} issues for audit run: ${auditRunId}`);
    } catch (error) {
      logger.error(`Failed to store issues for audit run ${auditRunId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get audit run by ID
   */
  async getAuditRun(auditRunId: string): Promise<AuditRunResult | null> {
    try {
      const auditRun = await this.prisma.auditRun.findUnique({
        where: { id: auditRunId },
        include: {
          issues: {
            orderBy: [
              { severity: 'desc' },
              { createdAt: 'desc' }
            ]
          }
        }
      });

      if (!auditRun) {
        return null;
      }

      return {
        id: auditRun.id,
        status: auditRun.status as AuditStatus,
        startedAt: auditRun.startedAt,
        completedAt: auditRun.completedAt || undefined,
        totalIssues: auditRun.totalIssues,
        criticalCount: auditRun.criticalCount,
        highCount: auditRun.highCount,
        mediumCount: auditRun.mediumCount,
        lowCount: auditRun.lowCount,
        issues: auditRun.issues.map(this.mapPrismaIssueToBaseIssue),
        metadata: auditRun.metadata ? JSON.parse(auditRun.metadata as string) : undefined
      };
    } catch (error) {
      logger.error(`Failed to get audit run ${auditRunId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get audit run summaries with pagination and filtering
   */
  async getAuditRuns(
    page: number = 1,
    limit: number = 20,
    filter: { status?: string; triggeredBy?: string } = {}
  ): Promise<AuditAPIResponse<AuditRunSummary[]>> {
    try {
      const offset = (page - 1) * limit;

      // Build where clause for filtering
      const where: any = {};
      if (filter.status) {
        where.status = filter.status;
      }
      if (filter.triggeredBy) {
        where.triggeredBy = filter.triggeredBy;
      }

      const [auditRuns, total] = await Promise.all([
        this.prisma.auditRun.findMany({
          where,
          skip: offset,
          take: limit,
          orderBy: { startedAt: 'desc' },
          include: {
            triggeredByUser: {
              select: { id: true, name: true, email: true }
            }
          }
        }),
        this.prisma.auditRun.count({ where })
      ]);

      const summaries: AuditRunSummary[] = auditRuns.map(run => ({
        id: run.id,
        status: run.status as AuditStatus,
        startedAt: run.startedAt,
        completedAt: run.completedAt || undefined,
        totalIssues: run.totalIssues,
        issuesBySeverity: {
          [IssueSeverity.CRITICAL]: run.criticalCount,
          [IssueSeverity.HIGH]: run.highCount,
          [IssueSeverity.MEDIUM]: run.mediumCount,
          [IssueSeverity.LOW]: run.lowCount
        },
        issuesByCategory: {
          [IssueCategory.SECURITY]: 0,
          [IssueCategory.PERFORMANCE]: 0,
          [IssueCategory.MAINTAINABILITY]: 0,
          [IssueCategory.TESTING]: 0,
          [IssueCategory.ARCHITECTURE]: 0,
          [IssueCategory.DOCUMENTATION]: 0,
          [IssueCategory.ACCESSIBILITY]: 0
        },
        triggeredBy: run.triggeredByUser?.name || run.triggeredBy || 'system'
      }));

      return {
        success: true,
        data: summaries,
        metadata: {
          total,
          page,
          limit
        }
      };
    } catch (error) {
      logger.error('Failed to get audit runs:', error as Error);
      return {
        success: false,
        error: 'Failed to retrieve audit runs'
      };
    }
  }

  /**
   * Get issues with filtering and pagination
   */
  async getIssues(
    filter: IssueFilter & {
      page?: number;
      limit?: number;
      auditRunId?: string;
      assignedToId?: string;
      search?: string;
    } = {},
    sort: IssueSortOptions = { field: 'severity', direction: 'desc' }
  ): Promise<any> {
    try {
      const page = filter.page || 1;
      const limit = filter.limit || 20;
      const offset = (page - 1) * limit;

      // Build where clause
      const where: any = {};

      if (filter.auditRunId) {
        where.auditRunId = filter.auditRunId;
      }

      if (filter.severity) {
        where.severity = filter.severity;
      }

      if (filter.category) {
        where.category = filter.category;
      }

      if (filter.status) {
        where.status = filter.status;
      }

      if (filter.filePath) {
        where.filePath = { contains: filter.filePath };
      }

      if (filter.assignedToId) {
        where.assignedToId = filter.assignedToId;
      }

      if (filter.search) {
        where.OR = [
          { title: { contains: filter.search, mode: 'insensitive' } },
          { description: { contains: filter.search, mode: 'insensitive' } },
          { filePath: { contains: filter.search, mode: 'insensitive' } }
        ];
      }

      if (filter.dateRange) {
        where.createdAt = {
          gte: filter.dateRange.start,
          lte: filter.dateRange.end
        };
      }

      // Build order by clause
      const orderBy: any = {};
      if (sort.field === 'severity') {
        // Custom severity ordering
        orderBy.severity = sort.direction;
      } else {
        orderBy[sort.field] = sort.direction;
      }

      const [issues, total, summary] = await Promise.all([
        this.prisma.auditIssue.findMany({
          where,
          skip: offset,
          take: limit,
          orderBy,
          include: {
            assignedTo: {
              select: { id: true, name: true, email: true }
            },
            auditRun: {
              select: { id: true, startedAt: true, status: true }
            },
            _count: {
              select: { comments: true }
            }
          }
        }),
        this.prisma.auditIssue.count({ where }),
        this.prisma.auditIssue.groupBy({
          by: ['severity', 'status'],
          _count: { severity: true }
        })
      ]);

      // Calculate summary statistics
      const summaryStats = {
        totalIssues: total,
        criticalCount: summary.filter(s => s.severity === IssueSeverity.CRITICAL).reduce((acc, s) => acc + s._count.severity, 0),
        highCount: summary.filter(s => s.severity === IssueSeverity.HIGH).reduce((acc, s) => acc + s._count.severity, 0),
        mediumCount: summary.filter(s => s.severity === IssueSeverity.MEDIUM).reduce((acc, s) => acc + s._count.severity, 0),
        lowCount: summary.filter(s => s.severity === IssueSeverity.LOW).reduce((acc, s) => acc + s._count.severity, 0),
        openCount: summary.filter(s => s.status === IssueStatus.OPEN).reduce((acc, s) => acc + s._count.severity, 0),
        resolvedCount: summary.filter(s => s.status === IssueStatus.RESOLVED).reduce((acc, s) => acc + s._count.severity, 0)
      };

      return {
        issues,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        summary: summaryStats
      };
    } catch (error) {
      logger.error('Failed to get issues:', error as Error);
      throw error;
    }
  }

  /**
   * Update issue status
   */
  async updateIssueStatus(
    issueId: string,
    status: IssueStatus,
    assignedToId?: string,
    resolvedAt?: Date
  ): Promise<void> {
    try {
      await this.prisma.auditIssue.update({
        where: { id: issueId },
        data: {
          status,
          assignedToId,
          resolvedAt: status === IssueStatus.RESOLVED ? (resolvedAt || new Date()) : null
        }
      });

      logger.info(`Updated issue ${issueId} status to ${status}`);
    } catch (error) {
      logger.error(`Failed to update issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Add comment to issue
   */
  async addIssueComment(issueId: string, userId: string, comment: string): Promise<any> {
    try {
      const newComment = await this.prisma.issueComment.create({
        data: {
          issueId,
          userId,
          comment
        },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      logger.info(`Added comment to issue ${issueId}`);
      return newComment;
    } catch (error) {
      logger.error(`Failed to add comment to issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get specific issue by ID
   */
  async getIssue(issueId: string): Promise<any | null> {
    try {
      return await this.prisma.auditIssue.findUnique({
        where: { id: issueId },
        include: {
          assignedTo: {
            select: { id: true, name: true, email: true }
          },
          auditRun: {
            select: { id: true, startedAt: true, status: true }
          },
          comments: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            },
            orderBy: { createdAt: 'asc' }
          }
        }
      });
    } catch (error) {
      logger.error(`Failed to get issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Update issue
   */
  async updateIssue(
    issueId: string,
    updateData: {
      status?: IssueStatus;
      assignedToId?: string | null;
      falsePositive?: boolean;
      resolution?: string;
    }
  ): Promise<any | null> {
    try {
      const data: any = {};

      if (updateData.status !== undefined) {
        data.status = updateData.status;
        if (updateData.status === IssueStatus.RESOLVED) {
          data.resolvedAt = new Date();
        }
      }

      if (updateData.assignedToId !== undefined) {
        data.assignedToId = updateData.assignedToId;
      }

      if (updateData.falsePositive !== undefined) {
        data.falsePositive = updateData.falsePositive;
      }

      return await this.prisma.auditIssue.update({
        where: { id: issueId },
        data,
        include: {
          assignedTo: {
            select: { id: true, name: true, email: true }
          }
        }
      });
    } catch (error) {
      logger.error(`Failed to update issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Delete issue
   */
  async deleteIssue(issueId: string): Promise<boolean> {
    try {
      await this.prisma.auditIssue.delete({
        where: { id: issueId }
      });
      return true;
    } catch (error) {
      logger.error(`Failed to delete issue ${issueId}:`, error as Error);
      return false;
    }
  }

  /**
   * Cancel audit run
   */
  async cancelAuditRun(auditId: string): Promise<boolean> {
    try {
      await this.prisma.auditRun.update({
        where: { id: auditId },
        data: {
          status: AuditStatus.FAILED,
          completedAt: new Date()
        }
      });
      return true;
    } catch (error) {
      logger.error(`Failed to cancel audit run ${auditId}:`, error as Error);
      return false;
    }
  }

  /**
   * Get issue comments with pagination
   */
  async getIssueComments(issueId: string, page: number = 1, limit: number = 20): Promise<any> {
    try {
      const offset = (page - 1) * limit;

      const [comments, total] = await Promise.all([
        this.prisma.issueComment.findMany({
          where: { issueId },
          skip: offset,
          take: limit,
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { createdAt: 'asc' }
        }),
        this.prisma.issueComment.count({ where: { issueId } })
      ]);

      return {
        comments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`Failed to get comments for issue ${issueId}:`, error as Error);
      throw error;
    }
  }

  /**
   * Get audit statistics
   */
  async getAuditStatistics(days: number = 30): Promise<any> {
    try {
      const since = new Date();
      since.setDate(since.getDate() - days);

      const [totalRuns, totalIssues, issuesBySeverity, issuesByCategory] = await Promise.all([
        this.prisma.auditRun.count({
          where: { startedAt: { gte: since } }
        }),
        this.prisma.auditIssue.count({
          where: { createdAt: { gte: since } }
        }),
        this.prisma.auditIssue.groupBy({
          by: ['severity'],
          where: { createdAt: { gte: since } },
          _count: { severity: true }
        }),
        this.prisma.auditIssue.groupBy({
          by: ['category'],
          where: { createdAt: { gte: since } },
          _count: { category: true }
        })
      ]);

      return {
        totalRuns,
        totalIssues,
        issuesBySeverity: issuesBySeverity.reduce((acc, item) => {
          acc[item.severity] = item._count.severity;
          return acc;
        }, {} as Record<string, number>),
        issuesByCategory: issuesByCategory.reduce((acc, item) => {
          acc[item.category] = item._count.category;
          return acc;
        }, {} as Record<string, number>)
      };
    } catch (error) {
      logger.error('Failed to get audit statistics:', error as Error);
      throw error;
    }
  }

  /**
   * Delete old audit runs and their issues
   */
  async cleanupOldAudits(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const deletedRuns = await this.prisma.auditRun.deleteMany({
        where: {
          startedAt: { lt: cutoffDate },
          status: { in: [AuditStatus.COMPLETED, AuditStatus.FAILED] }
        }
      });

      logger.info(`Cleaned up ${deletedRuns.count} old audit runs`);
      return deletedRuns.count;
    } catch (error) {
      logger.error('Failed to cleanup old audits:', error as Error);
      throw error;
    }
  }

  /**
   * Map Prisma issue to BaseIssue
   */
  private mapPrismaIssueToBaseIssue(prismaIssue: any): BaseIssue {
    return {
      severity: prismaIssue.severity as IssueSeverity,
      category: prismaIssue.category as IssueCategory,
      title: prismaIssue.title,
      description: prismaIssue.description,
      filePath: prismaIssue.filePath,
      lineNumber: prismaIssue.lineNumber,
      columnNumber: prismaIssue.columnNumber,
      codeSnippet: prismaIssue.codeSnippet,
      recommendation: prismaIssue.recommendation,
      fixExample: prismaIssue.fixExample,
      metadata: prismaIssue.metadata ? JSON.parse(prismaIssue.metadata) : undefined
    };
  }

  /**
   * Close database connection
   */
  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }
}
