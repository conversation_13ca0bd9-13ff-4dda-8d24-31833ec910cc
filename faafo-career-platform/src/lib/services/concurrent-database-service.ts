/**
 * Concurrent Database Operations Service
 * Optimizes database operations through concurrent processing, connection pooling,
 * and intelligent query batching for the Skills Analysis API
 */

import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { optimizedDatabaseService } from '@/lib/services/optimized-database-service';

interface DatabaseOperation<T = any> {
  id: string;
  operation: () => Promise<T>;
  priority: 'high' | 'medium' | 'low';
  timeout: number;
  retries: number;
  dependencies?: string[];
}

interface BatchDatabaseResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  operationId: string;
  executionTime: number;
}

interface DatabaseMetrics {
  totalOperations: number;
  concurrentOperations: number;
  averageExecutionTime: number;
  successRate: number;
  connectionPoolUtilization: number;
  queryOptimizationSavings: number;
}

export class ConcurrentDatabaseService {
  private static instance: ConcurrentDatabaseService;
  private operationQueue: Map<string, DatabaseOperation> = new Map();
  private executingOperations: Set<string> = new Set();
  private completedOperations: Map<string, any> = new Map();
  private metrics: DatabaseMetrics = {
    totalOperations: 0,
    concurrentOperations: 0,
    averageExecutionTime: 0,
    successRate: 0,
    connectionPoolUtilization: 0,
    queryOptimizationSavings: 0,
  };

  private readonly maxConcurrentOperations = parseInt(process.env.MAX_CONCURRENT_DB_OPS || '5');
  private readonly defaultTimeout = parseInt(process.env.DB_OPERATION_TIMEOUT || '30000');

  private constructor() {
    // Initialize operation processor
    setInterval(() => this.processOperationQueue(), 100);
    
    // Initialize metrics collection
    setInterval(() => this.updateMetrics(), 5000);
  }

  public static getInstance(): ConcurrentDatabaseService {
    if (!ConcurrentDatabaseService.instance) {
      ConcurrentDatabaseService.instance = new ConcurrentDatabaseService();
    }
    return ConcurrentDatabaseService.instance;
  }

  /**
   * Execute multiple database operations concurrently
   */
  async executeConcurrent<T>(operations: DatabaseOperation<T>[]): Promise<BatchDatabaseResult<T>[]> {
    const startTime = Date.now();
    const operationIds = operations.map(op => op.id);

    try {
      // Add operations to queue
      operations.forEach(op => {
        this.operationQueue.set(op.id, op);
        this.metrics.totalOperations++;
      });

      // Wait for all operations to complete
      const results = await Promise.allSettled(
        operations.map(op => this.waitForOperation<T>(op.id))
      );

      // Process results
      const batchResults: BatchDatabaseResult<T>[] = results.map((result, index) => {
        const operationId = operationIds[index];
        const executionTime = Date.now() - startTime;

        if (result.status === 'fulfilled') {
          return {
            success: true,
            data: result.value,
            operationId,
            executionTime,
          };
        } else {
          return {
            success: false,
            error: result.reason?.message || 'Operation failed',
            operationId,
            executionTime,
          };
        }
      });

      return batchResults;

    } catch (error) {
      console.error('Concurrent database execution failed:', error);
      throw error;
    }
  }

  /**
   * Optimized skill gap analysis creation with concurrent operations
   */
  async createSkillGapAnalysisOptimized(
    userId: string,
    requestData: any,
    analysisData: any,
    careerPathData: any
  ): Promise<any> {
    const operations: DatabaseOperation[] = [
      {
        id: 'create_analysis',
        operation: () => this.createAnalysisRecord(userId, requestData, analysisData),
        priority: 'high',
        timeout: this.defaultTimeout,
        retries: 2,
      },
      {
        id: 'fetch_user_profile',
        operation: () => this.fetchUserProfile(userId),
        priority: 'medium',
        timeout: 5000,
        retries: 1,
      },
      {
        id: 'update_user_stats',
        operation: () => this.updateUserAnalysisStats(userId),
        priority: 'low',
        timeout: 5000,
        retries: 1,
        dependencies: ['create_analysis'],
      },
    ];

    const results = await this.executeConcurrent(operations);
    
    // Extract the main analysis result
    const analysisResult = results.find(r => r.operationId === 'create_analysis');
    if (!analysisResult?.success) {
      throw new Error(analysisResult?.error || 'Failed to create skill gap analysis');
    }

    return analysisResult.data;
  }

  /**
   * Batch fetch user assessments with optimization
   */
  async fetchUserAssessmentsOptimized(userId: string): Promise<any[]> {
    const operations: DatabaseOperation[] = [
      {
        id: 'fetch_skill_assessments_optimized',
        operation: () => optimizedDatabaseService.getUserSkillAssessmentsOptimized(userId, 50),
        priority: 'high',
        timeout: 5000, // Reduced timeout due to optimization
        retries: 2,
      },
      {
        id: 'fetch_career_assessments_optimized',
        operation: () => optimizedDatabaseService.getCareerAssessmentOptimized(userId, 'COMPLETED'),
        priority: 'high',
        timeout: 5000, // Reduced timeout due to optimization
        retries: 2,
      },
      {
        id: 'fetch_learning_progress',
        operation: () => this.fetchLearningProgress(userId),
        priority: 'medium',
        timeout: 5000,
        retries: 1,
      },
    ];

    const results = await this.executeConcurrent(operations);
    
    // Combine results
    const skillAssessments = results.find(r => r.operationId === 'fetch_skill_assessments_optimized')?.data || [];
    const careerAssessments = results.find(r => r.operationId === 'fetch_career_assessments_optimized')?.data || [];
    const learningProgress = results.find(r => r.operationId === 'fetch_learning_progress')?.data || [];

    return [...skillAssessments, ...careerAssessments, ...learningProgress];
  }

  /**
   * Optimized career path data fetching
   */
  async fetchCareerPathDataOptimized(careerPathId: string): Promise<any> {
    const operations: DatabaseOperation[] = [
      {
        id: 'fetch_career_path_optimized',
        operation: () => this.fetchCareerPathOptimized(careerPathId),
        priority: 'high',
        timeout: 3000, // Reduced timeout due to optimization
        retries: 2,
      },
      {
        id: 'fetch_related_skills',
        operation: () => this.fetchCareerPathSkills(careerPathId),
        priority: 'high',
        timeout: 5000,
        retries: 2,
      },
      {
        id: 'fetch_learning_paths',
        operation: () => this.fetchCareerPathLearningPaths(careerPathId),
        priority: 'medium',
        timeout: 5000,
        retries: 1,
      },
      {
        id: 'fetch_market_data',
        operation: () => this.fetchCareerPathMarketData(careerPathId),
        priority: 'low',
        timeout: 10000,
        retries: 1,
      },
    ];

    const results = await this.executeConcurrent(operations);
    
    // Combine results into enhanced career path data
    const careerPath = results.find(r => r.operationId === 'fetch_career_path_optimized')?.data;
    const skills = results.find(r => r.operationId === 'fetch_related_skills')?.data || [];
    const learningPaths = results.find(r => r.operationId === 'fetch_learning_paths')?.data || [];
    const marketData = results.find(r => r.operationId === 'fetch_market_data')?.data;

    if (!careerPath) {
      throw new Error('Career path not found');
    }

    return {
      ...careerPath,
      relatedSkills: skills,
      learningPaths,
      marketData,
    };
  }

  /**
   * Individual database operations
   */
  private async createAnalysisRecord(userId: string, requestData: any, analysisData: any): Promise<any> {
    const expiresAt = new Date();
    expiresAt.setMonth(expiresAt.getMonth() + 3);

    return await prisma.skillGapAnalysis.create({
      data: {
        userId,
        targetCareerPathId: requestData.targetCareerPath?.careerPathId || null,
        targetCareerPathName: requestData.targetCareerPath?.careerPathName,
        experienceLevel: requestData.targetCareerPath?.targetLevel,
        timeframe: requestData.preferences?.timeframe,
        analysisData: analysisData,
        skillGaps: analysisData.skillGaps || [],
        learningPlan: analysisData.learningPlan || {},
        marketData: analysisData.marketInsights || null,
        progressTracking: {
          milestones: analysisData.learningPlan?.milestones || [],
          completedMilestones: [],
          currentPhase: 'planning',
        },
        status: 'ACTIVE',
        completionPercentage: 0,
        expiresAt,
      },
    });
  }

  private async fetchUserProfile(userId: string): Promise<any> {
    return await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        profile: {
          select: {
            firstName: true,
            lastName: true,
            experienceLevel: true,
            jobTitle: true,
            company: true,
            currentIndustry: true,
          },
        },
      },
    });
  }

  private async updateUserAnalysisStats(userId: string): Promise<any> {
    return await prisma.user.update({
      where: { id: userId },
      data: {
        profile: {
          update: {
            lastProfileUpdate: new Date(),
            lastActiveAt: new Date(),
          },
        },
      },
    });
  }

  private async fetchSkillAssessments(userId: string): Promise<any[]> {
    return await prisma.skillAssessment.findMany({
      where: { userId },
      include: {
        skill: {
          select: {
            id: true,
            name: true,
            category: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 50,
    });
  }

  private async fetchCareerAssessments(userId: string): Promise<any[]> {
    return await prisma.assessment.findMany({
      where: { userId },
      include: {
        responses: {
          select: {
            questionKey: true,
            answerValue: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });
  }

  private async fetchLearningProgress(userId: string): Promise<any[]> {
    return await prisma.userLearningProgress.findMany({
      where: { userId },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
            skillLevel: true,
            category: true,
            type: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
      take: 20,
    });
  }

  private async fetchCareerPath(careerPathId: string): Promise<any> {
    return await prisma.careerPath.findUnique({
      where: { id: careerPathId },
      include: {
        _count: {
          select: {
            relatedSkills: true,
            learningPaths: true,
          },
        },
      },
    });
  }

  /**
   * Optimized career path fetching with selective loading
   */
  private async fetchCareerPathOptimized(careerPathId: string): Promise<any> {
    return await prisma.careerPath.findUnique({
      where: { id: careerPathId },
      select: {
        id: true,
        name: true,
        slug: true,
        overview: true,
        pros: true,
        cons: true,
        actionableSteps: true,
        isActive: true,
        _count: {
          select: {
            relatedSkills: true,
            learningPaths: true,
          },
        },
      },
    });
  }

  private async fetchCareerPathSkills(careerPathId: string): Promise<any[]> {
    return await prisma.skill.findMany({
      where: {
        careerPaths: {
          some: { id: careerPathId },
        },
      },
      select: {
        id: true,
        name: true,
        category: true,
        description: true,
      },
      take: 20,
    });
  }

  private async fetchCareerPathLearningPaths(careerPathId: string): Promise<any[]> {
    return await prisma.learningPath.findMany({
      where: {
        careerPaths: {
          some: { id: careerPathId },
        },
      },
      include: {
        _count: {
          select: { steps: true },
        },
        skills: {
          select: {
            name: true,
          },
          take: 5,
        },
      },
      take: 10,
    });
  }

  private async fetchCareerPathMarketData(careerPathId: string): Promise<any> {
    // This would fetch market data from external APIs or cached data
    // For now, return null as this is typically handled by external services
    return null;
  }

  /**
   * Operation queue processing
   */
  private async processOperationQueue(): Promise<void> {
    if (this.executingOperations.size >= this.maxConcurrentOperations) {
      return;
    }

    // Find next operation to execute
    const availableOperations = Array.from(this.operationQueue.values())
      .filter(op => !this.executingOperations.has(op.id))
      .filter(op => this.areDependenciesMet(op))
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

    if (availableOperations.length === 0) {
      return;
    }

    const operation = availableOperations[0];
    this.executeOperation(operation);
  }

  private async executeOperation(operation: DatabaseOperation): Promise<void> {
    this.executingOperations.add(operation.id);
    this.metrics.concurrentOperations = this.executingOperations.size;

    const startTime = Date.now();

    try {
      const result = await Promise.race([
        operation.operation(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Operation timeout')), operation.timeout)
        ),
      ]);

      this.completedOperations.set(operation.id, result);
      this.operationQueue.delete(operation.id);

      const executionTime = Date.now() - startTime;
      this.updateExecutionMetrics(executionTime, true);

    } catch (error) {
      console.error(`Database operation ${operation.id} failed:`, error);
      
      if (operation.retries > 0) {
        operation.retries--;
        // Re-queue with lower priority
        operation.priority = 'low';
      } else {
        this.completedOperations.set(operation.id, { error: (error as Error).message });
        this.operationQueue.delete(operation.id);
      }

      const executionTime = Date.now() - startTime;
      this.updateExecutionMetrics(executionTime, false);

    } finally {
      this.executingOperations.delete(operation.id);
      this.metrics.concurrentOperations = this.executingOperations.size;
    }
  }

  private areDependenciesMet(operation: DatabaseOperation): boolean {
    if (!operation.dependencies) return true;
    
    return operation.dependencies.every(depId => 
      this.completedOperations.has(depId)
    );
  }

  private async waitForOperation<T>(operationId: string): Promise<T> {
    return new Promise((resolve, reject) => {
      const checkCompletion = () => {
        if (this.completedOperations.has(operationId)) {
          const result = this.completedOperations.get(operationId);
          if (result.error) {
            reject(new Error(result.error));
          } else {
            resolve(result);
          }
        } else if (!this.operationQueue.has(operationId) && !this.executingOperations.has(operationId)) {
          reject(new Error('Operation not found'));
        } else {
          setTimeout(checkCompletion, 50);
        }
      };
      checkCompletion();
    });
  }

  private updateExecutionMetrics(executionTime: number, success: boolean): void {
    const totalTime = this.metrics.averageExecutionTime * (this.metrics.totalOperations - 1);
    this.metrics.averageExecutionTime = (totalTime + executionTime) / this.metrics.totalOperations;
    
    if (success) {
      this.metrics.successRate = ((this.metrics.successRate * (this.metrics.totalOperations - 1)) + 1) / this.metrics.totalOperations;
    } else {
      this.metrics.successRate = (this.metrics.successRate * (this.metrics.totalOperations - 1)) / this.metrics.totalOperations;
    }
  }

  private updateMetrics(): void {
    // Update connection pool utilization (simplified)
    this.metrics.connectionPoolUtilization = (this.executingOperations.size / this.maxConcurrentOperations) * 100;
  }

  /**
   * Get performance metrics
   */
  getMetrics(): DatabaseMetrics & {
    queueSize: number;
    executingOperations: number;
    completedOperations: number;
  } {
    return {
      ...this.metrics,
      queueSize: this.operationQueue.size,
      executingOperations: this.executingOperations.size,
      completedOperations: this.completedOperations.size,
    };
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const metrics = this.getMetrics();
      return metrics.queueSize < 50 && 
             metrics.executingOperations < this.maxConcurrentOperations &&
             metrics.successRate > 0.8;
    } catch (error) {
      console.error('Concurrent database service health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const concurrentDatabaseService = ConcurrentDatabaseService.getInstance();
