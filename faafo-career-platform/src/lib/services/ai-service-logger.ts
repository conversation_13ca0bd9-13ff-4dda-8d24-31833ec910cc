/**
 * Enhanced logging utility for AI services
 * Extracted from geminiService.ts for better organization
 */

import { AIServiceLogLevel, AIServiceLogEntry, AITaskType, AIServiceError } from '@/lib/services/ai-service-types';

export class AIServiceLogger {
  private static logLevel: AIServiceLogLevel = process.env.NODE_ENV === 'development' ? 'debug' : 'info';
  private static logs: AIServiceLogEntry[] = [];
  private static maxLogs = 1000; // Keep last 1000 logs in memory

  static setLogLevel(level: AIServiceLogLevel): void {
    this.logLevel = level;
  }

  static getLogLevel(): AIServiceLogLevel {
    return this.logLevel;
  }

  static info(message: string, context?: any, taskType?: AITaskType): void {
    this.log('info', message, context, taskType);
  }

  static debug(message: string, context?: any, taskType?: AITaskType): void {
    this.log('debug', message, context, taskType);
  }

  static warn(message: string, context?: any, taskType?: AITaskType): void {
    this.log('warn', message, context, taskType);
  }

  static error(message: string, error?: AIServiceError | Error, context?: any, taskType?: AITaskType): void {
    const errorContext = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...context
    } : error;

    this.log('error', message, errorContext, taskType);
  }

  static logPerformance(
    taskType: AITaskType,
    duration: number,
    success: boolean,
    context?: any
  ): void {
    const message = `${taskType} completed in ${duration}ms - ${success ? 'SUCCESS' : 'FAILED'}`;
    this.log('info', message, { ...context, duration, success }, taskType);
  }

  static logRateLimit(taskType: AITaskType, remainingRequests: number): void {
    const message = `Rate limit check for ${taskType} - ${remainingRequests} requests remaining`;
    this.log('debug', message, { remainingRequests }, taskType);
  }

  static logCacheHit(taskType: AITaskType, cacheKey: string): void {
    const message = `Cache hit for ${taskType}`;
    this.log('debug', message, { cacheKey }, taskType);
  }

  static logCacheMiss(taskType: AITaskType, cacheKey: string): void {
    const message = `Cache miss for ${taskType}`;
    this.log('debug', message, { cacheKey }, taskType);
  }

  private static log(
    level: AIServiceLogLevel,
    message: string,
    context?: any,
    taskType?: AITaskType,
    duration?: number,
    error?: AIServiceError
  ): void {
    const logEntry: AIServiceLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      taskType,
      duration,
      error
    };

    // Add to in-memory logs
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift(); // Remove oldest log
    }

    // Console output based on log level
    if (this.shouldLog(level)) {
      const prefix = `[AI-Service] ${logEntry.timestamp} ${level.toUpperCase()}:`;
      
      switch (level) {
        case 'debug':
          console.debug(prefix, message, context || '');
          break;
        case 'info':
          console.log(prefix, message, context || '');
          break;
        case 'warn':
          console.warn(prefix, message, context || '');
          break;
        case 'error':
          console.error(prefix, message, context || '');
          break;
      }
    }
  }

  private static shouldLog(level: AIServiceLogLevel): boolean {
    const levels: Record<AIServiceLogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };

    return levels[level] >= levels[this.logLevel];
  }

  static getLogs(
    level?: AIServiceLogLevel,
    taskType?: AITaskType,
    limit?: number
  ): AIServiceLogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    if (taskType) {
      filteredLogs = filteredLogs.filter(log => log.taskType === taskType);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return filteredLogs;
  }

  static clearLogs(): void {
    this.logs = [];
  }

  static getLogStats(): {
    total: number;
    byLevel: Record<AIServiceLogLevel, number>;
    byTaskType: Record<string, number>;
  } {
    const stats = {
      total: this.logs.length,
      byLevel: { debug: 0, info: 0, warn: 0, error: 0 } as Record<AIServiceLogLevel, number>,
      byTaskType: {} as Record<string, number>
    };

    this.logs.forEach(log => {
      stats.byLevel[log.level]++;
      if (log.taskType) {
        stats.byTaskType[log.taskType] = (stats.byTaskType[log.taskType] || 0) + 1;
      }
    });

    return stats;
  }
}
