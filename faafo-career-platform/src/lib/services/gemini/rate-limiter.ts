/**
 * Gemini Rate Limiter Module
 * Rate limiting functionality for Gemini API requests
 */

export interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour?: number;
  requestsPerDay?: number;
  burstLimit?: number;
}

export interface RateLimitEntry {
  requests: number[];
  lastRequest: number;
  totalRequests: number;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

export class GeminiRateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map();
  private config: Required<RateLimitConfig>;
  private cleanupInterval: NodeJS.Timeout;

  constructor(config: RateLimitConfig) {
    this.config = {
      requestsPerMinute: config.requestsPerMinute,
      requestsPerHour: config.requestsPerHour || config.requestsPerMinute * 60,
      requestsPerDay: config.requestsPerDay || config.requestsPerMinute * 60 * 24,
      burstLimit: config.burstLimit || Math.ceil(config.requestsPerMinute / 4),
    };

    // Clean up old entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Check if request is allowed for user
   */
  checkLimit(userId: string): RateLimitResult {
    const now = Date.now();
    const entry = this.limits.get(userId) || {
      requests: [],
      lastRequest: 0,
      totalRequests: 0,
    };

    // Clean old requests (older than 1 day)
    const dayAgo = now - 24 * 60 * 60 * 1000;
    entry.requests = entry.requests.filter(time => time > dayAgo);

    // Check different time windows
    const minuteAgo = now - 60 * 1000;
    const hourAgo = now - 60 * 60 * 1000;

    const requestsInLastMinute = entry.requests.filter(time => time > minuteAgo).length;
    const requestsInLastHour = entry.requests.filter(time => time > hourAgo).length;
    const requestsInLastDay = entry.requests.length;

    // Check burst limit (requests in last 10 seconds)
    const tenSecondsAgo = now - 10 * 1000;
    const requestsInLast10Seconds = entry.requests.filter(time => time > tenSecondsAgo).length;

    // Determine if request is allowed
    let allowed = true;
    let retryAfter: number | undefined;

    if (requestsInLast10Seconds >= this.config.burstLimit) {
      allowed = false;
      retryAfter = 10; // Wait 10 seconds
    } else if (requestsInLastMinute >= this.config.requestsPerMinute) {
      allowed = false;
      retryAfter = 60; // Wait 1 minute
    } else if (requestsInLastHour >= this.config.requestsPerHour) {
      allowed = false;
      retryAfter = 3600; // Wait 1 hour
    } else if (requestsInLastDay >= this.config.requestsPerDay) {
      allowed = false;
      retryAfter = 24 * 3600; // Wait 1 day
    }

    if (allowed) {
      // Record the request
      entry.requests.push(now);
      entry.lastRequest = now;
      entry.totalRequests++;
      this.limits.set(userId, entry);
    }

    return {
      allowed,
      remaining: Math.max(0, this.config.requestsPerMinute - requestsInLastMinute),
      resetTime: minuteAgo + 60 * 1000,
      retryAfter,
    };
  }

  /**
   * Get rate limit status for user
   */
  getStatus(userId: string): {
    requestsInLastMinute: number;
    requestsInLastHour: number;
    requestsInLastDay: number;
    totalRequests: number;
    limits: Required<RateLimitConfig>;
  } {
    const now = Date.now();
    const entry = this.limits.get(userId) || {
      requests: [],
      lastRequest: 0,
      totalRequests: 0,
    };

    const minuteAgo = now - 60 * 1000;
    const hourAgo = now - 60 * 60 * 1000;
    const dayAgo = now - 24 * 60 * 60 * 1000;

    return {
      requestsInLastMinute: entry.requests.filter(time => time > minuteAgo).length,
      requestsInLastHour: entry.requests.filter(time => time > hourAgo).length,
      requestsInLastDay: entry.requests.filter(time => time > dayAgo).length,
      totalRequests: entry.totalRequests,
      limits: this.config,
    };
  }

  /**
   * Reset rate limit for user
   */
  reset(userId: string): void {
    this.limits.delete(userId);
  }

  /**
   * Reset all rate limits
   */
  resetAll(): void {
    this.limits.clear();
  }

  /**
   * Get all active users with their limits
   */
  getActiveUsers(): Array<{
    userId: string;
    requestsInLastMinute: number;
    requestsInLastHour: number;
    lastRequest: number;
  }> {
    const now = Date.now();
    const minuteAgo = now - 60 * 1000;
    const hourAgo = now - 60 * 60 * 1000;

    return Array.from(this.limits.entries()).map(([userId, entry]) => ({
      userId,
      requestsInLastMinute: entry.requests.filter(time => time > minuteAgo).length,
      requestsInLastHour: entry.requests.filter(time => time > hourAgo).length,
      lastRequest: entry.lastRequest,
    }));
  }

  /**
   * Get rate limiter statistics
   */
  getStats(): {
    totalUsers: number;
    activeUsers: number;
    totalRequests: number;
    requestsInLastHour: number;
  } {
    const now = Date.now();
    const hourAgo = now - 60 * 60 * 1000;
    
    let totalRequests = 0;
    let requestsInLastHour = 0;
    let activeUsers = 0;

    for (const entry of Array.from(this.limits.values())) {
      totalRequests += entry.totalRequests;
      const recentRequests = entry.requests.filter(time => time > hourAgo);
      requestsInLastHour += recentRequests.length;

      if (recentRequests.length > 0) {
        activeUsers++;
      }
    }

    return {
      totalUsers: this.limits.size,
      activeUsers,
      totalRequests,
      requestsInLastHour,
    };
  }

  /**
   * Clean up old entries
   */
  private cleanup(): void {
    const now = Date.now();
    const dayAgo = now - 24 * 60 * 60 * 1000;
    let cleaned = 0;

    for (const [userId, entry] of Array.from(this.limits.entries())) {
      // Remove entries with no recent requests
      if (entry.lastRequest < dayAgo) {
        this.limits.delete(userId);
        cleaned++;
      } else {
        // Clean old request timestamps
        const oldLength = entry.requests.length;
        entry.requests = entry.requests.filter(time => time > dayAgo);
        
        if (entry.requests.length === 0 && entry.lastRequest < dayAgo) {
          this.limits.delete(userId);
          cleaned++;
        }
      }
    }

    if (cleaned > 0) {
      console.log(`Rate limiter cleanup: removed ${cleaned} old entries`);
    }
  }

  /**
   * Destroy rate limiter and cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.resetAll();
  }
}
