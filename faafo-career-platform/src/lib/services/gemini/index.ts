/**
 * Gemini Service Module Exports
 * Centralized exports for all Gemini service modules
 */

// Core client
export { GeminiCoreClient, type GeminiConfig, type GeminiRequest, type GeminiResponse } from './core-client';

// Prompt templates
export { 
  GeminiPromptTemplates,
  type SkillsAnalysisInput,
  type ResumeAnalysisInput,
  type CareerRecommendationInput,
  type InterviewQuestionInput
} from './prompt-templates';

// Response parser
export {
  GeminiResponseParser,
  type ParsedResponse,
  type SkillsAnalysisResponse,
  type ResumeAnalysisResponse,
  type CareerRecommendation,
  type InterviewQuestion
} from './response-parser';

// Cache manager
export {
  GeminiCacheManager,
  type CacheEntry,
  type CacheOptions,
  type CacheStats
} from './cache-manager';

// Rate limiter
export {
  GeminiRateLimiter,
  type RateLimitConfig,
  type RateLimitEntry,
  type RateLimitResult
} from './rate-limiter';

// Main refactored service
export {
  RefactoredGeminiService,
  refactoredGeminiService,
  type RefactoredGeminiServiceConfig
} from './refactored-service';

// Legacy compatibility - export the refactored service as the main service
export { refactoredGeminiService as geminiService } from './refactored-service';
