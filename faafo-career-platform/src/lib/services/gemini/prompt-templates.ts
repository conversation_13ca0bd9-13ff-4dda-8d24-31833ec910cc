/**
 * Gemini Prompt Templates Module
 * All prompt templates and generation logic
 */

export interface SkillsAnalysisInput {
  skills: string[];
  careerPath?: string;
  experienceLevel?: string;
}

export interface ResumeAnalysisInput {
  resumeText: string;
  targetRole?: string;
}

export interface CareerRecommendationInput {
  currentRole?: string;
  skills: string[];
  interests: string[];
  experience: string;
}

export interface InterviewQuestionInput {
  role: string;
  experienceLevel: string;
  questionType: string;
  count: number;
}

export class GeminiPromptTemplates {
  /**
   * Generate skills gap analysis prompt
   */
  static generateSkillsAnalysisPrompt(input: SkillsAnalysisInput): string {
    const { skills, careerPath, experienceLevel } = input;
    
    return `
Analyze the following skills for a ${experienceLevel || 'professional'} ${careerPath ? `in ${careerPath}` : ''}:

Skills: ${skills.join(', ')}

Please provide a comprehensive analysis including:
1. Skill strengths and areas for improvement
2. Market demand for these skills
3. Recommended learning path
4. Skill gaps for career advancement
5. Industry-specific insights

Format the response as JSON with the following structure:
{
  "strengths": ["skill1", "skill2"],
  "improvements": ["area1", "area2"],
  "marketDemand": "high|medium|low",
  "learningPath": ["step1", "step2"],
  "skillGaps": ["gap1", "gap2"],
  "insights": "detailed analysis"
}
    `.trim();
  }

  /**
   * Generate resume analysis prompt
   */
  static generateResumeAnalysisPrompt(input: ResumeAnalysisInput): string {
    const { resumeText, targetRole } = input;
    
    return `
Analyze the following resume ${targetRole ? `for a ${targetRole} position` : ''}:

${resumeText}

Please provide a comprehensive analysis including:
1. Overall resume strength (score 1-10)
2. Key strengths and achievements
3. Areas for improvement
4. Missing skills or experience
5. Formatting and presentation feedback
6. ATS optimization suggestions

Format the response as JSON with the following structure:
{
  "overallScore": 8,
  "strengths": ["strength1", "strength2"],
  "improvements": ["improvement1", "improvement2"],
  "missingSkills": ["skill1", "skill2"],
  "formatting": "feedback on formatting",
  "atsOptimization": ["suggestion1", "suggestion2"],
  "summary": "overall assessment"
}
    `.trim();
  }

  /**
   * Generate career recommendations prompt
   */
  static generateCareerRecommendationsPrompt(input: CareerRecommendationInput): string {
    const { currentRole, skills, interests, experience } = input;
    
    return `
Based on the following profile, provide career recommendations:

Current Role: ${currentRole || 'Not specified'}
Skills: ${skills.join(', ')}
Interests: ${interests.join(', ')}
Experience: ${experience}

Please provide:
1. Top 5 career path recommendations
2. Reasoning for each recommendation
3. Required skills for each path
4. Potential salary ranges
5. Growth opportunities

Format the response as JSON with the following structure:
{
  "recommendations": [
    {
      "title": "Career Path Name",
      "match": 85,
      "reasoning": "why this fits",
      "requiredSkills": ["skill1", "skill2"],
      "salaryRange": "$X - $Y",
      "growth": "growth potential"
    }
  ]
}
    `.trim();
  }

  /**
   * Generate interview questions prompt
   */
  static generateInterviewQuestionsPrompt(input: InterviewQuestionInput): string {
    const { role, experienceLevel, questionType, count } = input;
    
    return `
Generate ${count} ${questionType} interview questions for a ${experienceLevel} ${role} position.

Requirements:
1. Questions should be appropriate for ${experienceLevel} level
2. Focus on ${questionType} type questions
3. Include expected answer guidelines
4. Provide difficulty rating (1-5)

Format the response as JSON with the following structure:
{
  "questions": [
    {
      "question": "Question text",
      "type": "${questionType}",
      "difficulty": 3,
      "expectedAnswer": "Guidelines for good answer",
      "followUp": "Optional follow-up question"
    }
  ]
}
    `.trim();
  }

  /**
   * Generate skills assessment prompt
   */
  static generateSkillsAssessmentPrompt(skills: string[], context?: string): string {
    return `
Assess the following skills and provide detailed feedback:

Skills: ${skills.join(', ')}
${context ? `Context: ${context}` : ''}

For each skill, provide:
1. Proficiency level assessment
2. Market demand
3. Learning resources
4. Career applications

Format as JSON:
{
  "assessments": [
    {
      "skill": "skill name",
      "proficiency": "beginner|intermediate|advanced",
      "marketDemand": "high|medium|low",
      "resources": ["resource1", "resource2"],
      "applications": ["use case1", "use case2"]
    }
  ]
}
    `.trim();
  }

  /**
   * Generate learning path prompt
   */
  static generateLearningPathPrompt(targetSkill: string, currentLevel: string, timeframe: string): string {
    return `
Create a learning path for ${targetSkill} from ${currentLevel} level within ${timeframe}.

Include:
1. Learning milestones
2. Recommended resources
3. Practice projects
4. Assessment methods
5. Time allocation

Format as JSON:
{
  "path": {
    "skill": "${targetSkill}",
    "duration": "${timeframe}",
    "milestones": [
      {
        "week": 1,
        "goal": "milestone description",
        "resources": ["resource1"],
        "project": "practice project",
        "assessment": "how to measure progress"
      }
    ]
  }
}
    `.trim();
  }
}
