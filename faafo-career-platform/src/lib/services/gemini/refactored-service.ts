/**
 * Refactored Gemini Service
 * Main service that orchestrates all Gemini modules
 */

import { GeminiCoreClient, type GeminiConfig, type GeminiRequest, type GeminiResponse } from './core-client';
import { GeminiPromptTemplates, type SkillsAnalysisInput, type ResumeAnalysisInput, type CareerRecommendationInput, type InterviewQuestionInput } from './prompt-templates';
import { GeminiResponseParser, type SkillsAnalysisResponse, type ResumeAnalysisResponse, type CareerRecommendation, type InterviewQuestion } from './response-parser';
import { GeminiCacheManager, type CacheOptions } from './cache-manager';
import { GeminiRateLimiter, type RateLimitConfig } from './rate-limiter';

export interface RefactoredGeminiServiceConfig {
  apiKey: string;
  model?: string;
  cacheEnabled?: boolean;
  rateLimitConfig?: RateLimitConfig;
  defaultCacheTTL?: number;
}

export class RefactoredGeminiService {
  private client: GeminiCoreClient;
  private cache: GeminiCacheManager;
  private rateLimiter: GeminiRateLimiter;
  private cacheEnabled: boolean;

  constructor(config: RefactoredGeminiServiceConfig) {
    // Initialize core client
    const clientConfig: GeminiConfig = {
      apiKey: config.apiKey,
      model: config.model || 'gemini-1.5-flash',
      maxRetries: 3,
      timeout: 30000,
    };
    this.client = new GeminiCoreClient(clientConfig);

    // Initialize cache
    this.cacheEnabled = config.cacheEnabled ?? true;
    this.cache = new GeminiCacheManager({
      defaultTTL: config.defaultCacheTTL || 3600,
      maxEntries: 1000,
    });

    // Initialize rate limiter
    const rateLimitConfig = config.rateLimitConfig || {
      requestsPerMinute: 10,
      requestsPerHour: 100,
      requestsPerDay: 1000,
    };
    this.rateLimiter = new GeminiRateLimiter(rateLimitConfig);
  }

  /**
   * Analyze skills with caching and rate limiting
   */
  async analyzeSkills(input: SkillsAnalysisInput, userId: string): Promise<GeminiResponse> {
    // Check rate limit
    const rateLimitResult = this.rateLimiter.checkLimit(userId);
    if (!rateLimitResult.allowed) {
      return {
        success: false,
        error: `Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`,
      };
    }

    // Generate cache key
    const prompt = GeminiPromptTemplates.generateSkillsAnalysisPrompt(input);
    const cacheKey = this.cache.generateKey(prompt, { userId, type: 'skills-analysis' });

    // Check cache
    if (this.cacheEnabled) {
      const cached = this.cache.get<SkillsAnalysisResponse>(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          metadata: { cached: true },
        };
      }
    }

    // Make API request
    const request: GeminiRequest = {
      prompt,
      userId,
      config: {
        maxOutputTokens: 2048,
        temperature: 0.7,
      },
    };

    const response = await this.client.generateContent(request);
    
    if (!response.success) {
      return response;
    }

    // Parse response
    const parsed = GeminiResponseParser.parseSkillsAnalysis(response.data);
    
    if (!parsed.success) {
      return {
        success: false,
        error: parsed.error,
      };
    }

    // Cache successful response
    if (this.cacheEnabled) {
      this.cache.set(cacheKey, parsed.data, {
        ttl: 3600,
        userId,
        tags: ['skills-analysis'],
      });
    }

    return {
      success: true,
      data: parsed.data,
      metadata: {
        ...response.metadata,
        cached: false,
      },
    };
  }

  /**
   * Analyze resume with caching and rate limiting
   */
  async analyzeResume(input: ResumeAnalysisInput, userId: string): Promise<GeminiResponse> {
    // Check rate limit
    const rateLimitResult = this.rateLimiter.checkLimit(userId);
    if (!rateLimitResult.allowed) {
      return {
        success: false,
        error: `Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`,
      };
    }

    // Generate cache key (hash the resume text for privacy)
    const prompt = GeminiPromptTemplates.generateResumeAnalysisPrompt(input);
    const cacheKey = this.cache.generateKey(prompt, { userId, type: 'resume-analysis' });

    // Check cache
    if (this.cacheEnabled) {
      const cached = this.cache.get<ResumeAnalysisResponse>(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          metadata: { cached: true },
        };
      }
    }

    // Make API request
    const request: GeminiRequest = {
      prompt,
      userId,
      config: {
        maxOutputTokens: 3000,
        temperature: 0.5,
      },
    };

    const response = await this.client.generateContent(request);
    
    if (!response.success) {
      return response;
    }

    // Parse response
    const parsed = GeminiResponseParser.parseResumeAnalysis(response.data);
    
    if (!parsed.success) {
      return {
        success: false,
        error: parsed.error,
      };
    }

    // Cache successful response
    if (this.cacheEnabled) {
      this.cache.set(cacheKey, parsed.data, {
        ttl: 24 * 3600, // Cache for 24 hours
        userId,
        tags: ['resume-analysis'],
      });
    }

    return {
      success: true,
      data: parsed.data,
      metadata: {
        ...response.metadata,
        cached: false,
      },
    };
  }

  /**
   * Generate career recommendations
   */
  async generateCareerRecommendations(input: CareerRecommendationInput, userId: string): Promise<GeminiResponse> {
    // Check rate limit
    const rateLimitResult = this.rateLimiter.checkLimit(userId);
    if (!rateLimitResult.allowed) {
      return {
        success: false,
        error: `Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`,
      };
    }

    const prompt = GeminiPromptTemplates.generateCareerRecommendationsPrompt(input);
    const cacheKey = this.cache.generateKey(prompt, { userId, type: 'career-recommendations' });

    // Check cache
    if (this.cacheEnabled) {
      const cached = this.cache.get<CareerRecommendation[]>(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          metadata: { cached: true },
        };
      }
    }

    const request: GeminiRequest = {
      prompt,
      userId,
      config: {
        maxOutputTokens: 4000,
        temperature: 0.8,
      },
    };

    const response = await this.client.generateContent(request);
    
    if (!response.success) {
      return response;
    }

    const parsed = GeminiResponseParser.parseCareerRecommendations(response.data);
    
    if (!parsed.success) {
      return {
        success: false,
        error: parsed.error,
      };
    }

    // Cache successful response
    if (this.cacheEnabled) {
      this.cache.set(cacheKey, parsed.data, {
        ttl: 12 * 3600, // Cache for 12 hours
        userId,
        tags: ['career-recommendations'],
      });
    }

    return {
      success: true,
      data: parsed.data,
      metadata: {
        ...response.metadata,
        cached: false,
      },
    };
  }

  /**
   * Generate interview questions
   */
  async generateInterviewQuestions(input: InterviewQuestionInput, userId: string): Promise<GeminiResponse> {
    // Check rate limit
    const rateLimitResult = this.rateLimiter.checkLimit(userId);
    if (!rateLimitResult.allowed) {
      return {
        success: false,
        error: `Rate limit exceeded. Try again in ${rateLimitResult.retryAfter} seconds.`,
      };
    }

    const prompt = GeminiPromptTemplates.generateInterviewQuestionsPrompt(input);
    const cacheKey = this.cache.generateKey(prompt, { userId, type: 'interview-questions' });

    // Check cache
    if (this.cacheEnabled) {
      const cached = this.cache.get<InterviewQuestion[]>(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          metadata: { cached: true },
        };
      }
    }

    const request: GeminiRequest = {
      prompt,
      userId,
      config: {
        maxOutputTokens: 3000,
        temperature: 0.9,
      },
    };

    const response = await this.client.generateContent(request);
    
    if (!response.success) {
      return response;
    }

    const parsed = GeminiResponseParser.parseInterviewQuestions(response.data);
    
    if (!parsed.success) {
      return {
        success: false,
        error: parsed.error,
      };
    }

    // Cache successful response
    if (this.cacheEnabled) {
      this.cache.set(cacheKey, parsed.data, {
        ttl: 6 * 3600, // Cache for 6 hours
        userId,
        tags: ['interview-questions'],
      });
    }

    return {
      success: true,
      data: parsed.data,
      metadata: {
        ...response.metadata,
        cached: false,
      },
    };
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ healthy: boolean; details: any }> {
    const clientHealthy = await this.client.healthCheck();
    const cacheStats = this.cache.getStats();
    const rateLimiterStats = this.rateLimiter.getStats();

    return {
      healthy: clientHealthy,
      details: {
        client: clientHealthy,
        cache: cacheStats,
        rateLimiter: rateLimiterStats,
      },
    };
  }

  /**
   * Clear user cache
   */
  clearUserCache(userId: string): number {
    return this.cache.clearByUserId(userId);
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      cache: this.cache.getStats(),
      rateLimiter: this.rateLimiter.getStats(),
      model: this.client.getModelInfo(),
    };
  }

  /**
   * Destroy service and cleanup
   */
  destroy(): void {
    this.cache.destroy();
    this.rateLimiter.destroy();
  }
}

// Export singleton instance
export const refactoredGeminiService = new RefactoredGeminiService({
  apiKey: process.env.GOOGLE_GEMINI_API_KEY || '',
  cacheEnabled: process.env.NODE_ENV === 'production',
  rateLimitConfig: {
    requestsPerMinute: 10,
    requestsPerHour: 100,
    requestsPerDay: 1000,
  },
});
