/**
 * Gemini Response Parser Module
 * Response parsing and validation logic
 */

export interface ParsedResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  rawResponse?: string;
}

export interface SkillsAnalysisResponse {
  strengths: string[];
  improvements: string[];
  marketDemand: 'high' | 'medium' | 'low';
  learningPath: string[];
  skillGaps: string[];
  insights: string;
}

export interface ResumeAnalysisResponse {
  overallScore: number;
  strengths: string[];
  improvements: string[];
  missingSkills: string[];
  formatting: string;
  atsOptimization: string[];
  summary: string;
}

export interface CareerRecommendation {
  title: string;
  match: number;
  reasoning: string;
  requiredSkills: string[];
  salaryRange: string;
  growth: string;
}

export interface InterviewQuestion {
  question: string;
  type: string;
  difficulty: number;
  expectedAnswer: string;
  followUp?: string;
}

export class GeminiResponseParser {
  /**
   * Parse JSON response with fallback handling
   */
  static parseJSON<T>(response: string): ParsedResponse<T> {
    try {
      // Clean the response - remove markdown code blocks if present
      let cleanResponse = response.trim();
      
      // Remove markdown code blocks
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const parsed = JSON.parse(cleanResponse);
      
      return {
        success: true,
        data: parsed,
        rawResponse: response,
      };
    } catch (error) {
      return {
        success: false,
        error: `Failed to parse JSON: ${error instanceof Error ? error.message : 'Unknown error'}`,
        rawResponse: response,
      };
    }
  }

  /**
   * Parse skills analysis response
   */
  static parseSkillsAnalysis(response: string): ParsedResponse<SkillsAnalysisResponse> {
    const parsed = this.parseJSON<SkillsAnalysisResponse>(response);
    
    if (!parsed.success) {
      return parsed;
    }

    // Validate required fields
    const data = parsed.data!;
    const requiredFields = ['strengths', 'improvements', 'marketDemand', 'learningPath', 'skillGaps', 'insights'];
    const missingFields = requiredFields.filter(field => !(field in data));

    if (missingFields.length > 0) {
      return {
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`,
        rawResponse: response,
      };
    }

    // Ensure arrays are arrays
    if (!Array.isArray(data.strengths)) data.strengths = [];
    if (!Array.isArray(data.improvements)) data.improvements = [];
    if (!Array.isArray(data.learningPath)) data.learningPath = [];
    if (!Array.isArray(data.skillGaps)) data.skillGaps = [];

    return parsed;
  }

  /**
   * Parse resume analysis response
   */
  static parseResumeAnalysis(response: string): ParsedResponse<ResumeAnalysisResponse> {
    const parsed = this.parseJSON<ResumeAnalysisResponse>(response);
    
    if (!parsed.success) {
      return parsed;
    }

    const data = parsed.data!;
    
    // Validate and normalize score
    if (typeof data.overallScore !== 'number' || data.overallScore < 1 || data.overallScore > 10) {
      data.overallScore = 5; // Default score
    }

    // Ensure arrays are arrays
    if (!Array.isArray(data.strengths)) data.strengths = [];
    if (!Array.isArray(data.improvements)) data.improvements = [];
    if (!Array.isArray(data.missingSkills)) data.missingSkills = [];
    if (!Array.isArray(data.atsOptimization)) data.atsOptimization = [];

    // Ensure strings are strings
    if (typeof data.formatting !== 'string') data.formatting = '';
    if (typeof data.summary !== 'string') data.summary = '';

    return parsed;
  }

  /**
   * Parse career recommendations response
   */
  static parseCareerRecommendations(response: string): ParsedResponse<CareerRecommendation[]> {
    const parsed = this.parseJSON<{ recommendations: CareerRecommendation[] }>(response);
    
    if (!parsed.success) {
      return {
        success: false,
        error: parsed.error,
        data: []
      };
    }

    const recommendations = parsed.data?.recommendations || [];
    
    // Validate and normalize each recommendation
    const validRecommendations = recommendations.map(rec => ({
      title: rec.title || 'Unknown Career Path',
      match: typeof rec.match === 'number' ? Math.max(0, Math.min(100, rec.match)) : 50,
      reasoning: rec.reasoning || 'No reasoning provided',
      requiredSkills: Array.isArray(rec.requiredSkills) ? rec.requiredSkills : [],
      salaryRange: rec.salaryRange || 'Not specified',
      growth: rec.growth || 'Not specified',
    }));

    return {
      success: true,
      data: validRecommendations,
      rawResponse: response,
    };
  }

  /**
   * Parse interview questions response
   */
  static parseInterviewQuestions(response: string): ParsedResponse<InterviewQuestion[]> {
    const parsed = this.parseJSON<{ questions: InterviewQuestion[] }>(response);
    
    if (!parsed.success) {
      return {
        success: false,
        error: parsed.error,
        data: []
      };
    }

    const questions = parsed.data?.questions || [];
    
    // Validate and normalize each question
    const validQuestions = questions.map(q => ({
      question: q.question || 'No question provided',
      type: q.type || 'general',
      difficulty: typeof q.difficulty === 'number' ? Math.max(1, Math.min(5, q.difficulty)) : 3,
      expectedAnswer: q.expectedAnswer || 'No guidance provided',
      followUp: q.followUp,
    }));

    return {
      success: true,
      data: validQuestions,
      rawResponse: response,
    };
  }

  /**
   * Extract text content from various response formats
   */
  static extractTextContent(response: string): string {
    // Remove markdown formatting
    let content = response
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/#{1,6}\s/g, '') // Remove headers
      .replace(/^\s*[-*+]\s/gm, '') // Remove list markers
      .trim();

    return content;
  }

  /**
   * Validate response structure
   */
  static validateResponseStructure(data: any, requiredFields: string[]): { valid: boolean; missingFields: string[] } {
    const missingFields = requiredFields.filter(field => !(field in data));
    
    return {
      valid: missingFields.length === 0,
      missingFields,
    };
  }
}
