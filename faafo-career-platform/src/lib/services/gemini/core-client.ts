/**
 * Gemini Core Client Module
 * Basic Gemini API client functionality
 */

import { GoogleGenerativeAI } from '@google/generative-ai';

export interface GeminiConfig {
  apiKey: string;
  model: string;
  maxRetries: number;
  timeout: number;
}

export interface GenerationConfig {
  maxOutputTokens?: number;
  temperature?: number;
  topP?: number;
  topK?: number;
}

export interface GeminiRequest {
  prompt: string;
  config?: GenerationConfig;
  userId?: string;
  context?: Record<string, any>;
}

export interface GeminiResponse {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    tokensUsed?: number;
    responseTime?: number;
    cached?: boolean;
  };
}

export class GeminiCoreClient {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GeminiConfig;

  constructor(config: GeminiConfig) {
    this.config = config;
    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({ model: config.model });
  }

  /**
   * Generate content using Gemini API
   */
  async generateContent(request: GeminiRequest): Promise<GeminiResponse> {
    const startTime = Date.now();

    try {
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: request.prompt }] }],
        generationConfig: request.config || {
          maxOutputTokens: 2048,
          temperature: 0.7,
        },
      });

      const response = await result.response;
      const text = response.text();
      const responseTime = Date.now() - startTime;

      return {
        success: true,
        data: text,
        metadata: {
          responseTime,
          cached: false,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          responseTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Health check for the Gemini service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.generateContent({
        prompt: 'Hello, please respond with "OK"',
        config: { maxOutputTokens: 10 },
      });

      return result.success && result.data?.toLowerCase().includes('ok');
    } catch {
      return false;
    }
  }

  /**
   * Get model information
   */
  getModelInfo() {
    return {
      model: this.config.model,
      maxRetries: this.config.maxRetries,
      timeout: this.config.timeout,
    };
  }
}
