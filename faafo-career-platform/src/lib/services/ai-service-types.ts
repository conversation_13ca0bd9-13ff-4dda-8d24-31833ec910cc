/**
 * Type definitions for AI services
 * Extracted from geminiService.ts for better organization
 */

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}

export interface ResumeAnalysisResult {
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  skillsIdentified: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  industryFit: string[];
  overallScore: number;
}

export interface CareerRecommendation {
  careerPath: string;
  matchScore: number;
  reasoning: string;
  requiredSkills: string[];
  timeToTransition: string;
  salaryRange: string;
  growthPotential: string;
}

export interface SkillsAnalysisResult {
  currentSkills: string[];
  skillGaps: string[];
  learningRecommendations: {
    skill: string;
    priority: 'high' | 'medium' | 'low';
    estimatedTime: string;
    resources: string[];
  }[];
  careerReadiness: number;
}

export interface InterviewPrepResult {
  commonQuestions: {
    question: string;
    category: 'behavioral' | 'technical' | 'situational';
    sampleAnswer: string;
    tips: string[];
  }[];
  companySpecific: {
    question: string;
    keyPoints: string[];
  }[];
  preparationTips: string[];
}

export interface InterviewQuestionsResult {
  questions: {
    questionText: string;
    questionType: string;
    category: string;
    difficulty: string;
    expectedAnswerLength: string;
    keyPoints: string[];
    evaluationCriteria: string[];
    followUpQuestions: string[];
  }[];
  metadata: {
    totalQuestions: number;
    estimatedDuration: number;
    difficultyDistribution: any;
    categoryDistribution: any;
  };
}

export interface InterviewResponseAnalysis {
  overallScore: number;
  analysis: any;
  feedback: any;
  strengths: any;
  improvements: any;
  behavioralScore?: number;
  communicationScore?: number;
  technicalScore?: number;
}

export interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: number;
  successCount: number;
}

export interface RateLimitEntry {
  count: number;
  resetTime: number;
}

export interface AIServiceConfig {
  model: string;
  cacheEnabled: boolean;
  cacheTTL: number;
  rateLimitPerMinute: number;
  performanceOptimizationsEnabled: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  sanitizedInput?: string;
  errors?: string[];
  warnings?: string[];
}

export interface AIServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  rateLimitHits: number;
}

export interface AIPromptTemplate {
  name: string;
  template: string;
  variables: string[];
  category: string;
  description: string;
}

export interface AIServiceError {
  code: string;
  message: string;
  type: 'validation' | 'rate_limit' | 'api_error' | 'network_error' | 'timeout' | 'circuit_breaker';
  retryable: boolean;
  context?: any;
}

export type AITaskType = 
  | 'resume_analysis'
  | 'career_recommendations' 
  | 'skills_analysis'
  | 'interview_prep'
  | 'interview_questions'
  | 'interview_response_analysis';

export type AIServiceLogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface AIServiceLogEntry {
  timestamp: string;
  level: AIServiceLogLevel;
  message: string;
  context?: any;
  taskType?: AITaskType;
  duration?: number;
  error?: AIServiceError;
}
