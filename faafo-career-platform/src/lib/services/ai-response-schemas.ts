/**
 * Response validation schemas for AI services
 * Extracted from geminiService.ts for better organization
 */

export const RESPONSE_SCHEMAS = {
  resume_analysis: {
    type: 'object' as const,
    required: ['strengths', 'weaknesses', 'suggestions', 'overallScore'],
    properties: {
      strengths: { type: 'array' as const, items: { type: 'string' as const } },
      weaknesses: { type: 'array' as const, items: { type: 'string' as const } },
      suggestions: { type: 'array' as const, items: { type: 'string' as const } },
      skillsIdentified: { type: 'array' as const, items: { type: 'string' as const } },
      experienceLevel: { 
        type: 'string' as const, 
        enum: ['entry', 'mid', 'senior', 'executive'] 
      },
      industryFit: { type: 'array' as const, items: { type: 'string' as const } },
      overallScore: { type: 'number' as const, min: 1, max: 10 }
    }
  },

  career_recommendations: {
    type: 'array' as const,
    items: {
      type: 'object' as const,
      required: ['careerPath', 'matchScore', 'reasoning'],
      properties: {
        careerPath: { type: 'string' as const },
        matchScore: { type: 'number' as const, min: 0, max: 100 },
        reasoning: { type: 'string' as const },
        requiredSkills: { type: 'array' as const, items: { type: 'string' as const } },
        timeToTransition: { type: 'string' as const },
        salaryRange: { type: 'string' as const },
        growthPotential: { type: 'string' as const }
      }
    }
  },

  skills_analysis: {
    type: 'object' as const,
    required: ['currentSkills', 'skillGaps', 'careerReadiness'],
    properties: {
      currentSkills: { type: 'array' as const, items: { type: 'string' as const } },
      skillGaps: { type: 'array' as const, items: { type: 'string' as const } },
      learningRecommendations: {
        type: 'array' as const,
        items: {
          type: 'object' as const,
          required: ['skill', 'priority'],
          properties: {
            skill: { type: 'string' as const },
            priority: { type: 'string' as const, enum: ['high', 'medium', 'low'] },
            estimatedTime: { type: 'string' as const },
            resources: { type: 'array' as const, items: { type: 'string' as const } }
          }
        }
      },
      careerReadiness: { type: 'number' as const, min: 0, max: 100 }
    }
  },

  interview_prep: {
    type: 'object' as const,
    required: ['commonQuestions', 'preparationTips'],
    properties: {
      commonQuestions: {
        type: 'array' as const,
        items: {
          type: 'object' as const,
          required: ['question', 'category'],
          properties: {
            question: { type: 'string' as const },
            category: { 
              type: 'string' as const, 
              enum: ['behavioral', 'technical', 'situational'] 
            },
            sampleAnswer: { type: 'string' as const },
            tips: { type: 'array' as const, items: { type: 'string' as const } }
          }
        }
      },
      companySpecific: {
        type: 'array' as const,
        items: {
          type: 'object' as const,
          required: ['question'],
          properties: {
            question: { type: 'string' as const },
            keyPoints: { type: 'array' as const, items: { type: 'string' as const } }
          }
        }
      },
      preparationTips: { type: 'array' as const, items: { type: 'string' as const } }
    }
  },

  interview_questions: {
    type: 'object' as const,
    required: ['questions', 'metadata'],
    properties: {
      questions: {
        type: 'array' as const,
        items: {
          type: 'object' as const,
          required: ['questionText', 'questionType', 'category', 'difficulty'],
          properties: {
            questionText: { type: 'string' as const },
            questionType: { type: 'string' as const },
            category: { type: 'string' as const },
            difficulty: { type: 'string' as const },
            expectedAnswerLength: { type: 'string' as const },
            keyPoints: { type: 'array' as const, items: { type: 'string' as const } },
            evaluationCriteria: { type: 'array' as const, items: { type: 'string' as const } },
            followUpQuestions: { type: 'array' as const, items: { type: 'string' as const } }
          }
        }
      },
      metadata: {
        type: 'object' as const,
        required: ['totalQuestions', 'estimatedDuration'],
        properties: {
          totalQuestions: { type: 'number' as const },
          estimatedDuration: { type: 'number' as const },
          difficultyDistribution: { type: 'object' as const },
          categoryDistribution: { type: 'object' as const }
        }
      }
    }
  },

  interview_response_analysis: {
    type: 'object' as const,
    required: ['overallScore', 'analysis', 'feedback'],
    properties: {
      overallScore: { type: 'number' as const, min: 1, max: 10 },
      analysis: { type: 'object' as const },
      feedback: { type: 'object' as const },
      strengths: { type: 'object' as const },
      improvements: { type: 'object' as const },
      behavioralScore: { type: 'number' as const, min: 1, max: 10 },
      communicationScore: { type: 'number' as const, min: 1, max: 10 },
      technicalScore: { type: 'number' as const, min: 1, max: 10 }
    }
  }
};

export function validateAIResponse(taskType: string, response: any): {
  isValid: boolean;
  errors: string[];
} {
  const schema = RESPONSE_SCHEMAS[taskType as keyof typeof RESPONSE_SCHEMAS];
  if (!schema) {
    return {
      isValid: false,
      errors: [`Unknown task type: ${taskType}`]
    };
  }

  const errors: string[] = [];

  // Basic validation implementation
  // In a production environment, you might want to use a library like Ajv
  if (schema.type === 'object') {
    if (typeof response !== 'object' || response === null) {
      errors.push('Response must be an object');
      return { isValid: false, errors };
    }

    // Check required properties
    if (schema.required) {
      for (const prop of schema.required) {
        if (!(prop in response)) {
          errors.push(`Missing required property: ${prop}`);
        }
      }
    }
  } else if (schema.type === 'array') {
    if (!Array.isArray(response)) {
      errors.push('Response must be an array');
      return { isValid: false, errors };
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
