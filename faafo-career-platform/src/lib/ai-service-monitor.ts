/**
 * AI Service Health Monitoring and Analytics
 * Provides real-time monitoring, performance metrics, and usage analytics
 */

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: number;
  responseTime: number;
  error?: string;
  uptime?: number;
  lastRequestTime?: number;
  issues?: string[];
  recommendations?: string[];
}

interface ServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  rateLimitHits: number;
  lastHealthCheck: number;
  uptime: number;
  requestsByType: Record<string, number>;
  requestsByUser: Record<string, number>;
  errorsByType: Record<string, number>;
}

interface PerformanceMetric {
  timestamp: number;
  operation: string;
  responseTime: number;
  success: boolean;
  cacheHit: boolean;
  userId?: string;
  error?: string;
}

interface UsageAnalytics {
  dailyRequests: Record<string, number>;
  operationBreakdown: Record<string, number>;
  userActivity: Record<string, number>;
  errorPatterns: Record<string, number>;
  peakUsageHours: number[];
  successRate: number;
  popularOperations: Array<{ operation: string; count: number }>;
  activeUsers: Array<{ userId: string; requestCount: number }>;
  timeAnalysis: { averageResponseTime: number };
  errorAnalysis: { totalErrors: number };
}

export class AIServiceMonitor {
  private static instance: AIServiceMonitor;
  private metrics: ServiceMetrics;
  private performanceHistory: PerformanceMetric[] = [];
  private healthHistory: HealthStatus[] = [];
  private startTime: number;
  private readonly MAX_HISTORY_SIZE = 1000;
  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
  private healthCheckTimer?: NodeJS.Timeout;

  private constructor() {
    this.startTime = Date.now();
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      rateLimitHits: 0,
      lastHealthCheck: 0,
      uptime: 0,
      requestsByType: {},
      requestsByUser: {},
      errorsByType: {}
    };

    this.startHealthChecking();
  }

  static getInstance(): AIServiceMonitor {
    if (!AIServiceMonitor.instance) {
      AIServiceMonitor.instance = new AIServiceMonitor();
    }
    return AIServiceMonitor.instance;
  }

  /**
   * Record a service operation
   */
  recordOperation(
    operation: string,
    responseTime: number,
    success: boolean,
    cacheHit: boolean = false,
    userId?: string,
    error?: string
  ): void {
    const metric: PerformanceMetric = {
      timestamp: Date.now(),
      operation,
      responseTime,
      success,
      cacheHit,
      userId,
      error
    };

    this.performanceHistory.push(metric);
    this.trimHistory();
    this.updateMetrics(metric);
  }

  /**
   * Record rate limit hit
   */
  recordRateLimitHit(userId: string): void {
    this.metrics.rateLimitHits++;
    console.warn(`[AI-Monitor] Rate limit hit for user: ${userId}`);
  }

  /**
   * Record a request (for compatibility with tests)
   */
  recordRequest(operationType: string, userId: string, success: boolean, responseTime: number): void {
    this.recordOperation(operationType, responseTime, success, false, userId);
  }

  /**
   * Record an error (for compatibility with tests)
   */
  recordError(errorType: string, errorMessage: string, context: any): void {
    this.recordOperation(errorType, 0, false, false, context.userId, errorMessage);
  }

  /**
   * Get current service health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    try {
      const startTime = Date.now();
      
      // Perform health checks on all components
      const aiHealthy = await this.checkAIServiceHealth();
      const cacheHealthy = await this.checkCacheHealth();
      const dbHealthy = await this.checkDatabaseHealth();
      
      const responseTime = Date.now() - startTime;
      const allHealthy = aiHealthy && cacheHealthy && dbHealthy;
      
      const issues: string[] = [];
      const recommendations: string[] = [];

      if (!aiHealthy) {
        issues.push('AI service is not responding');
        recommendations.push('Check AI service configuration and API keys');
      }
      if (!cacheHealthy) {
        issues.push('Cache service is degraded');
        recommendations.push('Check cache service connectivity');
      }
      if (!dbHealthy) {
        issues.push('Database connectivity issues');
        recommendations.push('Check database connection and health');
      }

      // Determine status based on issues
      let healthStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (issues.length > 0) {
        healthStatus = issues.length > 2 ? 'unhealthy' : 'degraded';
      }

      // Check for performance issues
      if (this.metrics.averageResponseTime > 10000) {
        issues.push('High average response time');
        recommendations.push('Optimize service performance');
        healthStatus = 'unhealthy';
      }

      const lastRequest = this.performanceHistory.length > 0
        ? this.performanceHistory[this.performanceHistory.length - 1].timestamp
        : Date.now();

      const status: HealthStatus = {
        status: healthStatus,
        timestamp: Date.now(),
        responseTime,
        uptime: Date.now() - this.startTime,
        lastRequestTime: lastRequest,
        issues: issues.length > 0 ? issues : undefined,
        recommendations: recommendations.length > 0 ? recommendations : undefined
      };

      this.healthHistory.push(status);
      this.trimHealthHistory();
      this.metrics.lastHealthCheck = status.timestamp;

      return status;
    } catch (error) {
      const status: HealthStatus = {
        status: 'unhealthy',
        timestamp: Date.now(),
        responseTime: 0,
        error: error instanceof Error ? error.message : String(error)
      };

      this.healthHistory.push(status);
      this.trimHealthHistory();
      
      return status;
    }
  }

  /**
   * Get comprehensive service metrics
   */
  getMetrics(): ServiceMetrics & { 
    recentPerformance: PerformanceMetric[];
    healthTrend: HealthStatus[];
  } {
    this.metrics.uptime = Date.now() - this.startTime;
    
    return {
      ...this.metrics,
      recentPerformance: this.performanceHistory.slice(-50), // Last 50 operations
      healthTrend: this.healthHistory.slice(-20) // Last 20 health checks
    };
  }

  /**
   * Get usage analytics
   */
  getUsageAnalytics(): UsageAnalytics {
    const now = new Date();
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    });

    const dailyRequests: Record<string, number> = {};
    const operationBreakdown: Record<string, number> = {};
    const userActivity: Record<string, number> = {};
    const errorPatterns: Record<string, number> = {};
    const hourlyUsage = new Array(24).fill(0);

    // Initialize daily requests
    last7Days.forEach(date => {
      dailyRequests[date] = 0;
    });

    // Analyze performance history
    this.performanceHistory.forEach(metric => {
      const date = new Date(metric.timestamp).toISOString().split('T')[0];
      const hour = new Date(metric.timestamp).getHours();
      
      // Daily requests
      if (dailyRequests.hasOwnProperty(date)) {
        dailyRequests[date]++;
      }
      
      // Operation breakdown
      operationBreakdown[metric.operation] = (operationBreakdown[metric.operation] || 0) + 1;
      
      // User activity
      if (metric.userId) {
        userActivity[metric.userId] = (userActivity[metric.userId] || 0) + 1;
      }
      
      // Error patterns
      if (!metric.success && metric.error) {
        errorPatterns[metric.error] = (errorPatterns[metric.error] || 0) + 1;
      }
      
      // Hourly usage
      hourlyUsage[hour]++;
    });

    // Find peak usage hours
    const peakUsageHours = hourlyUsage
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour);

    // Calculate additional analytics
    const totalRequests = this.performanceHistory.length;
    const successfulRequests = this.performanceHistory.filter(m => m.success).length;
    const successRate = totalRequests > 0 ? successfulRequests / totalRequests : 0;

    const popularOperations = Object.entries(operationBreakdown)
      .map(([operation, count]) => ({ operation, count }))
      .sort((a, b) => b.count - a.count);

    const activeUsers = Object.entries(userActivity)
      .map(([userId, requestCount]) => ({ userId, requestCount }))
      .sort((a, b) => b.requestCount - a.requestCount);

    const totalResponseTime = this.performanceHistory.reduce((sum, m) => sum + m.responseTime, 0);
    const averageResponseTime = totalRequests > 0 ? totalResponseTime / totalRequests : 0;

    const totalErrors = this.performanceHistory.filter(m => !m.success).length;

    return {
      dailyRequests,
      operationBreakdown,
      userActivity,
      errorPatterns,
      peakUsageHours,
      successRate,
      popularOperations,
      activeUsers,
      timeAnalysis: { averageResponseTime },
      errorAnalysis: { totalErrors }
    };
  }

  /**
   * Get performance insights
   */
  getPerformanceInsights(): {
    slowestOperations: Array<{ operation: string; avgResponseTime: number }>;
    errorRateByOperation: Array<{ operation: string; errorRate: number }>;
    cacheEffectiveness: { hitRate: number; avgResponseTimeWithCache: number; avgResponseTimeWithoutCache: number };
    recommendations: string[];
    performanceTrend?: { direction: string };
    bottlenecks?: Array<{ operation: string; issue: string }>;
    performanceScore?: number;
  } {
    const operationStats = new Map<string, { 
      totalTime: number; 
      count: number; 
      errors: number; 
      cacheHits: number;
      cacheHitTime: number;
      cacheMissTime: number;
    }>();

    // Analyze performance data
    this.performanceHistory.forEach(metric => {
      if (!operationStats.has(metric.operation)) {
        operationStats.set(metric.operation, {
          totalTime: 0,
          count: 0,
          errors: 0,
          cacheHits: 0,
          cacheHitTime: 0,
          cacheMissTime: 0
        });
      }

      const stats = operationStats.get(metric.operation)!;
      stats.totalTime += metric.responseTime;
      stats.count++;
      
      if (!metric.success) {
        stats.errors++;
      }
      
      if (metric.cacheHit) {
        stats.cacheHits++;
        stats.cacheHitTime += metric.responseTime;
      } else {
        stats.cacheMissTime += metric.responseTime;
      }
    });

    // Calculate insights
    const slowestOperations = Array.from(operationStats.entries())
      .map(([operation, stats]) => ({
        operation,
        avgResponseTime: stats.totalTime / stats.count
      }))
      .sort((a, b) => b.avgResponseTime - a.avgResponseTime)
      .slice(0, 5);

    const errorRateByOperation = Array.from(operationStats.entries())
      .map(([operation, stats]) => ({
        operation,
        errorRate: (stats.errors / stats.count) * 100
      }))
      .filter(item => item.errorRate > 0)
      .sort((a, b) => b.errorRate - a.errorRate);

    // Cache effectiveness
    const totalCacheHits = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheHits, 0);
    const totalOperations = this.performanceHistory.length;
    const totalCacheHitTime = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheHitTime, 0);
    const totalCacheMissTime = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheMissTime, 0);

    const cacheEffectiveness = {
      hitRate: totalOperations > 0 ? (totalCacheHits / totalOperations) * 100 : 0,
      avgResponseTimeWithCache: totalCacheHits > 0 ? totalCacheHitTime / totalCacheHits : 0,
      avgResponseTimeWithoutCache: (totalOperations - totalCacheHits) > 0 ? totalCacheMissTime / (totalOperations - totalCacheHits) : 0
    };

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (this.metrics.averageResponseTime > 5000) {
      recommendations.push('Consider optimizing AI prompts to reduce response times');
    }
    
    if (cacheEffectiveness.hitRate < 30) {
      recommendations.push('Improve caching strategy to increase cache hit rate');
    }
    
    if (this.metrics.failedRequests / this.metrics.totalRequests > 0.05) {
      recommendations.push('Investigate and address high error rate');
    }
    
    if (this.metrics.rateLimitHits > 10) {
      recommendations.push('Consider implementing request queuing to reduce rate limit hits');
    }

    // Calculate performance trend
    const recentMetrics = this.performanceHistory.slice(-10);
    const olderMetrics = this.performanceHistory.slice(-20, -10);
    const recentAvg = recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / Math.max(recentMetrics.length, 1);
    const olderAvg = olderMetrics.reduce((sum, m) => sum + m.responseTime, 0) / Math.max(olderMetrics.length, 1);
    const performanceTrend = {
      direction: recentAvg > olderAvg ? 'degrading' : 'improving'
    };

    // Identify bottlenecks
    const bottlenecks = slowestOperations
      .filter(op => op.avgResponseTime > 3000)
      .map(op => ({ operation: op.operation, issue: 'High response time' }));

    // Calculate performance score (0-100)
    const errorRate = this.metrics.totalRequests > 0 ? this.metrics.failedRequests / this.metrics.totalRequests : 0;
    const performanceScore = Math.max(0, 100 - (errorRate * 100) - Math.min(50, this.metrics.averageResponseTime / 100));

    return {
      slowestOperations,
      errorRateByOperation,
      cacheEffectiveness,
      recommendations,
      performanceTrend,
      bottlenecks,
      performanceScore
    };
  }

  /**
   * Start automatic health checking
   */
  private startHealthChecking(): void {
    this.healthCheckTimer = setInterval(async () => {
      await this.getHealthStatus();
    }, this.HEALTH_CHECK_INTERVAL);
  }

  /**
   * Stop health checking
   */
  stopHealthChecking(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
  }

  /**
   * Generate a comprehensive report (for compatibility with tests)
   */
  generateReport(startTime?: Date, endTime?: Date): any {
    const metrics = this.getMetrics();
    const analytics = this.getUsageAnalytics();
    const insights = this.getPerformanceInsights();

    return {
      metrics,
      analytics,
      insights,
      reportGenerated: new Date().toISOString(),
      timeRange: {
        start: startTime?.toISOString() || new Date(this.startTime).toISOString(),
        end: endTime?.toISOString() || new Date().toISOString()
      }
    };
  }

  /**
   * Get alert configuration (for compatibility with tests)
   */
  getAlertConfig(): any {
    return {
      errorRateThreshold: 0.05,
      responseTimeThreshold: 5000,
      rateLimitThreshold: 10,
      cacheHitRateThreshold: 30
    };
  }

  /**
   * Update metrics based on new performance data
   */
  private updateMetrics(metric: PerformanceMetric): void {
    this.metrics.totalRequests++;

    if (metric.success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }

    // Update average response time
    const totalResponseTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + metric.responseTime;
    this.metrics.averageResponseTime = totalResponseTime / this.metrics.totalRequests;

    // Update cache hit rate
    const cacheHits = this.performanceHistory.filter(m => m.cacheHit).length;
    this.metrics.cacheHitRate = (cacheHits / this.metrics.totalRequests) * 100;

    // Update requests by type
    this.metrics.requestsByType[metric.operation] = (this.metrics.requestsByType[metric.operation] || 0) + 1;

    // Update requests by user
    if (metric.userId) {
      this.metrics.requestsByUser[metric.userId] = (this.metrics.requestsByUser[metric.userId] || 0) + 1;
    }

    // Update errors by type
    if (!metric.success && metric.error) {
      this.metrics.errorsByType[metric.error] = (this.metrics.errorsByType[metric.error] || 0) + 1;
    }
  }

  /**
   * Trim performance history to prevent memory issues
   */
  private trimHistory(): void {
    if (this.performanceHistory.length > this.MAX_HISTORY_SIZE) {
      this.performanceHistory = this.performanceHistory.slice(-this.MAX_HISTORY_SIZE);
    }
  }

  /**
   * Trim health history
   */
  private trimHealthHistory(): void {
    if (this.healthHistory.length > 100) {
      this.healthHistory = this.healthHistory.slice(-100);
    }
  }

  /**
   * Check AI service health
   */
  private async checkAIServiceHealth(): Promise<boolean> {
    try {
      // This would typically make a lightweight request to the AI service
      // For now, we'll simulate a health check
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check cache health
   */
  private async checkCacheHealth(): Promise<boolean> {
    try {
      // Check if cache is responsive
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      // Check if database is responsive
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Export metrics for external monitoring systems
   */
  exportMetrics(): string {
    const metrics = this.getMetrics();
    const analytics = this.getUsageAnalytics();
    const insights = this.getPerformanceInsights();

    return JSON.stringify({
      timestamp: Date.now(),
      service: 'ai-service',
      metrics,
      analytics,
      insights
    }, null, 2);
  }

  /**
   * Reset all metrics (useful for testing)
   */
  reset(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      rateLimitHits: 0,
      lastHealthCheck: 0,
      uptime: 0,
      requestsByType: {},
      requestsByUser: {},
      errorsByType: {}
    };
    this.performanceHistory = [];
    this.healthHistory = [];
    this.startTime = Date.now();
  }
}

// Export singleton instance
export const aiServiceMonitor = AIServiceMonitor.getInstance();
export default aiServiceMonitor;
