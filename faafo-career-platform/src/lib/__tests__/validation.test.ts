import {
  signupSchema,
  loginSchema,
  validateRequestBody,
} from '@/lib/validation';

describe('Validation Schemas', () => {
  describe('signupSchema', () => {
    it('should validate correct signup data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const result = validateRequestBody(signupSchema, validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should reject invalid email', () => {
      const invalidData = {
        email: 'invalid-email',
        password: 'Password123!',
      };

      const result = validateRequestBody(signupSchema, invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toContain('Invalid email address');
      }
    });

    it('should reject weak password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: 'weak',
      };

      const result = validateRequestBody(signupSchema, invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toContain('Password must be at least 8 characters');
      }
    });
  });

  describe('loginSchema', () => {
    it('should validate correct login data', () => {
      const validData = {
        email: '<EMAIL>',
        password: 'anypassword',
      };

      const result = validateRequestBody(loginSchema, validData);
      expect(result.success).toBe(true);
    });

    it('should reject missing password', () => {
      const invalidData = {
        email: '<EMAIL>',
        password: '',
      };

      const result = validateRequestBody(loginSchema, invalidData);
      expect(result.success).toBe(false);
    });
  });
});
