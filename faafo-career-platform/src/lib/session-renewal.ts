/**
 * Session Renewal System
 * Handles session renewal without losing user work
 */

import { getSession } from 'next-auth/react';

export interface SessionRenewalConfig {
  renewalEndpoint: string;
  maxRenewalAttempts: number;
  renewalTimeout: number;
  autoSaveBeforeRenewal: boolean;
  preserveFormData: boolean;
}

export interface SessionRenewalResult {
  success: boolean;
  newExpiry?: number;
  error?: string;
  preservedData?: any;
}

export interface FormDataSnapshot {
  url: string;
  timestamp: number;
  formData: Record<string, any>;
  scrollPosition: number;
  activeElement?: string;
}

/**
 * Default session renewal configuration
 */
export const DEFAULT_RENEWAL_CONFIG: SessionRenewalConfig = {
  renewalEndpoint: '/api/auth/renew-session',
  maxRenewalAttempts: 3,
  renewalTimeout: 10000, // 10 seconds
  autoSaveBeforeRenewal: true,
  preserveFormData: true
};

/**
 * Session Renewal Manager
 */
export class SessionRenewalManager {
  private config: SessionRenewalConfig;
  private formDataSnapshots: Map<string, FormDataSnapshot> = new Map();
  private renewalInProgress = false;

  constructor(config: Partial<SessionRenewalConfig> = {}) {
    this.config = { ...DEFAULT_RENEWAL_CONFIG, ...config };
  }

  /**
   * Renew the current session
   */
  async renewSession(): Promise<SessionRenewalResult> {
    if (this.renewalInProgress) {
      return { success: false, error: 'Renewal already in progress' };
    }

    this.renewalInProgress = true;

    try {
      // Preserve current state before renewal
      const preservedData = this.config.preserveFormData ? this.captureCurrentState() : null;

      // Attempt to renew session
      const result = await this.performRenewal();

      if (result.success && preservedData) {
        // Restore state after successful renewal
        await this.restoreState(preservedData);
      }

      return {
        ...result,
        preservedData
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    } finally {
      this.renewalInProgress = false;
    }
  }

  /**
   * Perform the actual session renewal
   */
  private async performRenewal(): Promise<SessionRenewalResult> {
    let lastError: any;

    for (let attempt = 1; attempt <= this.config.maxRenewalAttempts; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.renewalTimeout);

        const response = await fetch(this.config.renewalEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const data = await response.json();
          return {
            success: true,
            newExpiry: data.expiresAt
          };
        } else {
          const errorData = await response.json().catch(() => ({}));
          lastError = new Error(errorData.error || `HTTP ${response.status}`);
        }
      } catch (error) {
        lastError = error;
        
        // Wait before retrying (except on last attempt)
        if (attempt < this.config.maxRenewalAttempts) {
          await this.sleep(1000 * attempt); // Exponential backoff
        }
      }
    }

    return {
      success: false,
      error: lastError?.message || 'Session renewal failed'
    };
  }

  /**
   * Capture current page state
   */
  private captureCurrentState(): FormDataSnapshot {
    const url = window.location.href;
    const timestamp = Date.now();
    const scrollPosition = window.scrollY;
    
    // Capture form data
    const formData: Record<string, any> = {};
    const forms = document.querySelectorAll('form');
    
    forms.forEach((form, formIndex) => {
      const formKey = `form_${formIndex}`;
      formData[formKey] = {};
      
      const formElements = form.querySelectorAll('input, textarea, select');
      formElements.forEach((element) => {
        const input = element as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
        const name = input.name || input.id || `element_${Array.from(formElements).indexOf(element)}`;
        
        if (input.type === 'checkbox' || input.type === 'radio') {
          formData[formKey][name] = (input as HTMLInputElement).checked;
        } else if (input.type === 'file') {
          // Don't capture file inputs for security reasons
          formData[formKey][name] = null;
        } else {
          formData[formKey][name] = input.value;
        }
      });
    });

    // Capture active element
    const activeElement = document.activeElement?.id || (document.activeElement as any)?.name;

    const snapshot: FormDataSnapshot = {
      url,
      timestamp,
      formData,
      scrollPosition,
      activeElement
    };

    // Store snapshot
    this.formDataSnapshots.set(url, snapshot);

    return snapshot;
  }

  /**
   * Restore page state after renewal
   */
  private async restoreState(snapshot: FormDataSnapshot): Promise<void> {
    try {
      // Restore form data
      Object.entries(snapshot.formData).forEach(([formKey, formData]) => {
        const formIndex = parseInt(formKey.replace('form_', ''));
        const form = document.querySelectorAll('form')[formIndex];
        
        if (form) {
          Object.entries(formData as Record<string, any>).forEach(([fieldName, value]) => {
            const element = form.querySelector(`[name="${fieldName}"], [id="${fieldName}"]`) as 
              HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
            
            if (element && value !== null) {
              if (element.type === 'checkbox' || element.type === 'radio') {
                (element as HTMLInputElement).checked = Boolean(value);
              } else if (element.type !== 'file') {
                element.value = String(value);
              }
            }
          });
        }
      });

      // Restore scroll position
      window.scrollTo(0, snapshot.scrollPosition);

      // Restore active element
      if (snapshot.activeElement) {
        const element = document.getElementById(snapshot.activeElement) || 
                      document.querySelector(`[name="${snapshot.activeElement}"]`);
        if (element && element instanceof HTMLElement) {
          element.focus();
        }
      }
    } catch (error) {
      console.error('Error restoring state after session renewal:', error);
    }
  }

  /**
   * Get stored form data snapshot
   */
  getFormDataSnapshot(url?: string): FormDataSnapshot | null {
    const targetUrl = url || window.location.href;
    return this.formDataSnapshots.get(targetUrl) || null;
  }

  /**
   * Clear stored form data snapshots
   */
  clearFormDataSnapshots(olderThan?: number): void {
    if (olderThan) {
      const cutoff = Date.now() - olderThan;
      Array.from(this.formDataSnapshots.entries()).forEach(([url, snapshot]) => {
        if (snapshot.timestamp < cutoff) {
          this.formDataSnapshots.delete(url);
        }
      });
    } else {
      this.formDataSnapshots.clear();
    }
  }

  /**
   * Check if session is still valid
   */
  async checkSessionValidity(): Promise<boolean> {
    try {
      const session = await getSession();
      return !!session;
    } catch (error) {
      console.error('Error checking session validity:', error);
      return false;
    }
  }

  /**
   * Auto-save current work before session renewal
   */
  async autoSaveWork(): Promise<boolean> {
    if (!this.config.autoSaveBeforeRenewal) {
      return true;
    }

    try {
      // Trigger any auto-save mechanisms in the current page
      const autoSaveEvent = new CustomEvent('autoSaveBeforeRenewal', {
        detail: { timestamp: Date.now() }
      });
      
      window.dispatchEvent(autoSaveEvent);

      // Wait a bit for auto-save to complete
      await this.sleep(1000);

      return true;
    } catch (error) {
      console.error('Error during auto-save:', error);
      return false;
    }
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Global session renewal manager instance
 */
let globalRenewalManager: SessionRenewalManager | null = null;

/**
 * Get or create global session renewal manager
 */
export function getSessionRenewalManager(config?: Partial<SessionRenewalConfig>): SessionRenewalManager {
  if (!globalRenewalManager) {
    globalRenewalManager = new SessionRenewalManager(config);
  }
  return globalRenewalManager;
}

/**
 * Convenience function to renew session
 */
export async function renewSession(config?: Partial<SessionRenewalConfig>): Promise<SessionRenewalResult> {
  const manager = getSessionRenewalManager(config);
  return manager.renewSession();
}

/**
 * React hook for session renewal
 */
export function useSessionRenewal(config?: Partial<SessionRenewalConfig>) {
  const manager = getSessionRenewalManager(config);

  const renewSession = async (): Promise<SessionRenewalResult> => {
    return manager.renewSession();
  };

  const captureFormData = (): FormDataSnapshot | null => {
    if (typeof window === 'undefined') return null;
    return manager.getFormDataSnapshot();
  };

  const checkValidity = async (): Promise<boolean> => {
    return manager.checkSessionValidity();
  };

  return {
    renewSession,
    captureFormData,
    checkValidity,
    manager
  };
}
