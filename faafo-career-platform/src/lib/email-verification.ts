import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import React from 'react';

/**
 * Check if the current user's email is verified
 * Returns the verification status and user info
 */
export async function checkEmailVerification() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return {
        isAuthenticated: false,
        isVerified: false,
        user: null,
      };
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        name: true,
      },
    });

    if (!user) {
      return {
        isAuthenticated: false,
        isVerified: false,
        user: null,
      };
    }

    return {
      isAuthenticated: true,
      isVerified: !!user.emailVerified,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        emailVerified: user.emailVerified,
      },
    };
  } catch (error) {
    console.error('Error checking email verification:', error);
    return {
      isAuthenticated: false,
      isVerified: false,
      user: null,
    };
  }
}

/**
 * Higher-order component to protect pages that require email verification
 */
export function withEmailVerification<T extends object>(
  WrappedComponent: React.ComponentType<T>
): React.ComponentType<T> {
  return function EmailVerificationWrapper(props: T) {
    // This would be used in pages that need verification
    // The actual verification check would happen server-side
    return React.createElement(WrappedComponent, props);
  };
}

/**
 * Routes that require email verification
 */
export const PROTECTED_ROUTES = [
  '/dashboard',
  '/profile',
  '/assessment',
  '/career-paths',
  '/resources',
  '/forum',
  '/progress',
  '/recommendations',
  '/freedom-fund',
];

/**
 * Check if a route requires email verification
 */
export function requiresEmailVerification(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route));
}
