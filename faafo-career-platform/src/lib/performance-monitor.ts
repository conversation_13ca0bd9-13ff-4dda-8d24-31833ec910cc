/**
 * Performance Monitor
 * Real-time performance tracking and optimization recommendations
 */

import { AIServiceLogger } from '@/lib/services/geminiService';
import { advancedCacheManager } from '@/lib/advanced-cache-manager';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { enhancedAlertingService } from '@/lib/enhanced-alerting-service';

interface PerformanceMetric {
  timestamp: number;
  value: number;
  operation: string;
  userId?: string;
  metadata?: any;
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'critical';
  message: string;
  timestamp: number;
  metric: string;
  threshold: number;
  currentValue: number;
  recommendations: string[];
}

interface PerformanceThresholds {
  responseTime: { warning: number; critical: number };
  throughput: { warning: number; critical: number };
  errorRate: { warning: number; critical: number };
  cacheHitRate: { warning: number; critical: number };
  memoryUsage: { warning: number; critical: number };
  queueLength: { warning: number; critical: number };
}

interface PerformanceInsights {
  overallScore: number;
  trends: {
    responseTime: 'improving' | 'stable' | 'degrading';
    throughput: 'improving' | 'stable' | 'degrading';
    errorRate: 'improving' | 'stable' | 'degrading';
  };
  bottlenecks: Array<{
    component: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
    impact: string;
  }>;
  recommendations: Array<{
    priority: 'low' | 'medium' | 'high';
    category: string;
    action: string;
    expectedImpact: string;
  }>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]>;
  private alerts: PerformanceAlert[];
  private thresholds: PerformanceThresholds;
  private isMonitoring: boolean;
  private alertCounter: number;

  constructor() {
    this.metrics = new Map();
    this.alerts = [];
    this.alertCounter = 0;
    this.isMonitoring = false;

    this.thresholds = {
      responseTime: { warning: 2000, critical: 5000 }, // ms
      throughput: { warning: 10, critical: 5 }, // requests/second
      errorRate: { warning: 0.05, critical: 0.1 }, // 5% warning, 10% critical
      cacheHitRate: { warning: 0.7, critical: 0.5 }, // 70% warning, 50% critical
      memoryUsage: { warning: 0.8, critical: 0.95 }, // 80% warning, 95% critical
      queueLength: { warning: 20, critical: 50 } // queue size
    };

    // Only start monitoring in production
    if (process.env.NODE_ENV === 'production') {
      this.startMonitoring();

      AIServiceLogger.info('Performance Monitor initialized', {
        thresholds: this.thresholds
      });
    } else {
      AIServiceLogger.info('Performance Monitor disabled in development');
    }
  }

  recordMetric(
    operation: string,
    value: number,
    metadata?: any,
    userId?: string
  ): void {
    const metric: PerformanceMetric = {
      timestamp: Date.now(),
      value,
      operation,
      userId,
      metadata
    };

    const operationMetrics = this.metrics.get(operation) || [];
    operationMetrics.push(metric);

    // Keep only last 1000 metrics per operation
    if (operationMetrics.length > 1000) {
      operationMetrics.shift();
    }

    this.metrics.set(operation, operationMetrics);

    // Check for threshold violations
    this.checkThresholds(operation, value);
  }

  recordResponseTime(operation: string, responseTime: number, userId?: string): void {
    this.recordMetric(`${operation}_response_time`, responseTime, { unit: 'ms' }, userId);
  }

  recordThroughput(operation: string, requestsPerSecond: number): void {
    this.recordMetric(`${operation}_throughput`, requestsPerSecond, { unit: 'req/s' });
  }

  recordError(operation: string, errorType: string, userId?: string): void {
    this.recordMetric(`${operation}_error`, 1, { errorType }, userId);
  }

  recordCacheHit(operation: string, hit: boolean): void {
    this.recordMetric(`${operation}_cache`, hit ? 1 : 0, { type: hit ? 'hit' : 'miss' });
  }

  recordMemoryUsage(component: string, usageBytes: number, maxBytes: number): void {
    const usageRatio = usageBytes / maxBytes;
    this.recordMetric(`${component}_memory`, usageRatio, { 
      usageBytes, 
      maxBytes, 
      unit: 'ratio' 
    });
  }

  recordQueueLength(component: string, queueLength: number): void {
    this.recordMetric(`${component}_queue`, queueLength, { unit: 'count' });
  }

  private checkThresholds(operation: string, value: number): void {
    const operationType = this.extractOperationType(operation);
    
    if (operationType === 'response_time') {
      this.checkResponseTimeThreshold(operation, value);
    } else if (operationType === 'throughput') {
      this.checkThroughputThreshold(operation, value);
    } else if (operationType === 'error') {
      this.checkErrorRateThreshold(operation);
    } else if (operationType === 'cache') {
      this.checkCacheHitRateThreshold(operation);
    } else if (operationType === 'memory') {
      this.checkMemoryUsageThreshold(operation, value);
    } else if (operationType === 'queue') {
      this.checkQueueLengthThreshold(operation, value);
    }
  }

  private checkResponseTimeThreshold(operation: string, responseTime: number): void {
    const thresholds = this.thresholds.responseTime;
    
    if (responseTime > thresholds.critical) {
      this.createAlert('critical', operation, 'Response time critical', 
        thresholds.critical, responseTime, [
          'Check for database query optimization opportunities',
          'Review AI service timeout settings',
          'Consider implementing request queuing',
          'Investigate potential memory leaks'
        ]);
    } else if (responseTime > thresholds.warning) {
      this.createAlert('warning', operation, 'Response time elevated',
        thresholds.warning, responseTime, [
          'Monitor for trending performance degradation',
          'Review recent code changes',
          'Check cache hit rates'
        ]);
    }
  }

  private checkThroughputThreshold(operation: string, throughput: number): void {
    const thresholds = this.thresholds.throughput;
    
    if (throughput < thresholds.critical) {
      this.createAlert('critical', operation, 'Throughput critically low',
        thresholds.critical, throughput, [
          'Increase server capacity',
          'Implement request batching',
          'Optimize database connections',
          'Review rate limiting settings'
        ]);
    } else if (throughput < thresholds.warning) {
      this.createAlert('warning', operation, 'Throughput below optimal',
        thresholds.warning, throughput, [
          'Monitor for capacity planning needs',
          'Review request optimization opportunities'
        ]);
    }
  }

  private checkErrorRateThreshold(operation: string): void {
    const errorRate = this.calculateErrorRate(operation);
    const thresholds = this.thresholds.errorRate;
    
    if (errorRate > thresholds.critical) {
      this.createAlert('critical', operation, 'Error rate critically high',
        thresholds.critical, errorRate, [
          'Investigate root cause of errors',
          'Check AI service connectivity',
          'Review input validation logic',
          'Implement circuit breaker pattern'
        ]);
    } else if (errorRate > thresholds.warning) {
      this.createAlert('warning', operation, 'Error rate elevated',
        thresholds.warning, errorRate, [
          'Monitor error patterns',
          'Review recent deployments',
          'Check external service dependencies'
        ]);
    }
  }

  private checkCacheHitRateThreshold(operation: string): void {
    const hitRate = this.calculateCacheHitRate(operation);
    const thresholds = this.thresholds.cacheHitRate;
    
    if (hitRate < thresholds.critical) {
      this.createAlert('critical', operation, 'Cache hit rate critically low',
        thresholds.critical, hitRate, [
          'Review cache TTL settings',
          'Implement cache warming strategies',
          'Optimize cache key generation',
          'Consider increasing cache size'
        ]);
    } else if (hitRate < thresholds.warning) {
      this.createAlert('warning', operation, 'Cache hit rate below optimal',
        thresholds.warning, hitRate, [
          'Monitor cache usage patterns',
          'Consider cache optimization'
        ]);
    }
  }

  private checkMemoryUsageThreshold(operation: string, usage: number): void {
    const thresholds = this.thresholds.memoryUsage;
    
    if (usage > thresholds.critical) {
      this.createAlert('critical', operation, 'Memory usage critically high',
        thresholds.critical, usage, [
          'Implement immediate memory cleanup',
          'Review cache size limits',
          'Check for memory leaks',
          'Consider scaling up resources'
        ]);
    } else if (usage > thresholds.warning) {
      this.createAlert('warning', operation, 'Memory usage elevated',
        thresholds.warning, usage, [
          'Monitor memory growth trends',
          'Review cache eviction policies',
          'Plan for capacity scaling'
        ]);
    }
  }

  private checkQueueLengthThreshold(operation: string, queueLength: number): void {
    const thresholds = this.thresholds.queueLength;
    
    if (queueLength > thresholds.critical) {
      this.createAlert('critical', operation, 'Queue length critically high',
        thresholds.critical, queueLength, [
          'Increase processing capacity',
          'Implement request prioritization',
          'Review timeout settings',
          'Consider load balancing'
        ]);
    } else if (queueLength > thresholds.warning) {
      this.createAlert('warning', operation, 'Queue length elevated',
        thresholds.warning, queueLength, [
          'Monitor queue growth trends',
          'Review processing efficiency'
        ]);
    }
  }

  private createAlert(
    type: 'warning' | 'critical',
    metric: string,
    message: string,
    threshold: number,
    currentValue: number,
    recommendations: string[]
  ): void {
    const alert: PerformanceAlert = {
      id: `alert_${++this.alertCounter}_${Date.now()}`,
      type,
      message,
      timestamp: Date.now(),
      metric,
      threshold,
      currentValue,
      recommendations
    };

    this.alerts.push(alert);

    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts.shift();
    }

    // Send to enhanced alerting service
    enhancedAlertingService.createAlert({
      title: `Performance Alert: ${metric}`,
      message: `${message} - Current: ${currentValue}, Threshold: ${threshold}`,
      severity: type === 'critical' ? 'critical' : 'medium',
      type: 'PERFORMANCE',
      source: 'performance_monitor',
      metadata: {
        metric,
        threshold,
        currentValue,
        recommendations,
        originalAlertId: alert.id
      }
    }).catch(error => {
      console.error('Failed to send alert to enhanced alerting service:', error);
    });

    AIServiceLogger[type === 'critical' ? 'error' : 'warn']('Performance alert', {
      alert: {
        type: alert.type,
        message: alert.message,
        metric: alert.metric,
        threshold: alert.threshold,
        currentValue: alert.currentValue
      }
    });
  }

  private extractOperationType(operation: string): string {
    const parts = operation.split('_');
    return parts[parts.length - 1];
  }

  private calculateErrorRate(operation: string): number {
    const baseOperation = operation.replace('_error', '');
    const errorMetrics = this.metrics.get(`${baseOperation}_error`) || [];
    const totalMetrics = this.getTotalRequestsForOperation(baseOperation);
    
    if (totalMetrics === 0) return 0;
    
    const recentErrors = errorMetrics.filter(m => 
      Date.now() - m.timestamp < 300000 // Last 5 minutes
    ).length;
    
    return recentErrors / totalMetrics;
  }

  private calculateCacheHitRate(operation: string): number {
    const baseOperation = operation.replace('_cache', '');
    const cacheMetrics = this.metrics.get(`${baseOperation}_cache`) || [];
    
    if (cacheMetrics.length === 0) return 0;
    
    const recentMetrics = cacheMetrics.filter(m => 
      Date.now() - m.timestamp < 300000 // Last 5 minutes
    );
    
    const hits = recentMetrics.filter(m => m.value === 1).length;
    return hits / recentMetrics.length;
  }

  private getTotalRequestsForOperation(operation: string): number {
    // This would be enhanced with actual request counting
    return 100; // Placeholder
  }

  private monitoringIntervals: NodeJS.Timeout[] = [];

  private startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Start enhanced alerting service
    enhancedAlertingService.start();

    // Collect system metrics every 30 seconds
    const systemMetricsInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);
    this.monitoringIntervals.push(systemMetricsInterval);

    // Generate insights every 5 minutes
    const insightsInterval = setInterval(() => {
      this.generateInsights();
    }, 300000);
    this.monitoringIntervals.push(insightsInterval);

    // Cleanup old metrics every hour
    const cleanupInterval = setInterval(() => {
      this.cleanupOldMetrics();
    }, 3600000);
    this.monitoringIntervals.push(cleanupInterval);

    // Setup graceful shutdown
    this.setupGracefulShutdown();
  }

  private setupGracefulShutdown(): void {
    const cleanup = () => {
      console.log('[Performance Monitor] Shutting down gracefully...');
      this.stopMonitoring();
    };

    // Check if listeners already exist to prevent MaxListenersExceededWarning
    if (process.listenerCount('SIGINT') < 10) {
      process.on('SIGINT', cleanup);
    }
    if (process.listenerCount('SIGTERM') < 10) {
      process.on('SIGTERM', cleanup);
    }
    if (process.listenerCount('exit') < 10) {
      process.on('exit', cleanup);
    }
  }

  stopMonitoring(): void {
    this.isMonitoring = false;

    // Stop enhanced alerting service
    enhancedAlertingService.stop();

    // Clear all intervals
    this.monitoringIntervals.forEach(interval => {
      clearInterval(interval);
    });
    this.monitoringIntervals = [];

    // Clear metrics to free memory
    this.metrics.clear();
    this.alerts.length = 0; // Clear array

    console.log('[Performance Monitor] Monitoring stopped and memory cleaned up');
  }

  private async collectSystemMetrics(): Promise<void> {
    try {
      // Collect cache metrics
      const cacheStats = advancedCacheManager.getStats();
      this.recordCacheHit('system', cacheStats.hitRate > 0);
      this.recordMemoryUsage('cache', cacheStats.memoryUsage, 100 * 1024 * 1024); // 100MB limit

      // Collect consolidated cache metrics
      const optimizerStats = await consolidatedCache.getMetrics();
      this.recordThroughput('system', optimizerStats.totalRequests / 60); // Approximate throughput per second
      this.recordQueueLength('system', 0); // No queue tracking in consolidated cache

    } catch (error) {
      AIServiceLogger.error('Failed to collect system metrics', error);
    }
  }

  private generateInsights(): void {
    try {
      const insights = this.calculatePerformanceInsights();
      
      AIServiceLogger.info('Performance insights generated', {
        overallScore: insights.overallScore,
        bottleneckCount: insights.bottlenecks.length,
        recommendationCount: insights.recommendations.length
      });

    } catch (error) {
      AIServiceLogger.error('Failed to generate performance insights', error);
    }
  }

  private calculatePerformanceInsights(): PerformanceInsights {
    // This would be a comprehensive analysis of all metrics
    return {
      overallScore: 85, // Calculated based on all metrics
      trends: {
        responseTime: 'stable',
        throughput: 'improving',
        errorRate: 'improving'
      },
      bottlenecks: [
        {
          component: 'cache',
          severity: 'medium',
          description: 'Cache hit rate could be improved',
          impact: 'Increased response times for cached operations'
        }
      ],
      recommendations: [
        {
          priority: 'high',
          category: 'caching',
          action: 'Implement cache warming for frequently accessed data',
          expectedImpact: '15-20% improvement in response times'
        }
      ]
    };
  }

  private cleanupOldMetrics(): void {
    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 hours
    
    for (const [operation, metrics] of Array.from(this.metrics.entries())) {
      const filteredMetrics = metrics.filter(m => m.timestamp > cutoff);
      this.metrics.set(operation, filteredMetrics);
    }

    // Cleanup old alerts
    this.alerts = this.alerts.filter(alert => 
      Date.now() - alert.timestamp < 24 * 60 * 60 * 1000
    );
  }

  // Public API
  getMetrics(operation?: string): Map<string, PerformanceMetric[]> | PerformanceMetric[] {
    if (operation) {
      return this.metrics.get(operation) || [];
    }
    return new Map(this.metrics);
  }

  getAlerts(type?: 'warning' | 'critical'): PerformanceAlert[] {
    if (type) {
      return this.alerts.filter(alert => alert.type === type);
    }
    return [...this.alerts];
  }

  getInsights(): PerformanceInsights {
    return this.calculatePerformanceInsights();
  }

  getThresholds(): PerformanceThresholds {
    return { ...this.thresholds };
  }

  updateThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    AIServiceLogger.info('Performance thresholds updated', { thresholds: this.thresholds });
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    activeAlerts: number;
    criticalAlerts: number;
    overallScore: number;
  }> {
    const criticalAlerts = this.alerts.filter(a => a.type === 'critical').length;
    const insights = this.getInsights();
    
    const status = criticalAlerts > 0 ? 'unhealthy' :
                  this.alerts.length > 5 ? 'degraded' : 'healthy';

    return {
      status,
      activeAlerts: this.alerts.length,
      criticalAlerts,
      overallScore: insights.overallScore
    };
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();
export { PerformanceMonitor };
