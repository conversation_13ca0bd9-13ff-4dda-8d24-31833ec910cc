/**
 * User-Friendly Error Message Mapper
 * Converts technical error messages to user-friendly ones
 */

import { ErrorRecoveryGuidance } from '@/lib/error-recovery-guidance';

export interface UserFriendlyError {
  title: string;
  message: string;
  action?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'auth' | 'network' | 'validation' | 'permission' | 'system' | 'data';
  recoveryGuidance?: any; // Recovery guidance from ErrorRecoveryGuidance
}

/**
 * Maps technical error codes and messages to user-friendly alternatives
 */
export class UserFriendlyErrorMapper {
  private static readonly errorMap: Record<string, UserFriendlyError> = {
    // HTTP Status Codes
    '400': {
      title: 'Invalid Request',
      message: 'The information you provided is not valid. Please check your input and try again.',
      action: 'Please review your information and try again.',
      severity: 'medium',
      category: 'validation'
    },
    '401': {
      title: 'Sign In Required',
      message: 'You need to sign in to access this feature.',
      action: 'Please sign in to continue.',
      severity: 'medium',
      category: 'auth'
    },
    '403': {
      title: 'Access Denied',
      message: 'You don\'t have permission to access this feature.',
      action: 'Contact support if you believe this is an error.',
      severity: 'medium',
      category: 'permission'
    },
    '404': {
      title: 'Not Found',
      message: 'The page or information you\'re looking for doesn\'t exist.',
      action: 'Please check the URL or go back to the previous page.',
      severity: 'low',
      category: 'data'
    },
    '429': {
      title: 'Too Many Requests',
      message: 'You\'re making requests too quickly. Please wait a moment before trying again.',
      action: 'Please wait a few minutes and try again.',
      severity: 'medium',
      category: 'system'
    },
    '500': {
      title: 'Server Error',
      message: 'Something went wrong on our end. We\'re working to fix this.',
      action: 'Please try again in a few minutes.',
      severity: 'high',
      category: 'system'
    },
    '502': {
      title: 'Service Unavailable',
      message: 'Our service is temporarily unavailable. Please try again shortly.',
      action: 'Please try again in a few minutes.',
      severity: 'high',
      category: 'system'
    },
    '503': {
      title: 'Service Maintenance',
      message: 'We\'re performing maintenance. The service will be back shortly.',
      action: 'Please try again in a few minutes.',
      severity: 'high',
      category: 'system'
    },

    // Common Error Codes
    'UNAUTHORIZED': {
      title: 'Sign In Required',
      message: 'You need to sign in to access this feature.',
      action: 'Please sign in to continue.',
      severity: 'medium',
      category: 'auth'
    },
    'FORBIDDEN': {
      title: 'Access Denied',
      message: 'You don\'t have permission to perform this action.',
      action: 'Contact support if you believe this is an error.',
      severity: 'medium',
      category: 'permission'
    },
    'NOT_FOUND': {
      title: 'Not Found',
      message: 'The information you\'re looking for doesn\'t exist.',
      action: 'Please check your request and try again.',
      severity: 'low',
      category: 'data'
    },
    'VALIDATION_ERROR': {
      title: 'Invalid Information',
      message: 'Some of the information you provided is not valid.',
      action: 'Please check the highlighted fields and try again.',
      severity: 'medium',
      category: 'validation'
    },
    'RATE_LIMIT_EXCEEDED': {
      title: 'Too Many Attempts',
      message: 'You\'ve made too many requests. Please wait before trying again.',
      action: 'Please wait a few minutes and try again.',
      severity: 'medium',
      category: 'system'
    },
    'DATABASE_ERROR': {
      title: 'Data Error',
      message: 'We\'re having trouble accessing your data right now.',
      action: 'Please try again in a few minutes.',
      severity: 'high',
      category: 'system'
    },
    'NETWORK_ERROR': {
      title: 'Connection Problem',
      message: 'We\'re having trouble connecting to our servers.',
      action: 'Please check your internet connection and try again.',
      severity: 'medium',
      category: 'network'
    },

    // Authentication Specific
    'INVALID_CREDENTIALS': {
      title: 'Sign In Failed',
      message: 'The email or password you entered is incorrect.',
      action: 'Please check your credentials and try again.',
      severity: 'medium',
      category: 'auth'
    },
    'TOKEN_EXPIRED': {
      title: 'Session Expired',
      message: 'Your session has expired for security reasons.',
      action: 'Please sign in again to continue.',
      severity: 'medium',
      category: 'auth'
    },
    'ACCOUNT_LOCKED': {
      title: 'Account Locked',
      message: 'Your account has been temporarily locked for security.',
      action: 'Please contact support or try again later.',
      severity: 'high',
      category: 'auth'
    },

    // Validation Specific
    'MISSING_REQUIRED_FIELD': {
      title: 'Missing Information',
      message: 'Some required information is missing.',
      action: 'Please fill in all required fields.',
      severity: 'medium',
      category: 'validation'
    },
    'INVALID_INPUT': {
      title: 'Invalid Input',
      message: 'Some of the information you entered is not in the correct format.',
      action: 'Please check your input and try again.',
      severity: 'medium',
      category: 'validation'
    },

    // System Specific
    'INTERNAL_ERROR': {
      title: 'System Error',
      message: 'Something unexpected happened. We\'re working to fix this.',
      action: 'Please try again in a few minutes.',
      severity: 'high',
      category: 'system'
    },
    'EXTERNAL_SERVICE_ERROR': {
      title: 'Service Unavailable',
      message: 'An external service we depend on is currently unavailable.',
      action: 'Please try again in a few minutes.',
      severity: 'high',
      category: 'system'
    }
  };

  /**
   * Convert a technical error to a user-friendly message
   */
  static mapError(error: any, context?: string): UserFriendlyError {
    let friendlyError: UserFriendlyError;

    // Handle string errors
    if (typeof error === 'string') {
      friendlyError = this.mapErrorString(error);
    }
    // Handle error objects
    else if (error && typeof error === 'object') {
      friendlyError = this.mapErrorObject(error);
    }
    // Default fallback
    else {
      friendlyError = this.getDefaultError();
    }

    // Add recovery guidance
    const errorCode = this.extractErrorCode(error);
    const recoveryGuidance = ErrorRecoveryGuidance.getGuidance(errorCode, context);
    if (recoveryGuidance) {
      friendlyError.recoveryGuidance = recoveryGuidance;
    }

    return friendlyError;
  }

  /**
   * Map error string to user-friendly message
   */
  private static mapErrorString(error: string): UserFriendlyError {
    // Check for HTTP status codes in the string
    const statusMatch = error.match(/\b(4\d{2}|5\d{2})\b/);
    if (statusMatch) {
      const statusCode = statusMatch[1];
      if (this.errorMap[statusCode]) {
        return this.errorMap[statusCode];
      }
    }

    // Check for common error patterns
    const lowerError = error.toLowerCase();
    
    if (lowerError.includes('forbidden') || lowerError.includes('403')) {
      return this.errorMap['403'];
    }
    
    if (lowerError.includes('unauthorized') || lowerError.includes('401')) {
      return this.errorMap['401'];
    }
    
    if (lowerError.includes('not found') || lowerError.includes('404')) {
      return this.errorMap['404'];
    }
    
    if (lowerError.includes('network') || lowerError.includes('connection')) {
      return this.errorMap['NETWORK_ERROR'];
    }
    
    if (lowerError.includes('timeout')) {
      return {
        title: 'Request Timeout',
        message: 'The request is taking longer than expected.',
        action: 'Please try again.',
        severity: 'medium',
        category: 'network'
      };
    }

    // Return a generic error for unrecognized strings
    return this.getDefaultError();
  }

  /**
   * Map error object to user-friendly message
   */
  private static mapErrorObject(error: any): UserFriendlyError {
    // Check for error code
    if (error.code && this.errorMap[error.code]) {
      return this.errorMap[error.code];
    }

    // Check for HTTP status
    if (error.status && this.errorMap[error.status.toString()]) {
      return this.errorMap[error.status.toString()];
    }

    // Check for response status
    if (error.response?.status && this.errorMap[error.response.status.toString()]) {
      return this.errorMap[error.response.status.toString()];
    }

    // Check error message for patterns
    if (error.message) {
      return this.mapErrorString(error.message);
    }

    return this.getDefaultError();
  }

  /**
   * Extract error code from error for recovery guidance
   */
  private static extractErrorCode(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error && typeof error === 'object') {
      return error.code || error.status?.toString() || error.message || 'UNKNOWN_ERROR';
    }

    return 'UNKNOWN_ERROR';
  }

  /**
   * Get default error message
   */
  private static getDefaultError(): UserFriendlyError {
    return {
      title: 'Something Went Wrong',
      message: 'An unexpected error occurred. Please try again.',
      action: 'If the problem persists, please contact support.',
      severity: 'medium',
      category: 'system'
    };
  }

  /**
   * Get user-friendly error for specific contexts
   */
  static getContextualError(context: string, error: any): UserFriendlyError {
    const baseError = this.mapError(error);
    
    // Customize message based on context
    switch (context) {
      case 'login':
        if (baseError.category === 'auth') {
          return {
            ...baseError,
            message: 'Unable to sign you in. Please check your email and password.',
            action: 'Verify your credentials and try again.'
          };
        }
        break;
        
      case 'assessment':
        if (baseError.category === 'system') {
          return {
            ...baseError,
            message: 'We\'re having trouble processing your assessment right now.',
            action: 'Your progress has been saved. Please try again in a few minutes.'
          };
        }
        break;
        
      case 'resume':
        if (baseError.category === 'system') {
          return {
            ...baseError,
            message: 'We\'re having trouble saving your resume changes.',
            action: 'Your work is automatically saved. Please try again.'
          };
        }
        break;
        
      case 'interview':
        if (baseError.category === 'system') {
          return {
            ...baseError,
            message: 'We\'re having trouble generating interview questions.',
            action: 'Please try selecting a different interview type or try again later.'
          };
        }
        break;
    }
    
    return baseError;
  }

  /**
   * Check if an error should be shown to the user
   */
  static shouldShowToUser(error: any): boolean {
    const mappedError = this.mapError(error);
    
    // Don't show critical system errors to users in production
    if (process.env.NODE_ENV === 'production' && mappedError.severity === 'critical') {
      return false;
    }
    
    return true;
  }
}

/**
 * Convenience function to get user-friendly error message
 */
export function getUserFriendlyError(error: any, context?: string): UserFriendlyError {
  if (context) {
    return UserFriendlyErrorMapper.getContextualError(context, error);
  }
  return UserFriendlyErrorMapper.mapError(error);
}

/**
 * Convenience function to format error for display
 */
export function formatErrorForDisplay(error: any, context?: string): {
  title: string;
  message: string;
  action?: string;
} {
  const friendlyError = getUserFriendlyError(error, context);
  return {
    title: friendlyError.title,
    message: friendlyError.message,
    action: friendlyError.action
  };
}
