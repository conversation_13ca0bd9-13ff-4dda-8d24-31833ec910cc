// Performance monitoring and analytics utilities

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface ErrorEvent {
  message: string;
  stack?: string;
  url: string;
  timestamp: number;
  userId?: string;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private errors: ErrorEvent[] = [];
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'production';
    this.setupErrorHandling();
    this.setupPerformanceObserver();
  }

  // Track custom metrics
  trackMetric(name: string, value: number, metadata?: Record<string, any>) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata
    };

    this.metrics.push(metric);
    this.sendMetricToAnalytics(metric);
  }

  // Track API response times
  trackAPICall(endpoint: string, duration: number, status: number) {
    this.trackMetric('api_response_time', duration, {
      endpoint,
      status,
      type: 'api_call'
    });
  }

  // Track page load times
  trackPageLoad(page: string, duration: number) {
    this.trackMetric('page_load_time', duration, {
      page,
      type: 'page_load'
    });
  }

  // Track user interactions
  trackUserAction(action: string, metadata?: Record<string, any>) {
    this.trackMetric('user_action', 1, {
      action,
      type: 'user_interaction',
      ...metadata
    });
  }

  // Track database query performance
  trackDatabaseQuery(query: string, duration: number, success: boolean) {
    this.trackMetric('database_query_time', duration, {
      query: query.substring(0, 100), // Truncate for privacy
      success,
      type: 'database_query'
    });
  }

  // Error tracking
  trackError(error: Error, metadata?: Record<string, any>) {
    if (!this.isEnabled) {
      console.error('Error tracked:', error);
      return;
    }

    const errorEvent: ErrorEvent = {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : String(error),
      url: typeof window !== 'undefined' ? window.location.href : 'server',
      timestamp: Date.now(),
      metadata
    };

    this.errors.push(errorEvent);
    this.sendErrorToAnalytics(errorEvent);
  }

  // Setup global error handling
  private setupErrorHandling() {
    if (typeof window === 'undefined') return;

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(new Error(event.reason), {
        type: 'unhandled_promise_rejection'
      });
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.trackError(new Error(event.message), {
        type: 'javascript_error',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });
  }

  // Setup performance observer for Web Vitals
  private setupPerformanceObserver() {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      // Observe Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            this.trackMetric('lcp', entry.startTime, { type: 'web_vital' });
          } else if (entry.entryType === 'first-input') {
            this.trackMetric('fid', (entry as any).processingStart - entry.startTime, { type: 'web_vital' });
          } else if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
            this.trackMetric('cls', (entry as any).value, { type: 'web_vital' });
          }
        }
      });

      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    } catch (error) {
      console.warn('Performance observer not supported:', error);
    }
  }

  // Send metrics to analytics service
  private sendMetricToAnalytics(metric: PerformanceMetric) {
    // In production, send to your analytics service
    // For now, just log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Metric:', metric);
    }

    // Example: Send to external service
    // fetch('/api/analytics/metrics', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(metric)
    // }).catch(console.error);
  }

  // Send errors to error tracking service
  private sendErrorToAnalytics(error: ErrorEvent) {
    // In production, send to your error tracking service (Sentry, LogRocket, etc.)
    if (process.env.NODE_ENV === 'development') {
      console.error('Error tracked:', error);
    }

    // Example: Send to external service
    // fetch('/api/analytics/errors', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(error)
    // }).catch(console.error);
  }

  // Get performance summary
  getPerformanceSummary() {
    const summary = {
      totalMetrics: this.metrics.length,
      totalErrors: this.errors.length,
      averagePageLoadTime: this.getAverageMetric('page_load_time'),
      averageAPIResponseTime: this.getAverageMetric('api_response_time'),
      recentErrors: this.errors.slice(-5)
    };

    return summary;
  }

  private getAverageMetric(name: string): number {
    const relevantMetrics = this.metrics.filter(m => m.name === name);
    if (relevantMetrics.length === 0) return 0;
    
    const sum = relevantMetrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / relevantMetrics.length;
  }

  // Clear old metrics to prevent memory leaks
  clearOldMetrics(olderThanMs: number = 24 * 60 * 60 * 1000) { // 24 hours
    const cutoff = Date.now() - olderThanMs;
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
    this.errors = this.errors.filter(e => e.timestamp > cutoff);
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for common tracking scenarios
export const trackPageView = (page: string) => {
  performanceMonitor.trackUserAction('page_view', { page });
};

export const trackButtonClick = (buttonName: string, location: string) => {
  performanceMonitor.trackUserAction('button_click', { buttonName, location });
};

export const trackFormSubmission = (formName: string, success: boolean) => {
  performanceMonitor.trackUserAction('form_submission', { formName, success });
};

export const trackSearchQuery = (query: string, resultsCount: number) => {
  performanceMonitor.trackUserAction('search', { 
    query: query.substring(0, 50), // Truncate for privacy
    resultsCount 
  });
};

// Higher-order function to track API call performance
export function withPerformanceTracking<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  name: string
): T {
  return (async (...args: any[]) => {
    const startTime = Date.now();
    try {
      const result = await fn(...args);
      const duration = Date.now() - startTime;
      performanceMonitor.trackAPICall(name, duration, 200);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      performanceMonitor.trackAPICall(name, duration, 500);
      performanceMonitor.trackError(error as Error, { context: name });
      throw error;
    }
  }) as T;
}

// React hook for tracking component performance
export function usePerformanceTracking(componentName: string) {
  const trackRender = () => {
    performanceMonitor.trackMetric('component_render', 1, {
      component: componentName,
      type: 'component_render'
    });
  };

  const trackInteraction = (interaction: string) => {
    performanceMonitor.trackUserAction('component_interaction', {
      component: componentName,
      interaction
    });
  };

  return { trackRender, trackInteraction };
}

export default performanceMonitor;
