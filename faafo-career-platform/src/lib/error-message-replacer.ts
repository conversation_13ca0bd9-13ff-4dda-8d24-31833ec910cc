/**
 * Error Message Replacer
 * Automatically replaces technical error messages with user-friendly ones
 */

import { getUserFriendlyError } from '@/lib/user-friendly-errors';

/**
 * Replace technical error messages in API responses
 */
export function replaceApiErrorMessages(response: any): any {
  if (!response || typeof response !== 'object') {
    return response;
  }

  // Handle array responses
  if (Array.isArray(response)) {
    return response.map(item => replaceApiErrorMessages(item));
  }

  // Create a copy to avoid mutating the original
  const processedResponse = { ...response };

  // Replace error messages in common API response patterns
  if (processedResponse.error) {
    const friendlyError = getUserFriendlyError(processedResponse.error);
    processedResponse.error = friendlyError.message;
    
    // Add additional user-friendly fields
    if (friendlyError.title !== friendlyError.message) {
      processedResponse.errorTitle = friendlyError.title;
    }
    if (friendlyError.action) {
      processedResponse.errorAction = friendlyError.action;
    }
  }

  // Replace error messages in nested error objects
  if (processedResponse.errors && typeof processedResponse.errors === 'object') {
    const processedErrors: any = {};
    for (const [key, value] of Object.entries(processedResponse.errors)) {
      if (typeof value === 'string') {
        const friendlyError = getUserFriendlyError(value);
        processedErrors[key] = friendlyError.message;
      } else {
        processedErrors[key] = replaceApiErrorMessages(value);
      }
    }
    processedResponse.errors = processedErrors;
  }

  // Replace message field if it contains technical details
  if (processedResponse.message && typeof processedResponse.message === 'string') {
    if (containsTechnicalDetails(processedResponse.message)) {
      const friendlyError = getUserFriendlyError(processedResponse.message);
      processedResponse.message = friendlyError.message;
    }
  }

  // Recursively process nested objects
  for (const [key, value] of Object.entries(processedResponse)) {
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      processedResponse[key] = replaceApiErrorMessages(value);
    }
  }

  return processedResponse;
}

/**
 * Check if a message contains technical details that should be replaced
 */
function containsTechnicalDetails(message: string): boolean {
  const technicalPatterns = [
    /\b(4\d{2}|5\d{2})\b/, // HTTP status codes
    /\bHTTP\s+\d{3}\b/i, // HTTP status format
    /\bForbidden\b/i,
    /\bUnauthorized\b/i,
    /\bInternal\s+Server\s+Error\b/i,
    /\bBad\s+Request\b/i,
    /\bNot\s+Found\b/i,
    /\bDatabase\s+error\b/i,
    /\bSQL\b/i,
    /\bPrisma\b/i,
    /\bP\d{4}\b/, // Prisma error codes
    /\bstack\s+trace\b/i,
    /\bat\s+\w+\.\w+/i, // Stack trace patterns
    /\bERROR\s*:\s*/i,
    /\bException\b/i,
    /\bfailed\s+to\s+fetch\b/i,
    /\bnetwork\s+error\b/i,
    /\btimeout\b/i,
    /\baborted\b/i
  ];

  return technicalPatterns.some(pattern => pattern.test(message));
}

/**
 * Fetch wrapper that automatically replaces error messages
 */
export async function fetchWithFriendlyErrors(
  url: string, 
  options?: RequestInit,
  context?: string
): Promise<Response> {
  try {
    const response = await fetch(url, options);
    
    // If response is not ok, try to get error details and make them user-friendly
    if (!response.ok) {
      try {
        const errorData = await response.clone().json();
        const friendlyError = getUserFriendlyError(
          errorData.error || errorData.message || `HTTP ${response.status}`,
          context
        );
        
        // Create a new response with friendly error message
        const friendlyErrorData = {
          ...errorData,
          error: friendlyError.message,
          errorTitle: friendlyError.title,
          errorAction: friendlyError.action
        };
        
        // Return a new response with the friendly error data
        return new Response(JSON.stringify(friendlyErrorData), {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers
        });
      } catch {
        // If we can't parse the error, just return the original response
        return response;
      }
    }
    
    return response;
  } catch (error) {
    // For network errors, throw a user-friendly error
    const friendlyError = getUserFriendlyError(error, context);
    throw new Error(friendlyError.message);
  }
}

/**
 * Hook for components to use friendly error handling
 */
export function useFriendlyErrorHandler(context?: string) {
  const handleError = (error: any) => {
    const friendlyError = getUserFriendlyError(error, context);
    return {
      title: friendlyError.title,
      message: friendlyError.message,
      action: friendlyError.action,
      severity: friendlyError.severity,
      category: friendlyError.category
    };
  };

  const handleApiResponse = async (response: Response) => {
    if (!response.ok) {
      try {
        const errorData = await response.json();
        const friendlyError = getUserFriendlyError(
          errorData.error || errorData.message || `HTTP ${response.status}`,
          context
        );
        throw new Error(friendlyError.message);
      } catch (parseError) {
        const friendlyError = getUserFriendlyError(`HTTP ${response.status}`, context);
        throw new Error(friendlyError.message);
      }
    }
    return response;
  };

  return {
    handleError,
    handleApiResponse,
    fetchWithFriendlyErrors: (url: string, options?: RequestInit) => 
      fetchWithFriendlyErrors(url, options, context)
  };
}

/**
 * Global error message replacer for console errors
 */
export function setupGlobalErrorReplacement() {
  // Only in development mode to avoid affecting production logging
  if (process.env.NODE_ENV === 'development') {
    const originalConsoleError = console.error;
    
    console.error = (...args: any[]) => {
      const processedArgs = args.map(arg => {
        if (typeof arg === 'string' && containsTechnicalDetails(arg)) {
          const friendlyError = getUserFriendlyError(arg);
          return `${arg} (User-friendly: ${friendlyError.message})`;
        }
        return arg;
      });
      
      originalConsoleError.apply(console, processedArgs);
    };
  }
}

/**
 * Middleware for API routes to automatically replace error messages
 */
export function withFriendlyErrorMessages(handler: Function) {
  return async (...args: any[]) => {
    try {
      const result = await handler(...args);
      
      // If result is a Response, process its JSON
      if (result instanceof Response) {
        try {
          const data = await result.clone().json();
          const processedData = replaceApiErrorMessages(data);
          
          return new Response(JSON.stringify(processedData), {
            status: result.status,
            statusText: result.statusText,
            headers: result.headers
          });
        } catch {
          // If we can't parse JSON, return original response
          return result;
        }
      }
      
      // If result is an object, process it directly
      if (result && typeof result === 'object') {
        return replaceApiErrorMessages(result);
      }
      
      return result;
    } catch (error) {
      // Replace error messages in thrown errors
      const friendlyError = getUserFriendlyError(error);
      throw new Error(friendlyError.message);
    }
  };
}

/**
 * Utility to clean up error messages for display
 */
export function cleanErrorMessage(message: string): string {
  if (!message || typeof message !== 'string') {
    return 'An unexpected error occurred.';
  }

  // Remove technical prefixes
  const cleanedMessage = message
    .replace(/^Error:\s*/i, '')
    .replace(/^HTTP\s+\d{3}:\s*/i, '')
    .replace(/^Forbidden\s*\(status:\s*\d+\)\s*/i, 'Access denied. ')
    .replace(/^Unauthorized\s*\(status:\s*\d+\)\s*/i, 'Sign in required. ')
    .replace(/^Not\s+Found\s*\(status:\s*\d+\)\s*/i, 'Resource not found. ')
    .replace(/^Internal\s+Server\s+Error\s*\(status:\s*\d+\)\s*/i, 'Server error. ')
    .replace(/\s*\(status:\s*\d+\)\s*$/i, '')
    .trim();

  // If the cleaned message is too technical, get a user-friendly version
  if (containsTechnicalDetails(cleanedMessage)) {
    const friendlyError = getUserFriendlyError(cleanedMessage);
    return friendlyError.message;
  }

  return cleanedMessage || 'An unexpected error occurred.';
}
