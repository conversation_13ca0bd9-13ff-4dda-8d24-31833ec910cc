/**
 * Unified API Error Handler
 * 
 * This module provides a standardized error handling system for all API routes.
 * It consolidates the various error handling patterns into a single, secure,
 * and consistent approach.
 */

import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { log } from '@/lib/logger';
import { trackError } from '@/lib/errorTracking';
import { ErrorReporter } from '@/lib/errorReporting';

// Standard error codes
export const API_ERROR_CODES = {
  // Authentication & Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  
  // Database
  NOT_FOUND: 'NOT_FOUND',
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Server Errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // Business Logic
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS'
} as const;

export type ApiErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES];

// Standard error response interface
export interface ApiErrorResponse {
  success: false;
  error: {
    code: ApiErrorCode;
    message: string;
    details?: any;
    requestId: string;
    timestamp: string;
  };
}

// Standard success response interface
export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  requestId?: string;
  timestamp?: string;
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

// Error context for logging
interface ErrorContext {
  endpoint: string;
  method: string;
  userId?: string;
  userEmail?: string;
  userAgent?: string;
  ip?: string;
  requestId: string;
  timestamp: string;
}

class UnifiedApiErrorHandler {
  private static generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static getClientIp(request: NextRequest): string | undefined {
    return request.headers.get('x-forwarded-for') || 
           request.headers.get('x-real-ip') || 
           undefined;
  }

  private static async getErrorContext(request: NextRequest): Promise<ErrorContext> {
    const requestId = this.generateRequestId();
    const timestamp = new Date().toISOString();
    
    // Try to get session for user context (but don't fail if it's not available)
    let userId: string | undefined;
    let userEmail: string | undefined;
    
    try {
      const session = await getServerSession(authOptions);
      userId = session?.user?.id || undefined;
      userEmail = session?.user?.email || undefined;
    } catch {
      // Session retrieval failed, continue without user context
    }

    return {
      endpoint: request.nextUrl.pathname,
      method: request.method,
      userId,
      userEmail,
      userAgent: request.headers.get('user-agent') || undefined,
      ip: this.getClientIp(request),
      requestId,
      timestamp
    };
  }

  private static logError(error: unknown, context: ErrorContext): void {
    // Log to our structured logger
    log.error('API Error', error as Error, {
      component: 'unified_api_error_handler',
      action: 'handle_error',
      userId: context.userEmail,
      requestId: context.requestId
    });

    // Track error for monitoring
    trackError.api(error as Error, context.endpoint, context.method, 500);

    // Report to error tracking service
    ErrorReporter.captureError(error as Error, {
      userId: context.userEmail,
      userEmail: context.userEmail,
      action: 'api_error',
      component: 'unified_api_error_handler'
    });
  }

  private static createErrorResponse(
    code: ApiErrorCode,
    message: string,
    requestId: string,
    timestamp: string,
    details?: any
  ): ApiErrorResponse {
    return {
      success: false,
      error: {
        code,
        message,
        details: process.env.NODE_ENV === 'development' ? details : undefined,
        requestId,
        timestamp
      }
    };
  }

  private static handleZodError(error: ZodError, requestId: string, timestamp: string): ApiErrorResponse {
    const validationErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    }));

    return this.createErrorResponse(
      API_ERROR_CODES.VALIDATION_ERROR,
      'Validation failed',
      requestId,
      timestamp,
      validationErrors
    );
  }

  private static handlePrismaError(error: Prisma.PrismaClientKnownRequestError, requestId: string, timestamp: string): ApiErrorResponse {
    switch (error.code) {
      case 'P2002':
        return this.createErrorResponse(
          API_ERROR_CODES.DUPLICATE_ENTRY,
          'A record with this information already exists',
          requestId,
          timestamp
        );
      case 'P2025':
        return this.createErrorResponse(
          API_ERROR_CODES.NOT_FOUND,
          'The requested resource was not found',
          requestId,
          timestamp
        );
      case 'P2003':
        return this.createErrorResponse(
          API_ERROR_CODES.DATABASE_ERROR,
          'Foreign key constraint failed',
          requestId,
          timestamp
        );
      default:
        return this.createErrorResponse(
          API_ERROR_CODES.DATABASE_ERROR,
          'Database operation failed',
          requestId,
          timestamp,
          process.env.NODE_ENV === 'development' ? error instanceof Error ? error.message : String(error) : undefined
        );
    }
  }

  private static handleAuthError(error: any, requestId: string, timestamp: string): ApiErrorResponse {
    if (error instanceof Error ? error.message : String(error)?.includes('unauthorized') || error instanceof Error ? error.message : String(error)?.includes('Unauthorized')) {
      return this.createErrorResponse(
        API_ERROR_CODES.UNAUTHORIZED,
        'Authentication required',
        requestId,
        timestamp
      );
    }

    if (error instanceof Error ? error.message : String(error)?.includes('forbidden') || error instanceof Error ? error.message : String(error)?.includes('Forbidden')) {
      return this.createErrorResponse(
        API_ERROR_CODES.FORBIDDEN,
        'Access denied',
        requestId,
        timestamp
      );
    }

    return this.createErrorResponse(
      API_ERROR_CODES.UNAUTHORIZED,
      'Authentication failed',
      requestId,
      timestamp
    );
  }

  public static async handleError(error: unknown, request: NextRequest): Promise<NextResponse<ApiErrorResponse>> {
    const context = await this.getErrorContext(request);
    
    // Log the error
    this.logError(error, context);

    let errorResponse: ApiErrorResponse;

    // Handle different error types
    if (error instanceof ZodError) {
      errorResponse = this.handleZodError(error, context.requestId, context.timestamp);
    } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
      errorResponse = this.handlePrismaError(error, context.requestId, context.timestamp);
    } else if (error instanceof Prisma.PrismaClientValidationError) {
      errorResponse = this.createErrorResponse(
        API_ERROR_CODES.VALIDATION_ERROR,
        'Invalid database query',
        context.requestId,
        context.timestamp
      );
    } else if (error instanceof Error && (() => {
      const errorMessage = error.message;
      return errorMessage.includes('unauthorized') ||
             errorMessage.includes('forbidden') ||
             errorMessage.includes('Authentication') ||
             errorMessage.includes('Unauthorized');
    })()) {
      errorResponse = this.handleAuthError(error, context.requestId, context.timestamp);
    } else if (error instanceof Error && (error as any).statusCode) {
      // Handle errors with custom status codes
      const statusCode = (error as any).statusCode;
      let errorCode: ApiErrorCode;

      switch (statusCode) {
        case 404:
          errorCode = API_ERROR_CODES.NOT_FOUND;
          break;
        case 401:
          errorCode = API_ERROR_CODES.UNAUTHORIZED;
          break;
        case 403:
          errorCode = API_ERROR_CODES.FORBIDDEN;
          break;
        case 400:
          errorCode = API_ERROR_CODES.VALIDATION_ERROR;
          break;
        case 409:
          errorCode = API_ERROR_CODES.DUPLICATE_ENTRY;
          break;
        case 429:
          errorCode = API_ERROR_CODES.RATE_LIMIT_EXCEEDED;
          break;
        default:
          errorCode = API_ERROR_CODES.INTERNAL_ERROR;
      }

      errorResponse = this.createErrorResponse(
        errorCode,
        error instanceof Error ? error.message : String(error),
        context.requestId,
        context.timestamp,
        process.env.NODE_ENV === 'development' ? error instanceof Error ? error.stack : String(error) : undefined
      );
    } else {
      // Generic error handling
      errorResponse = this.createErrorResponse(
        API_ERROR_CODES.INTERNAL_ERROR,
        process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : String(error))
          : 'Internal server error',
        context.requestId,
        context.timestamp,
        process.env.NODE_ENV === 'development' && error instanceof Error ? error.stack : undefined
      );
    }

    // Determine HTTP status code
    const statusCode = this.getHttpStatusCode(errorResponse.error.code);

    return NextResponse.json(errorResponse, { status: statusCode });
  }

  private static getHttpStatusCode(errorCode: ApiErrorCode): number {
    switch (errorCode) {
      case API_ERROR_CODES.UNAUTHORIZED:
      case API_ERROR_CODES.SESSION_EXPIRED:
        return 401;
      case API_ERROR_CODES.FORBIDDEN:
      case API_ERROR_CODES.INSUFFICIENT_PERMISSIONS:
        return 403;
      case API_ERROR_CODES.NOT_FOUND:
        return 404;
      case API_ERROR_CODES.VALIDATION_ERROR:
      case API_ERROR_CODES.INVALID_INPUT:
        return 400;
      case API_ERROR_CODES.DUPLICATE_ENTRY:
        return 409;
      case API_ERROR_CODES.RATE_LIMIT_EXCEEDED:
        return 429;
      case API_ERROR_CODES.SERVICE_UNAVAILABLE:
        return 503;
      case API_ERROR_CODES.BUSINESS_RULE_VIOLATION:
        return 422;
      default:
        return 500;
    }
  }

  public static createSuccessResponse<T>(data: T, requestId?: string): ApiSuccessResponse<T> {
    return {
      success: true,
      data,
      requestId,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Unified error handling wrapper for API routes
 * This replaces withErrorHandler, withSecureErrorHandling, and manual try-catch blocks
 */
export function withUnifiedErrorHandling<T extends any[]>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      return await handler(request, ...args);
    } catch (error) {
      return await UnifiedApiErrorHandler.handleError(error, request);
    }
  };
}

export { UnifiedApiErrorHandler };
export default UnifiedApiErrorHandler;
