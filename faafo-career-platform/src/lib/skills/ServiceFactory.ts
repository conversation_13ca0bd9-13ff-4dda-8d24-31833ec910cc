import { EdgeCase<PERSON>and<PERSON> } from '@/lib/skills/EdgeCaseHandler';
import { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';
import { SkillAssessmentEngine } from '@/lib/skills/SkillAssessmentEngine';
import { SkillMarketDataService } from '@/lib/skills/SkillMarketDataService';

/**
 * Service Factory for creating properly integrated skill services
 * with EdgeCaseHandler support
 */
export class SkillServiceFactory {
  private static instance: SkillServiceFactory;
  private assessmentEngine: SkillAssessmentEngine | null = null;
  private marketDataService: SkillMarketDataService | null = null;
  private learningPathService: PersonalizedLearningPathService | null = null;
  private edgeCaseHandler: EdgeCaseHandler | null = null;

  private constructor() {}

  static getInstance(): SkillServiceFactory {
    if (!SkillServiceFactory.instance) {
      SkillServiceFactory.instance = new SkillServiceFactory();
    }
    return SkillServiceFactory.instance;
  }

  /**
   * Initialize all services with proper EdgeCaseHandler integration
   */
  initializeServices(): {
    assessmentEngine: SkillAssessmentEngine;
    marketDataService: SkillMarketDataService;
    learningPathService: PersonalizedLearningPathService;
    edgeCaseHandler: EdgeCaseHandler;
  } {
    // Create services first
    this.assessmentEngine = new SkillAssessmentEngine();
    this.marketDataService = new SkillMarketDataService();
    this.learningPathService = new PersonalizedLearningPathService();

    // Create EdgeCaseHandler with service references
    this.edgeCaseHandler = new EdgeCaseHandler(
      this.assessmentEngine,
      this.marketDataService,
      this.learningPathService
    );

    return {
      assessmentEngine: this.assessmentEngine,
      marketDataService: this.marketDataService,
      learningPathService: this.learningPathService,
      edgeCaseHandler: this.edgeCaseHandler,
    };
  }

  /**
   * Get existing services or initialize if not already done
   */
  getServices(): {
    assessmentEngine: SkillAssessmentEngine;
    marketDataService: SkillMarketDataService;
    learningPathService: PersonalizedLearningPathService;
    edgeCaseHandler: EdgeCaseHandler;
  } {
    if (!this.assessmentEngine || !this.marketDataService || !this.learningPathService || !this.edgeCaseHandler) {
      return this.initializeServices();
    }

    return {
      assessmentEngine: this.assessmentEngine,
      marketDataService: this.marketDataService,
      learningPathService: this.learningPathService,
      edgeCaseHandler: this.edgeCaseHandler,
    };
  }

  /**
   * Get individual services
   */
  getAssessmentEngine(): SkillAssessmentEngine {
    return this.getServices().assessmentEngine;
  }

  getMarketDataService(): SkillMarketDataService {
    return this.getServices().marketDataService;
  }

  getLearningPathService(): PersonalizedLearningPathService {
    return this.getServices().learningPathService;
  }

  getEdgeCaseHandler(): EdgeCaseHandler {
    return this.getServices().edgeCaseHandler;
  }

  /**
   * Reset all services (useful for testing)
   */
  reset(): void {
    this.assessmentEngine = null;
    this.marketDataService = null;
    this.learningPathService = null;
    this.edgeCaseHandler = null;
  }
}

// Export singleton instance
export const skillServiceFactory = SkillServiceFactory.getInstance();

// Export individual service getters for convenience
export const getSkillAssessmentEngine = () => skillServiceFactory.getAssessmentEngine();
export const getSkillMarketDataService = () => skillServiceFactory.getMarketDataService();
export const getPersonalizedLearningPathService = () => skillServiceFactory.getLearningPathService();
export const getEdgeCaseHandler = () => skillServiceFactory.getEdgeCaseHandler();
