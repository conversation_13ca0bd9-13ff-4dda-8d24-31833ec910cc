/**
 * Enhanced Error Recovery System
 * Provides automatic error recovery, retry mechanisms, and graceful degradation
 */

import { errorTracker } from '@/lib/errorTracking';
import { SecureErrorHandler } from '@/lib/secure-error-handler';

export interface RecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  fallbackValue?: any;
  onRetry?: (attempt: number, error: Error) => void;
  onFallback?: (error: Error) => void;
  shouldRetry?: (error: Error) => boolean;
  context?: string;
}

export interface RecoveryResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  attempts: number;
  usedFallback: boolean;
  recoveryTime: number;
}

export class ErrorRecoveryManager {

  /**
   * Execute a function with automatic retry and recovery
   */
  static async withRecovery<T>(
    operation: () => Promise<T>,
    options: RecoveryOptions = {}
  ): Promise<RecoveryResult<T>> {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      exponentialBackoff = true,
      fallbackValue,
      onRetry,
      onFallback,
      shouldRetry = this.defaultShouldRetry,
      context = 'unknown'
    } = options;

    const startTime = Date.now();
    let lastError: Error | null = null;
    let attempts = 0;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      attempts = attempt + 1;

      try {
        const result = await operation();
        const recoveryTime = Date.now() - startTime;

        // Log successful recovery if there were previous failures
        if (attempt > 0) {
          errorTracker.captureMessage(
            `Operation recovered after ${attempt} retries`,
            {
              tags: { context, attempts: attempt.toString() },
              extra: { recoveryTime }
            }
          );
        }

        return {
          success: true,
          data: result,
          attempts,
          usedFallback: false,
          recoveryTime
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Log the error with context
        errorTracker.captureException(lastError, {
          tags: {
            context,
            attempt: attempt.toString(),
            maxRetries: maxRetries.toString()
          },
          extra: { 
            shouldRetry: shouldRetry(lastError),
            willRetry: attempt < maxRetries && shouldRetry(lastError)
          }
        });

        // Check if we should retry
        if (attempt < maxRetries && shouldRetry(lastError)) {
          // Call retry callback
          if (onRetry) {
            onRetry(attempt + 1, lastError);
          }

          // Calculate delay with exponential backoff
          const delay = exponentialBackoff 
            ? retryDelay * Math.pow(2, attempt)
            : retryDelay;

          await this.sleep(delay);
          continue;
        }

        // No more retries, break the loop
        break;
      }
    }

    // All retries failed, try fallback
    const recoveryTime = Date.now() - startTime;

    if (fallbackValue !== undefined) {
      if (onFallback && lastError) {
        onFallback(lastError);
      }

      errorTracker.captureMessage(
        `Using fallback value after ${attempts} failed attempts`,
        {
          tags: { context, attempts: attempts.toString() },
          extra: { recoveryTime, fallbackUsed: true }
        }
      );

      return {
        success: true,
        data: fallbackValue,
        error: lastError || undefined,
        attempts,
        usedFallback: true,
        recoveryTime
      };
    }

    // No fallback available, return failure
    return {
      success: false,
      error: lastError || new Error('Unknown error'),
      attempts,
      usedFallback: false,
      recoveryTime
    };
  }

  /**
   * Execute multiple operations with recovery, returning partial results
   */
  static async withPartialRecovery<T>(
    operations: Array<() => Promise<T>>,
    options: RecoveryOptions = {}
  ): Promise<{
    results: Array<RecoveryResult<T>>;
    successCount: number;
    failureCount: number;
    totalTime: number;
  }> {
    const startTime = Date.now();
    const results: Array<RecoveryResult<T>> = [];

    // Execute all operations concurrently with individual recovery
    const promises = operations.map((operation, index) =>
      this.withRecovery(operation, {
        ...options,
        context: `${options.context || 'batch'}-${index}`
      })
    );

    const recoveryResults = await Promise.allSettled(promises);

    // Process results
    for (const result of recoveryResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        // Even the recovery failed
        results.push({
          success: false,
          error: result.reason instanceof Error ? result.reason : new Error(String(result.reason)),
          attempts: 1,
          usedFallback: false,
          recoveryTime: 0
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    const totalTime = Date.now() - startTime;

    // Log batch operation summary
    errorTracker.captureMessage(
      `Batch operation completed: ${successCount}/${results.length} successful`,
      {
        tags: {
          context: options.context || 'batch',
          successRate: ((successCount / results.length) * 100).toFixed(1)
        },
        extra: { successCount, failureCount, totalTime }
      }
    );

    return {
      results,
      successCount,
      failureCount,
      totalTime
    };
  }

  /**
   * Circuit breaker pattern for preventing cascading failures
   */
  static createCircuitBreaker<T>(
    operation: () => Promise<T>,
    options: {
      failureThreshold?: number;
      resetTimeout?: number;
      monitorWindow?: number;
      context?: string;
    } = {}
  ) {
    const {
      failureThreshold = 5,
      resetTimeout = 60000, // 1 minute
      monitorWindow = 300000, // 5 minutes
      context = 'circuit-breaker'
    } = options;

    let failures: number[] = [];
    let lastFailureTime = 0;
    let isOpen = false;

    return async (): Promise<T> => {
      const now = Date.now();

      // Clean old failures outside the monitor window
      failures = failures.filter(time => now - time < monitorWindow);

      // Check if circuit should be reset
      if (isOpen && now - lastFailureTime > resetTimeout) {
        isOpen = false;
        failures = [];
        errorTracker.captureMessage(
          'Circuit breaker reset - attempting operation',
          { tags: { context, state: 'half-open' } }
        );
      }

      // If circuit is open, fail fast
      if (isOpen) {
        throw new Error('Circuit breaker is open - operation temporarily unavailable');
      }

      try {
        const result = await operation();
        
        // Success - reset failure count if we had any
        if (failures.length > 0) {
          failures = [];
          errorTracker.captureMessage(
            'Circuit breaker operation successful after previous failures',
            { tags: { context, state: 'closed' } }
          );
        }

        return result;
      } catch (error) {
        failures.push(now);
        lastFailureTime = now;

        // Check if we should open the circuit
        if (failures.length >= failureThreshold) {
          isOpen = true;
          errorTracker.captureMessage(
            `Circuit breaker opened after ${failures.length} failures`,
            {
              tags: { context, state: 'open' },
              extra: { failureThreshold, resetTimeout }
            }
          );
        }

        throw error;
      }
    };
  }

  /**
   * Default retry logic
   */
  private static defaultShouldRetry(error: Error): boolean {
    // Don't retry on authentication errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized')) {
      return false;
    }

    // Don't retry on validation errors
    if (errorMessage.includes('Validation') || errorMessage.includes('Invalid')) {
      return false;
    }

    // Don't retry on not found errors
    if (errorMessage.includes('Not found') || errorMessage.includes('404')) {
      return false;
    }

    // Don't retry on rate limit errors (they should have their own backoff)
    if (errorMessage.includes('Rate limit') || errorMessage.includes('Too many requests')) {
      return false;
    }

    // Retry on network errors, timeouts, and server errors
    return true;
  }

  /**
   * Sleep utility for delays
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a resilient API call wrapper
   */
  static createResilientApiCall<T>(
    apiCall: () => Promise<T>,
    options: RecoveryOptions & {
      cacheKey?: string;
      cacheTtl?: number;
    } = {}
  ) {
    const { cacheKey, cacheTtl = 300000, ...recoveryOptions } = options; // 5 minute default cache
    let cache: { data: T; timestamp: number } | null = null;

    return async (): Promise<T> => {
      // Check cache first
      if (cache && cacheKey && Date.now() - cache.timestamp < cacheTtl) {
        return cache.data;
      }

      const result = await this.withRecovery(apiCall, {
        ...recoveryOptions,
        fallbackValue: cache?.data, // Use cached data as fallback
        context: `api-${options.context || 'call'}`
      });

      if (result.success && result.data !== undefined) {
        // Update cache on success
        if (cacheKey) {
          cache = { data: result.data, timestamp: Date.now() };
        }
        return result.data;
      }

      // If we have cached data and the call failed, use it
      if (cache?.data !== undefined) {
        errorTracker.captureMessage(
          'Using stale cached data due to API failure',
          {
            tags: { context: options.context || 'api-call', cacheAge: (Date.now() - cache.timestamp).toString() }
          }
        );
        return cache.data;
      }

      throw result.error || new Error('API call failed and no fallback available');
    };
  }
}

/**
 * React hook for error recovery in components
 */
export function useErrorRecovery() {
  const recover = async <T>(
    operation: () => Promise<T>,
    options?: RecoveryOptions
  ): Promise<RecoveryResult<T>> => {
    return ErrorRecoveryManager.withRecovery(operation, options);
  };

  const recoverWithFallback = async <T>(
    operation: () => Promise<T>,
    fallback: T,
    options?: Omit<RecoveryOptions, 'fallbackValue'>
  ): Promise<T> => {
    const result = await ErrorRecoveryManager.withRecovery(operation, {
      ...options,
      fallbackValue: fallback
    });
    return result.data as T;
  };

  return {
    recover,
    recoverWithFallback,
    createCircuitBreaker: ErrorRecoveryManager.createCircuitBreaker,
    createResilientApiCall: ErrorRecoveryManager.createResilientApiCall
  };
}
