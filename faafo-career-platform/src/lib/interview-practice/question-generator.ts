/**
 * Interview Practice Question Generator Module
 * Intelligent question generation based on session parameters
 */

import { QuestionBankService, type QuestionTemplate } from './question-bank';

export interface SessionParameters {
  sessionType: string;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  industryFocus?: string;
  specificRole?: string;
  interviewType?: string;
  preparationTime?: number;
  focusAreas?: string[];
  difficulty?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  totalQuestions: number;
}

export interface GeneratedQuestion extends QuestionTemplate {
  questionOrder: number;
  sessionId: string;
  adaptedForSession?: boolean;
  generationReason?: string;
}

export interface QuestionGenerationResult {
  success: boolean;
  questions: GeneratedQuestion[];
  metadata: {
    totalGenerated: number;
    distribution: Record<string, number>;
    adaptations: string[];
    warnings?: string[];
  };
  error?: string;
}

export class QuestionGeneratorService {
  /**
   * Generate questions for an interview session
   */
  static generateQuestionsForSession(
    sessionId: string,
    parameters: SessionParameters
  ): QuestionGenerationResult {
    try {
      const questions = this.selectQuestionsBasedOnParameters(sessionId, parameters);
      const distribution = this.calculateDistribution(questions);
      const adaptations = this.getAdaptationReasons(questions);

      return {
        success: true,
        questions,
        metadata: {
          totalGenerated: questions.length,
          distribution,
          adaptations,
        },
      };
    } catch (error) {
      return {
        success: false,
        questions: [],
        metadata: {
          totalGenerated: 0,
          distribution: {},
          adaptations: [],
        },
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Select questions based on session parameters
   */
  private static selectQuestionsBasedOnParameters(
    sessionId: string,
    parameters: SessionParameters
  ): GeneratedQuestion[] {
    const availableQuestions: QuestionTemplate[] = [];
    let questionOrder = 1;

    // Start with core behavioral questions (always include)
    const coreQuestions = QuestionBankService.getQuestionsByType('BEHAVIORAL').slice(0, 2);
    availableQuestions.push(...coreQuestions);

    // Add questions based on session type
    this.addQuestionsBySessionType(availableQuestions, parameters.sessionType, parameters.focusAreas || []);

    // Add questions based on experience level
    this.addQuestionsByExperienceLevel(availableQuestions, parameters.experienceLevel);

    // Add questions based on career path and role
    this.addQuestionsByCareerPath(availableQuestions, parameters.careerPath, parameters.specificRole);

    // Add questions based on interview type
    this.addQuestionsByInterviewType(availableQuestions, parameters.interviewType);

    // Add questions based on industry focus
    this.addQuestionsByIndustry(availableQuestions, parameters.industryFocus);

    // Remove duplicates
    const uniqueQuestions = this.removeDuplicates(availableQuestions);

    // Filter by difficulty if specified
    let filteredQuestions = uniqueQuestions;
    if (parameters.difficulty) {
      const difficultyQuestions = filteredQuestions.filter(q => q.difficulty === parameters.difficulty);
      if (difficultyQuestions.length >= parameters.totalQuestions) {
        filteredQuestions = difficultyQuestions;
      }
    }

    // Select final questions with balanced distribution
    const selectedQuestions = this.selectBalancedQuestions(filteredQuestions, parameters.totalQuestions);

    // Convert to GeneratedQuestion format
    return selectedQuestions.map(question => ({
      ...question,
      questionOrder: questionOrder++,
      sessionId,
      adaptedForSession: true,
      generationReason: this.getGenerationReason(question, parameters),
    }));
  }

  /**
   * Add questions based on session type
   */
  private static addQuestionsBySessionType(
    questions: QuestionTemplate[],
    sessionType: string,
    focusAreas: string[]
  ): void {
    switch (sessionType) {
      case 'TECHNICAL_INTERVIEW':
        questions.push(...QuestionBankService.getQuestionsByType('TECHNICAL'));
        questions.push(...QuestionBankService.getQuestionsByType('PROBLEM_SOLVING'));
        break;
      case 'BEHAVIORAL_PRACTICE':
        questions.push(...QuestionBankService.getQuestionsByType('BEHAVIORAL'));
        questions.push(...QuestionBankService.getQuestionsByType('SITUATIONAL'));
        break;
      case 'LEADERSHIP_ASSESSMENT':
        questions.push(...QuestionBankService.getQuestionsByType('LEADERSHIP'));
        questions.push(...QuestionBankService.getQuestionsByCategory('MANAGEMENT'));
        break;
      case 'CULTURE_FIT':
        questions.push(...QuestionBankService.getQuestionsByType('COMPANY_CULTURE'));
        questions.push(...QuestionBankService.getQuestionsByCategory('ETHICS'));
        break;
      default:
        // Mixed session - add variety
        questions.push(...QuestionBankService.getQuestionsByType('BEHAVIORAL').slice(0, 3));
        questions.push(...QuestionBankService.getQuestionsByType('TECHNICAL').slice(0, 2));
        questions.push(...QuestionBankService.getQuestionsByType('SITUATIONAL').slice(0, 2));
    }

    // Add questions based on focus areas
    focusAreas.forEach(area => {
      const areaQuestions = QuestionBankService.getQuestionsByTags([area.toLowerCase()]);
      questions.push(...areaQuestions);
    });
  }

  /**
   * Add questions based on experience level
   */
  private static addQuestionsByExperienceLevel(
    questions: QuestionTemplate[],
    experienceLevel?: string
  ): void {
    if (!experienceLevel) return;

    switch (experienceLevel.toLowerCase()) {
      case 'entry':
      case 'junior':
        questions.push(...QuestionBankService.getQuestionsByDifficulty('BEGINNER'));
        questions.push(...QuestionBankService.getQuestionsByDifficulty('INTERMEDIATE').slice(0, 2));
        break;
      case 'mid':
      case 'intermediate':
        questions.push(...QuestionBankService.getQuestionsByDifficulty('INTERMEDIATE'));
        questions.push(...QuestionBankService.getQuestionsByDifficulty('ADVANCED').slice(0, 2));
        break;
      case 'senior':
      case 'lead':
        questions.push(...QuestionBankService.getQuestionsByDifficulty('ADVANCED'));
        questions.push(...QuestionBankService.getQuestionsByType('LEADERSHIP'));
        break;
      case 'principal':
      case 'staff':
      case 'executive':
        questions.push(...QuestionBankService.getQuestionsByDifficulty('EXPERT'));
        questions.push(...QuestionBankService.getQuestionsByType('LEADERSHIP'));
        questions.push(...QuestionBankService.getQuestionsByCategory('STRATEGY'));
        break;
    }
  }

  /**
   * Add questions based on career path and role
   */
  private static addQuestionsByCareerPath(
    questions: QuestionTemplate[],
    careerPath?: string,
    specificRole?: string
  ): void {
    if (careerPath?.toLowerCase().includes('manager') || careerPath?.toLowerCase().includes('lead')) {
      questions.push(...QuestionBankService.getQuestionsByType('LEADERSHIP'));
      questions.push(...QuestionBankService.getQuestionsByCategory('MANAGEMENT'));
    }

    if (specificRole?.toLowerCase().includes('senior') || specificRole?.toLowerCase().includes('principal')) {
      questions.push(...QuestionBankService.getQuestionsByType('LEADERSHIP'));
      questions.push(...QuestionBankService.getQuestionsByType('PROBLEM_SOLVING'));
    }

    // Add role-specific questions based on common roles
    if (careerPath?.toLowerCase().includes('sales')) {
      questions.push(...QuestionBankService.getQuestionsByCategory('SALES'));
    }

    if (careerPath?.toLowerCase().includes('customer')) {
      questions.push(...QuestionBankService.getQuestionsByCategory('CUSTOMER_SERVICE'));
    }
  }

  /**
   * Add questions based on interview type
   */
  private static addQuestionsByInterviewType(
    questions: QuestionTemplate[],
    interviewType?: string
  ): void {
    if (!interviewType) return;

    switch (interviewType) {
      case 'PANEL':
      case 'GROUP':
        questions.push(...QuestionBankService.getQuestionsByCategory('COMMUNICATION'));
        questions.push(...QuestionBankService.getQuestionsByCategory('TEAMWORK'));
        break;
      case 'TECHNICAL_SCREEN':
        questions.push(...QuestionBankService.getQuestionsByType('TECHNICAL'));
        questions.push(...QuestionBankService.getQuestionsByType('PROBLEM_SOLVING'));
        break;
      case 'BEHAVIORAL':
        questions.push(...QuestionBankService.getQuestionsByType('BEHAVIORAL'));
        questions.push(...QuestionBankService.getQuestionsByType('SITUATIONAL'));
        break;
    }
  }

  /**
   * Add questions based on industry focus
   */
  private static addQuestionsByIndustry(
    questions: QuestionTemplate[],
    industryFocus?: string
  ): void {
    if (!industryFocus) return;

    // Add industry-specific questions based on common patterns
    if (industryFocus.toLowerCase().includes('tech')) {
      questions.push(...QuestionBankService.getQuestionsByType('TECHNICAL'));
    }

    if (industryFocus.toLowerCase().includes('finance')) {
      questions.push(...QuestionBankService.getQuestionsByCategory('ANALYTICAL_THINKING'));
    }

    if (industryFocus.toLowerCase().includes('healthcare')) {
      questions.push(...QuestionBankService.getQuestionsByCategory('ETHICS'));
    }
  }

  /**
   * Remove duplicate questions
   */
  private static removeDuplicates(questions: QuestionTemplate[]): QuestionTemplate[] {
    const seen = new Set<string>();
    return questions.filter(question => {
      if (seen.has(question.questionText)) {
        return false;
      }
      seen.add(question.questionText);
      return true;
    });
  }

  /**
   * Select balanced questions from available pool
   */
  private static selectBalancedQuestions(
    questions: QuestionTemplate[],
    totalCount: number
  ): QuestionTemplate[] {
    if (questions.length <= totalCount) {
      return questions;
    }

    // Ensure balanced distribution across types
    const typeDistribution = this.calculateOptimalDistribution(totalCount);
    const selectedQuestions: QuestionTemplate[] = [];

    // Select questions by type according to distribution
    for (const [type, count] of Object.entries(typeDistribution)) {
      const typeQuestions = questions.filter(q => q.questionType === type);
      const shuffled = typeQuestions.sort(() => Math.random() - 0.5);
      selectedQuestions.push(...shuffled.slice(0, count));
    }

    // Fill remaining slots with random questions
    const remaining = totalCount - selectedQuestions.length;
    if (remaining > 0) {
      const unusedQuestions = questions.filter(q => 
        !selectedQuestions.some(sq => sq.questionText === q.questionText)
      );
      const shuffled = unusedQuestions.sort(() => Math.random() - 0.5);
      selectedQuestions.push(...shuffled.slice(0, remaining));
    }

    return selectedQuestions.slice(0, totalCount);
  }

  /**
   * Calculate optimal distribution of question types
   */
  private static calculateOptimalDistribution(totalCount: number): Record<string, number> {
    const baseDistribution = {
      BEHAVIORAL: Math.ceil(totalCount * 0.4), // 40%
      TECHNICAL: Math.ceil(totalCount * 0.25), // 25%
      SITUATIONAL: Math.ceil(totalCount * 0.15), // 15%
      PROBLEM_SOLVING: Math.ceil(totalCount * 0.1), // 10%
      COMMUNICATION: Math.ceil(totalCount * 0.1), // 10%
    };

    // Adjust to match exact total
    const currentTotal = Object.values(baseDistribution).reduce((sum, count) => sum + count, 0);
    const diff = totalCount - currentTotal;

    if (diff !== 0) {
      baseDistribution.BEHAVIORAL += diff;
    }

    return baseDistribution;
  }

  /**
   * Calculate distribution of generated questions
   */
  private static calculateDistribution(questions: GeneratedQuestion[]): Record<string, number> {
    const distribution: Record<string, number> = {};
    
    questions.forEach(question => {
      distribution[question.questionType] = (distribution[question.questionType] || 0) + 1;
    });

    return distribution;
  }

  /**
   * Get adaptation reasons for questions
   */
  private static getAdaptationReasons(questions: GeneratedQuestion[]): string[] {
    return questions
      .filter(q => q.generationReason)
      .map(q => q.generationReason!)
      .filter((reason, index, array) => array.indexOf(reason) === index);
  }

  /**
   * Get generation reason for a question
   */
  private static getGenerationReason(question: QuestionTemplate, parameters: SessionParameters): string {
    const reasons: string[] = [];

    if (question.questionType === 'BEHAVIORAL') {
      reasons.push('Core behavioral assessment');
    }

    if (parameters.sessionType === 'TECHNICAL_INTERVIEW' && question.questionType === 'TECHNICAL') {
      reasons.push('Technical interview focus');
    }

    if (parameters.experienceLevel === 'senior' && question.difficulty === 'ADVANCED') {
      reasons.push('Senior-level complexity');
    }

    if (parameters.focusAreas?.some(area => question.tags?.includes(area.toLowerCase()))) {
      reasons.push('Matches focus areas');
    }

    return reasons.length > 0 ? reasons.join(', ') : 'Standard question selection';
  }
}
