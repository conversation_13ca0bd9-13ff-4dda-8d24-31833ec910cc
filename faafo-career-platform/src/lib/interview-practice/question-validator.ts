/**
 * Interview Practice Question Validator Module
 * Validation schemas and logic for question-related operations
 */

import { z } from 'zod';

// Enums for validation
export const QuestionTypeEnum = z.enum([
  'BEHAVIORAL',
  'TECHNICAL', 
  'SITUATIONAL',
  'COMPANY_CULTURE',
  'LEADERSHIP',
  'PROBLEM_SOLVING',
  'COMMUNICATION',
  'STRESS_TEST',
  'CASE_STUDY',
  'ROLE_SPECIFIC'
]);

export const CategoryEnum = z.enum([
  'GENERAL',
  'TECHNICAL_SKILLS',
  'SOFT_SKILLS',
  'LEADERSHIP',
  'PROBLEM_SOLVING',
  'COMMUNICATION',
  'TEAMWORK',
  'ADAPTABILITY',
  'CREATIVITY',
  'ANALYTICAL_THINKING',
  'CUSTOMER_SERVICE',
  'SALES',
  'MANAGEMENT',
  'STRATEGY',
  'ETHICS',
  'INDUSTRY_KNOWLEDGE'
]);

export const DifficultyEnum = z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']);

// Validation schema for generating questions
export const generateQuestionsSchema = z.object({
  count: z.number().min(1).max(20).default(10),
  questionTypes: z.array(QuestionTypeEnum).optional(),
  categories: z.array(CategoryEnum).optional(),
  difficulty: DifficultyEnum.optional(),
});

// Validation schema for question creation
export const createQuestionSchema = z.object({
  questionText: z.string().min(10, 'Question must be at least 10 characters').max(1000, 'Question too long'),
  questionType: QuestionTypeEnum,
  category: CategoryEnum,
  difficulty: DifficultyEnum,
  expectedDuration: z.number().min(30).max(1800), // 30 seconds to 30 minutes
  context: z.string().max(500).optional(),
  hints: z.record(z.any()).optional(),
  tags: z.array(z.string()).optional(),
});

// Validation schema for question update
export const updateQuestionSchema = createQuestionSchema.partial();

// Validation schema for session parameters
export const sessionParametersSchema = z.object({
  sessionType: z.string().min(1),
  careerPath: z.string().optional(),
  experienceLevel: z.string().optional(),
  companyType: z.string().optional(),
  industryFocus: z.string().optional(),
  specificRole: z.string().optional(),
  interviewType: z.string().optional(),
  preparationTime: z.number().min(0).optional(),
  focusAreas: z.array(z.string()).optional(),
  difficulty: DifficultyEnum.optional(),
  totalQuestions: z.number().min(1).max(50),
});

// Validation schema for question filters
export const questionFiltersSchema = z.object({
  types: z.array(QuestionTypeEnum).optional(),
  categories: z.array(CategoryEnum).optional(),
  difficulty: DifficultyEnum.optional(),
  tags: z.array(z.string()).optional(),
  searchTerm: z.string().optional(),
});

export interface ValidationResult<T = any> {
  isValid: boolean;
  data?: T;
  errors?: string[];
  sanitizedData?: T;
}

export class QuestionValidatorService {
  /**
   * Validate question generation request
   */
  static validateGenerateQuestionsRequest(data: any): ValidationResult {
    try {
      const result = generateQuestionsSchema.safeParse(data);
      
      if (!result.success) {
        return {
          isValid: false,
          errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
        };
      }

      return {
        isValid: true,
        data: result.data,
        sanitizedData: result.data,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Invalid request format'],
      };
    }
  }

  /**
   * Validate question creation data
   */
  static validateQuestionCreation(data: any): ValidationResult {
    try {
      const result = createQuestionSchema.safeParse(data);
      
      if (!result.success) {
        return {
          isValid: false,
          errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
        };
      }

      // Additional business logic validation
      const businessValidation = this.validateQuestionBusinessRules(result.data);
      if (!businessValidation.isValid) {
        return businessValidation;
      }

      return {
        isValid: true,
        data: result.data,
        sanitizedData: this.sanitizeQuestionData(result.data),
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Invalid question data format'],
      };
    }
  }

  /**
   * Validate question update data
   */
  static validateQuestionUpdate(data: any): ValidationResult {
    try {
      const result = updateQuestionSchema.safeParse(data);
      
      if (!result.success) {
        return {
          isValid: false,
          errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
        };
      }

      return {
        isValid: true,
        data: result.data,
        sanitizedData: this.sanitizeQuestionData(result.data),
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Invalid update data format'],
      };
    }
  }

  /**
   * Validate session parameters
   */
  static validateSessionParameters(data: any): ValidationResult {
    try {
      const result = sessionParametersSchema.safeParse(data);
      
      if (!result.success) {
        return {
          isValid: false,
          errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
        };
      }

      // Additional validation for session parameters
      const sessionValidation = this.validateSessionBusinessRules(result.data);
      if (!sessionValidation.isValid) {
        return sessionValidation;
      }

      return {
        isValid: true,
        data: result.data,
        sanitizedData: result.data,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Invalid session parameters format'],
      };
    }
  }

  /**
   * Validate question filters
   */
  static validateQuestionFilters(data: any): ValidationResult {
    try {
      const result = questionFiltersSchema.safeParse(data);
      
      if (!result.success) {
        return {
          isValid: false,
          errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
        };
      }

      return {
        isValid: true,
        data: result.data,
        sanitizedData: result.data,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: ['Invalid filter format'],
      };
    }
  }

  /**
   * Validate question business rules
   */
  private static validateQuestionBusinessRules(data: any): ValidationResult {
    const errors: string[] = [];

    // Check if question type matches category appropriately
    if (data.questionType === 'TECHNICAL' && !this.isTechnicalCategory(data.category)) {
      errors.push('Technical questions should use technical categories');
    }

    // Check if difficulty matches expected duration
    if (data.difficulty === 'BEGINNER' && data.expectedDuration > 300) {
      errors.push('Beginner questions should typically be under 5 minutes');
    }

    if (data.difficulty === 'EXPERT' && data.expectedDuration < 180) {
      errors.push('Expert questions should typically be at least 3 minutes');
    }

    // Validate question text quality
    if (data.questionText && !this.isValidQuestionText(data.questionText)) {
      errors.push('Question text should be a proper question');
    }

    if (errors.length > 0) {
      return {
        isValid: false,
        errors,
      };
    }

    return { isValid: true };
  }

  /**
   * Validate session business rules
   */
  private static validateSessionBusinessRules(data: any): ValidationResult {
    const errors: string[] = [];

    // Check if total questions is reasonable for session type
    if (data.sessionType === 'QUICK_PRACTICE' && data.totalQuestions > 5) {
      errors.push('Quick practice sessions should have 5 or fewer questions');
    }

    if (data.sessionType === 'COMPREHENSIVE' && data.totalQuestions < 10) {
      errors.push('Comprehensive sessions should have at least 10 questions');
    }

    // Check if difficulty matches experience level
    if (data.experienceLevel === 'entry' && data.difficulty === 'EXPERT') {
      errors.push('Entry-level candidates should not have expert-level questions');
    }

    // Validate focus areas
    if (data.focusAreas && data.focusAreas.length > 5) {
      errors.push('Too many focus areas selected (maximum 5)');
    }

    if (errors.length > 0) {
      return {
        isValid: false,
        errors,
      };
    }

    return { isValid: true };
  }

  /**
   * Check if category is technical
   */
  private static isTechnicalCategory(category: string): boolean {
    const technicalCategories = [
      'TECHNICAL_SKILLS',
      'PROBLEM_SOLVING',
      'ANALYTICAL_THINKING'
    ];
    return technicalCategories.includes(category);
  }

  /**
   * Validate question text format
   */
  private static isValidQuestionText(text: string): boolean {
    // Check if it's a proper question (ends with ? or contains question words)
    const questionWords = ['what', 'how', 'why', 'when', 'where', 'who', 'which', 'describe', 'explain', 'tell'];
    const lowerText = text.toLowerCase();
    
    return text.endsWith('?') || questionWords.some(word => lowerText.includes(word));
  }

  /**
   * Sanitize question data
   */
  private static sanitizeQuestionData(data: any): any {
    return {
      ...data,
      questionText: data.questionText?.trim(),
      context: data.context?.trim(),
      tags: data.tags?.map((tag: string) => tag.trim().toLowerCase()),
    };
  }

  /**
   * Validate UUID format
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Validate session ID
   */
  static validateSessionId(sessionId: string): ValidationResult {
    if (!sessionId) {
      return {
        isValid: false,
        errors: ['Session ID is required'],
      };
    }

    if (!this.isValidUUID(sessionId)) {
      return {
        isValid: false,
        errors: ['Invalid session ID format'],
      };
    }

    return {
      isValid: true,
      data: sessionId,
    };
  }

  /**
   * Validate question ID
   */
  static validateQuestionId(questionId: string): ValidationResult {
    if (!questionId) {
      return {
        isValid: false,
        errors: ['Question ID is required'],
      };
    }

    if (!this.isValidUUID(questionId)) {
      return {
        isValid: false,
        errors: ['Invalid question ID format'],
      };
    }

    return {
      isValid: true,
      data: questionId,
    };
  }

  /**
   * Get available validation options
   */
  static getValidationOptions() {
    return {
      questionTypes: QuestionTypeEnum.options,
      categories: CategoryEnum.options,
      difficulties: DifficultyEnum.options,
    };
  }
}
