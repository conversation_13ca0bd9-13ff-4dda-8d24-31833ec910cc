/**
 * Refactored Interview Practice Questions <PERSON> Handler
 * Orchestrates all question-related modules for clean API handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';

import { QuestionGeneratorService, type SessionParameters } from './question-generator';
import { QuestionValidatorService } from './question-validator';
import { QuestionService } from './question-service';
import { SelfHealingAIService } from '@/lib/self-healing-ai-service';

export interface QuestionRouteContext {
  sessionId: string;
  userId: string;
  session: any;
}

export class RefactoredQuestionsRouteHandler {
  /**
   * Handle GET request - Retrieve questions for a session
   */
  static async handleGetQuestions(
    request: NextRequest,
    sessionId: string
  ): Promise<NextResponse> {
    return withRateLimit(
      request,
      {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ? 200 : 50,
      },
      async () => {
        // Validate session and get user context
        const context = await this.validateAndGetContext(request, sessionId);
        if (!context.success) {
          const error = new Error(context.error);
          (error as any).statusCode = context.statusCode || 401;
          throw error;
        }

        const { userId } = context.data!;

        // Get questions for the session
        const questionsResult = await QuestionService.getQuestionsForSession(sessionId, userId);
        
        if (!questionsResult.success) {
          const error = new Error(questionsResult.error);
          (error as any).statusCode = 500;
          throw error;
        }

        return NextResponse.json({
          success: true,
          data: questionsResult.questions,
        });
      }
    );
  }

  /**
   * Handle POST request - Generate questions for a session
   */
  static async handleGenerateQuestions(
    request: NextRequest,
    sessionId: string
  ): Promise<NextResponse> {
    return withCSRFProtection(request, async () => {
      return withRateLimit(
        request,
        {
          windowMs: 15 * 60 * 1000,
          maxRequests: process.env.NODE_ENV === 'development' ? 25 : 5,
        },
        async () => {
          // Validate session and get user context
          const context = await this.validateAndGetContext(request, sessionId);
          if (!context.success) {
            const error = new Error(context.error);
            (error as any).statusCode = context.statusCode || 401;
            throw error;
          }

          const { userId } = context.data!;

          // Parse and validate request body
          const body = await request.json();
          const validationResult = QuestionValidatorService.validateGenerateQuestionsRequest(body);
          
          if (!validationResult.isValid) {
            const error = new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
            (error as any).statusCode = 400;
            throw error;
          }

          // Get session details for question generation
          const sessionDetails = await this.getSessionDetails(sessionId);
          if (!sessionDetails.success) {
            const error = new Error(sessionDetails.error);
            (error as any).statusCode = 404;
            throw error;
          }

          // Check if session already has questions
          const hasQuestions = await QuestionService.sessionHasQuestions(sessionId);
          if (hasQuestions) {
            const error = new Error('Session already has questions. Delete existing questions first.');
            (error as any).statusCode = 409;
            throw error;
          }

          // Prepare session parameters for question generation
          const sessionParams: SessionParameters = {
            sessionType: sessionDetails.data.sessionType,
            careerPath: sessionDetails.data.careerPath,
            experienceLevel: sessionDetails.data.experienceLevel,
            companyType: sessionDetails.data.companyType,
            industryFocus: sessionDetails.data.industryFocus,
            specificRole: sessionDetails.data.specificRole,
            interviewType: sessionDetails.data.interviewType,
            preparationTime: sessionDetails.data.preparationTime,
            focusAreas: sessionDetails.data.focusAreas || [],
            difficulty: validationResult.sanitizedData?.difficulty || sessionDetails.data.difficulty,
            totalQuestions: validationResult.sanitizedData?.count || 10,
          };

          // Generate questions using the generator service
          const generationResult = QuestionGeneratorService.generateQuestionsForSession(
            sessionId,
            sessionParams
          );

          if (!generationResult.success) {
            const error = new Error(generationResult.error);
            (error as any).statusCode = 500;
            throw error;
          }

          // Enhance questions with AI if available
          let enhancedQuestions = generationResult.questions;
          try {
            const aiEnhancementResult = await this.enhanceQuestionsWithAI(
              enhancedQuestions,
              sessionParams,
              userId
            );
            
            if (aiEnhancementResult.success) {
              enhancedQuestions = aiEnhancementResult.questions;
            }
          } catch (aiError) {
            // AI enhancement is optional, continue with original questions
            console.warn('AI enhancement failed, using original questions:', aiError);
          }

          // Save questions to database
          const saveResult = await QuestionService.createQuestionsForSession(
            sessionId,
            enhancedQuestions
          );

          if (!saveResult.success) {
            const error = new Error(saveResult.error);
            (error as any).statusCode = 500;
            throw error;
          }

          return NextResponse.json({
            success: true,
            data: {
              questions: saveResult.questions,
              metadata: generationResult.metadata,
            },
            message: `Generated ${saveResult.questions?.length} interview questions successfully`,
          });
        }
      );
    });
  }

  /**
   * Validate session and get user context
   */
  private static async validateAndGetContext(
    request: NextRequest,
    sessionId: string
  ): Promise<{ success: boolean; data?: { userId: string; session: any }; error?: string; statusCode?: number }> {
    // Validate session ID format
    const sessionIdValidation = QuestionValidatorService.validateSessionId(sessionId);
    if (!sessionIdValidation.isValid) {
      return {
        success: false,
        error: sessionIdValidation.errors?.join(', '),
        statusCode: 400,
      };
    }

    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return {
        success: false,
        error: 'Authentication required',
        statusCode: 401,
      };
    }

    // Validate session access (simplified validation for now)
    const sessionValidation = {
      isValid: true,
      error: null,
      statusCode: null
    };

    if (!sessionValidation.isValid) {
      return {
        success: false,
        error: sessionValidation.error || undefined,
        statusCode: sessionValidation.statusCode || 403,
      };
    }

    return {
      success: true,
      data: {
        userId: session.user.id,
        session,
      },
    };
  }

  /**
   * Get session details for question generation
   */
  private static async getSessionDetails(sessionId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const { prisma } = await import('@/lib/prisma');
      
      const session = await prisma.interviewSession.findUnique({
        where: { id: sessionId },
        select: {
          id: true,
          sessionType: true,
          careerPath: true,
          experienceLevel: true,
          companyType: true,
          industryFocus: true,
          specificRole: true,
          interviewType: true,
          preparationTime: true,
          focusAreas: true,
          difficulty: true,
          totalQuestions: true,
        },
      });

      if (!session) {
        return {
          success: false,
          error: 'Session not found',
        };
      }

      return {
        success: true,
        data: session,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch session details',
      };
    }
  }

  /**
   * Enhance questions with AI
   */
  private static async enhanceQuestionsWithAI(
    questions: any[],
    sessionParams: SessionParameters,
    userId: string
  ): Promise<{ success: boolean; questions: any[]; error?: string }> {
    try {
      // Use AI service to enhance questions if available
      const aiService = new SelfHealingAIService();
      
      const enhancementPrompt = this.buildEnhancementPrompt(questions, sessionParams);
      const aiResult = await aiService.generateContent(enhancementPrompt, userId);

      if (!aiResult.success) {
        return {
          success: false,
          questions,
          error: aiResult.error,
        };
      }

      // Parse AI response and merge with original questions
      const enhancedQuestions = this.parseAIEnhancement(aiResult.data, questions);

      return {
        success: true,
        questions: enhancedQuestions,
      };
    } catch (error) {
      return {
        success: false,
        questions,
        error: error instanceof Error ? error.message : 'AI enhancement failed',
      };
    }
  }

  /**
   * Build AI enhancement prompt
   */
  private static buildEnhancementPrompt(questions: any[], sessionParams: SessionParameters): string {
    return `
Enhance the following interview questions for a ${sessionParams.experienceLevel} ${sessionParams.careerPath} interview:

Session Context:
- Interview Type: ${sessionParams.interviewType}
- Company Type: ${sessionParams.companyType}
- Industry: ${sessionParams.industryFocus}
- Focus Areas: ${sessionParams.focusAreas?.join(', ')}

Questions to enhance:
${questions.map((q, i) => `${i + 1}. ${q.questionText}`).join('\n')}

Please provide enhanced versions that:
1. Are more specific to the role and industry
2. Include better context and hints
3. Have appropriate follow-up questions
4. Match the experience level

Return as JSON with enhanced question details.
    `.trim();
  }

  /**
   * Parse AI enhancement response
   */
  private static parseAIEnhancement(aiResponse: string, originalQuestions: any[]): any[] {
    try {
      // Simple fallback - return original questions if AI parsing fails
      const parsed = JSON.parse(aiResponse);
      
      // Merge AI enhancements with original questions
      return originalQuestions.map((question, index) => {
        const enhancement = parsed.questions?.[index];
        if (enhancement) {
          return {
            ...question,
            questionText: enhancement.questionText || question.questionText,
            context: enhancement.context || question.context,
            hints: enhancement.hints || question.hints,
          };
        }
        return question;
      });
    } catch {
      // Return original questions if parsing fails
      return originalQuestions;
    }
  }
}

// Export wrapped route handlers
export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  const { sessionId } = await params;
  return RefactoredQuestionsRouteHandler.handleGetQuestions(request, sessionId);
});

export const POST = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  const { sessionId } = await params;
  return RefactoredQuestionsRouteHandler.handleGenerateQuestions(request, sessionId);
});
