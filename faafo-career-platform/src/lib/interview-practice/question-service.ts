/**
 * Interview Practice Question Service Module
 * Database operations and business logic for questions
 */

import { prisma } from '@/lib/prisma';
import type { GeneratedQuestion } from './question-generator';

export interface QuestionCreateData {
  sessionId: string;
  questionText: string;
  questionType: string;
  category: string;
  difficulty: string;
  expectedDuration: number;
  context?: string;
  hints?: any;
  questionOrder: number;
}

export interface QuestionUpdateData {
  questionText?: string;
  questionType?: string;
  category?: string;
  difficulty?: string;
  expectedDuration?: number;
  context?: string | null;
  hints?: any;
  questionOrder?: number;
}

export interface QuestionFilters {
  sessionId?: string;
  userId?: string;
  questionTypes?: string[];
  categories?: string[];
  difficulty?: string;
  searchTerm?: string;
}

export interface QuestionWithResponses {
  id: string;
  questionText: string;
  questionType: string;
  category: string;
  difficulty: string;
  expectedDuration: number;
  context?: string | null;
  hints?: any;
  questionOrder: number;
  sessionId: string;
  createdAt: Date;
  updatedAt?: Date;
  tags?: any;
  responses?: any[];
}

export class QuestionService {
  /**
   * Create multiple questions for a session
   */
  static async createQuestionsForSession(
    sessionId: string,
    questions: GeneratedQuestion[]
  ): Promise<{ success: boolean; questions?: any[]; error?: string }> {
    try {
      // Prepare question data for database
      const questionsData = questions.map(question => ({
        sessionId,
        questionText: question.questionText,
        questionType: question.questionType,
        category: question.category,
        difficulty: question.difficulty,
        expectedDuration: question.expectedDuration,
        context: question.context || null,
        hints: question.hints || null,
        questionOrder: question.questionOrder,
      }));

      // Create questions in database
      await prisma.interviewQuestion.createMany({
        data: questionsData,
      });

      // Update session with total questions count
      await prisma.interviewSession.update({
        where: { id: sessionId },
        data: {
          totalQuestions: questionsData.length,
          lastActiveAt: new Date(),
        },
      });

      // Fetch the created questions with full details
      const createdQuestions = await prisma.interviewQuestion.findMany({
        where: { sessionId },
        orderBy: { questionOrder: 'asc' },
      });

      return {
        success: true,
        questions: createdQuestions,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create questions',
      };
    }
  }

  /**
   * Get questions for a session with user responses
   */
  static async getQuestionsForSession(
    sessionId: string,
    userId: string
  ): Promise<{ success: boolean; questions?: QuestionWithResponses[]; error?: string }> {
    try {
      const questions = await prisma.interviewQuestion.findMany({
        where: { sessionId },
        include: {
          responses: {
            where: { userId },
            select: {
              id: true,
              responseText: true,
              audioUrl: true,
              responseTime: true,
              preparationTime: true,
              aiScore: true,
              isCompleted: true,
              userNotes: true,
              createdAt: true,
            },
          },
        },
        orderBy: { questionOrder: 'asc' },
      });

      return {
        success: true,
        questions,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch questions',
      };
    }
  }

  /**
   * Get a single question by ID
   */
  static async getQuestionById(
    questionId: string,
    userId?: string
  ): Promise<{ success: boolean; question?: QuestionWithResponses; error?: string }> {
    try {
      const question = await prisma.interviewQuestion.findUnique({
        where: { id: questionId },
        include: userId ? {
          responses: {
            where: { userId },
            select: {
              id: true,
              responseText: true,
              audioUrl: true,
              responseTime: true,
              preparationTime: true,
              aiScore: true,
              isCompleted: true,
              userNotes: true,
              createdAt: true,
            },
          },
        } : undefined,
      });

      if (!question) {
        return {
          success: false,
          error: 'Question not found',
        };
      }

      return {
        success: true,
        question,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch question',
      };
    }
  }

  /**
   * Update a question
   */
  static async updateQuestion(
    questionId: string,
    updateData: QuestionUpdateData
  ): Promise<{ success: boolean; question?: any; error?: string }> {
    try {
      const question = await prisma.interviewQuestion.update({
        where: { id: questionId },
        data: {
          ...(updateData as any),
          updatedAt: new Date(),
        },
      });

      return {
        success: true,
        question,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update question',
      };
    }
  }

  /**
   * Delete a question
   */
  static async deleteQuestion(
    questionId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await prisma.interviewQuestion.delete({
        where: { id: questionId },
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete question',
      };
    }
  }

  /**
   * Delete all questions for a session
   */
  static async deleteQuestionsForSession(
    sessionId: string
  ): Promise<{ success: boolean; deletedCount?: number; error?: string }> {
    try {
      const result = await prisma.interviewQuestion.deleteMany({
        where: { sessionId },
      });

      return {
        success: true,
        deletedCount: result.count,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete questions',
      };
    }
  }

  /**
   * Get questions with filters
   */
  static async getQuestionsWithFilters(
    filters: QuestionFilters,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ success: boolean; questions?: QuestionWithResponses[]; total?: number; error?: string }> {
    try {
      const whereClause: any = {};

      if (filters.sessionId) {
        whereClause.sessionId = filters.sessionId;
      }

      if (filters.questionTypes && filters.questionTypes.length > 0) {
        whereClause.questionType = { in: filters.questionTypes };
      }

      if (filters.categories && filters.categories.length > 0) {
        whereClause.category = { in: filters.categories };
      }

      if (filters.difficulty) {
        whereClause.difficulty = filters.difficulty;
      }

      if (filters.searchTerm) {
        whereClause.OR = [
          { questionText: { contains: filters.searchTerm, mode: 'insensitive' } },
          { context: { contains: filters.searchTerm, mode: 'insensitive' } },
        ];
      }

      const [questions, total] = await Promise.all([
        prisma.interviewQuestion.findMany({
          where: whereClause,
          include: filters.userId ? {
            responses: {
              where: { userId: filters.userId },
              select: {
                id: true,
                responseText: true,
                audioUrl: true,
                responseTime: true,
                preparationTime: true,
                aiScore: true,
                isCompleted: true,
                userNotes: true,
                createdAt: true,
              },
            },
          } : undefined,
          orderBy: { questionOrder: 'asc' },
          take: limit,
          skip: offset,
        }),
        prisma.interviewQuestion.count({ where: whereClause }),
      ]);

      return {
        success: true,
        questions,
        total,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch questions',
      };
    }
  }

  /**
   * Get question statistics for a session
   */
  static async getQuestionStatistics(
    sessionId: string,
    userId?: string
  ): Promise<{ success: boolean; stats?: any; error?: string }> {
    try {
      const questions = await prisma.interviewQuestion.findMany({
        where: { sessionId },
        include: userId ? {
          responses: {
            where: { userId },
            select: {
              isCompleted: true,
              aiScore: true,
              responseTime: true,
            },
          },
        } : {
          responses: {
            select: {
              isCompleted: true,
              aiScore: true,
              responseTime: true,
            },
          },
        },
      });

      const stats = {
        totalQuestions: questions.length,
        completedQuestions: 0,
        averageScore: 0,
        averageResponseTime: 0,
        difficultyDistribution: {} as Record<string, number>,
        typeDistribution: {} as Record<string, number>,
      };

      let totalScore = 0;
      let totalResponseTime = 0;
      let scoredQuestions = 0;
      let timedQuestions = 0;

      questions.forEach(question => {
        // Count difficulty distribution
        stats.difficultyDistribution[question.difficulty] = 
          (stats.difficultyDistribution[question.difficulty] || 0) + 1;

        // Count type distribution
        stats.typeDistribution[question.questionType] = 
          (stats.typeDistribution[question.questionType] || 0) + 1;

        // Process responses
        question.responses.forEach(response => {
          if (response.isCompleted) {
            stats.completedQuestions++;
          }

          if (response.aiScore !== null) {
            totalScore += response.aiScore;
            scoredQuestions++;
          }

          if (response.responseTime) {
            totalResponseTime += response.responseTime;
            timedQuestions++;
          }
        });
      });

      stats.averageScore = scoredQuestions > 0 ? totalScore / scoredQuestions : 0;
      stats.averageResponseTime = timedQuestions > 0 ? totalResponseTime / timedQuestions : 0;

      return {
        success: true,
        stats,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get statistics',
      };
    }
  }

  /**
   * Check if session has questions
   */
  static async sessionHasQuestions(sessionId: string): Promise<boolean> {
    try {
      const count = await prisma.interviewQuestion.count({
        where: { sessionId },
      });
      return count > 0;
    } catch {
      return false;
    }
  }

  /**
   * Get next question order for session
   */
  static async getNextQuestionOrder(sessionId: string): Promise<number> {
    try {
      const lastQuestion = await prisma.interviewQuestion.findFirst({
        where: { sessionId },
        orderBy: { questionOrder: 'desc' },
        select: { questionOrder: true },
      });

      return lastQuestion ? lastQuestion.questionOrder + 1 : 1;
    } catch {
      return 1;
    }
  }
}
