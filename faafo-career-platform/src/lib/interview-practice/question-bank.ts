/**
 * Interview Practice Question Bank Module
 * Static question definitions organized by category and type
 */

export interface QuestionTemplate {
  questionText: string;
  questionType: 'BEHAVIORAL' | 'TECHNICAL' | 'SITUATIONAL' | 'COMPANY_CULTURE' | 'LEADERSHIP' | 'PROBLEM_SOLVING' | 'COMMUNICATION' | 'STRESS_TEST' | 'CASE_STUDY' | 'ROLE_SPECIFIC';
  category: 'GENERAL' | 'TECHNICAL_SKILLS' | 'SOFT_SKILLS' | 'LEADERSHIP' | 'PROBLEM_SOLVING' | 'COMMUNICATION' | 'TEAMWORK' | 'ADAPTABILITY' | 'CREATIVITY' | 'ANALYTICAL_THINKING' | 'CUSTOMER_SERVICE' | 'SALES' | 'MANAGEMENT' | 'STRATEGY' | 'ETHICS' | 'INDUSTRY_KNOWLEDGE';
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  expectedDuration: number; // in seconds
  context?: string;
  hints?: any;
  tags?: string[];
}

export const QUESTION_BANKS = {
  BEHAVIORAL: [
    {
      questionText: "Tell me about a time when you had to work with a difficult team member. How did you handle the situation?",
      questionType: "BEHAVIORAL" as const,
      category: "TEAMWORK" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 180,
      context: "This question assesses conflict resolution and interpersonal skills.",
      hints: { starMethod: true, focusOnResolution: true },
      tags: ["teamwork", "conflict-resolution", "communication"]
    },
    {
      questionText: "Describe a situation where you had to meet a tight deadline. What steps did you take?",
      questionType: "BEHAVIORAL" as const,
      category: "PROBLEM_SOLVING" as const,
      difficulty: "BEGINNER" as const,
      expectedDuration: 150,
      context: "Evaluates time management and pressure handling abilities.",
      hints: { starMethod: true, focusOnProcess: true },
      tags: ["time-management", "pressure", "planning"]
    },
    {
      questionText: "Give me an example of a time when you had to learn something new quickly. How did you approach it?",
      questionType: "BEHAVIORAL" as const,
      category: "ADAPTABILITY" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 180,
      context: "Tests learning agility and adaptability.",
      hints: { starMethod: true, focusOnLearningProcess: true },
      tags: ["learning", "adaptability", "growth-mindset"]
    },
    {
      questionText: "Tell me about a time when you made a mistake. How did you handle it?",
      questionType: "BEHAVIORAL" as const,
      category: "ETHICS" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 180,
      context: "Assesses accountability and problem-solving under pressure.",
      hints: { starMethod: true, focusOnLearning: true },
      tags: ["accountability", "learning", "integrity"]
    },
    {
      questionText: "Describe a situation where you had to persuade someone to see your point of view.",
      questionType: "BEHAVIORAL" as const,
      category: "COMMUNICATION" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 200,
      context: "Evaluates persuasion and communication skills.",
      hints: { starMethod: true, focusOnStrategy: true },
      tags: ["persuasion", "communication", "influence"]
    }
  ],

  TECHNICAL: [
    {
      questionText: "Explain the difference between synchronous and asynchronous programming. When would you use each?",
      questionType: "TECHNICAL" as const,
      category: "TECHNICAL_SKILLS" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 240,
      context: "Tests understanding of fundamental programming concepts.",
      hints: { provideExamples: true, discussTradeoffs: true },
      tags: ["programming", "async", "performance"]
    },
    {
      questionText: "How would you optimize a slow database query?",
      questionType: "TECHNICAL" as const,
      category: "TECHNICAL_SKILLS" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 300,
      context: "Assesses database optimization knowledge.",
      hints: { discussIndexing: true, explainQueryPlans: true },
      tags: ["database", "optimization", "performance"]
    },
    {
      questionText: "What is the difference between REST and GraphQL APIs?",
      questionType: "TECHNICAL" as const,
      category: "TECHNICAL_SKILLS" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 180,
      context: "Tests API design knowledge.",
      hints: { compareUseCases: true, discussTradeoffs: true },
      tags: ["api", "rest", "graphql"]
    },
    {
      questionText: "Explain how you would implement caching in a web application.",
      questionType: "TECHNICAL" as const,
      category: "TECHNICAL_SKILLS" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 300,
      context: "Evaluates caching strategies and performance optimization.",
      hints: { discussLayers: true, mentionStrategies: true },
      tags: ["caching", "performance", "architecture"]
    }
  ],

  LEADERSHIP: [
    {
      questionText: "Describe a time when you had to lead a team through a challenging project.",
      questionType: "LEADERSHIP" as const,
      category: "LEADERSHIP" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 240,
      context: "Assesses leadership and project management skills.",
      hints: { starMethod: true, focusOnTeamDynamics: true },
      tags: ["leadership", "project-management", "team-building"]
    },
    {
      questionText: "How do you motivate team members who are struggling with their performance?",
      questionType: "LEADERSHIP" as const,
      category: "MANAGEMENT" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 200,
      context: "Tests people management and coaching abilities.",
      hints: { discussApproaches: true, mentionEmpathy: true },
      tags: ["coaching", "motivation", "performance-management"]
    },
    {
      questionText: "Tell me about a time when you had to make a difficult decision that affected your team.",
      questionType: "LEADERSHIP" as const,
      category: "LEADERSHIP" as const,
      difficulty: "EXPERT" as const,
      expectedDuration: 300,
      context: "Evaluates decision-making and leadership under pressure.",
      hints: { starMethod: true, focusOnDecisionProcess: true },
      tags: ["decision-making", "leadership", "team-impact"]
    }
  ],

  PROBLEM_SOLVING: [
    {
      questionText: "Walk me through how you would approach solving a problem you've never encountered before.",
      questionType: "PROBLEM_SOLVING" as const,
      category: "PROBLEM_SOLVING" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 180,
      context: "Tests problem-solving methodology and analytical thinking.",
      hints: { explainProcess: true, mentionResources: true },
      tags: ["problem-solving", "methodology", "analytical-thinking"]
    },
    {
      questionText: "Describe a complex problem you solved recently. What was your approach?",
      questionType: "PROBLEM_SOLVING" as const,
      category: "ANALYTICAL_THINKING" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 240,
      context: "Assesses analytical skills and problem-solving depth.",
      hints: { starMethod: true, focusOnAnalysis: true },
      tags: ["analysis", "complexity", "solution-design"]
    }
  ],

  COMMUNICATION: [
    {
      questionText: "How do you explain technical concepts to non-technical stakeholders?",
      questionType: "COMMUNICATION" as const,
      category: "COMMUNICATION" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 150,
      context: "Tests ability to communicate across different audiences.",
      hints: { provideExamples: true, mentionAnalogies: true },
      tags: ["communication", "technical-translation", "stakeholder-management"]
    },
    {
      questionText: "Describe a time when you had to give difficult feedback to a colleague.",
      questionType: "COMMUNICATION" as const,
      category: "COMMUNICATION" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 200,
      context: "Evaluates difficult conversation management skills.",
      hints: { starMethod: true, focusOnApproach: true },
      tags: ["feedback", "difficult-conversations", "interpersonal-skills"]
    }
  ],

  SITUATIONAL: [
    {
      questionText: "If you discovered a security vulnerability in production, what would be your immediate steps?",
      questionType: "SITUATIONAL" as const,
      category: "TECHNICAL_SKILLS" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 180,
      context: "Tests crisis management and security awareness.",
      hints: { prioritizeSafety: true, mentionCommunication: true },
      tags: ["security", "crisis-management", "incident-response"]
    },
    {
      questionText: "How would you handle a situation where a client is unhappy with your team's work?",
      questionType: "SITUATIONAL" as const,
      category: "CUSTOMER_SERVICE" as const,
      difficulty: "INTERMEDIATE" as const,
      expectedDuration: 150,
      context: "Assesses client relationship management skills.",
      hints: { focusOnListening: true, mentionSolutions: true },
      tags: ["client-management", "conflict-resolution", "service-recovery"]
    }
  ],

  COMPANY_CULTURE: [
    {
      questionText: "What type of work environment do you thrive in?",
      questionType: "COMPANY_CULTURE" as const,
      category: "GENERAL" as const,
      difficulty: "BEGINNER" as const,
      expectedDuration: 120,
      context: "Assesses cultural fit and work style preferences.",
      hints: { beSpecific: true, relateToRole: true },
      tags: ["culture-fit", "work-style", "environment"]
    },
    {
      questionText: "How do you handle disagreements with company policies or decisions?",
      questionType: "COMPANY_CULTURE" as const,
      category: "ETHICS" as const,
      difficulty: "ADVANCED" as const,
      expectedDuration: 180,
      context: "Tests professional maturity and ethical reasoning.",
      hints: { showRespect: true, mentionConstructiveApproach: true },
      tags: ["ethics", "professionalism", "constructive-feedback"]
    }
  ]
};

export class QuestionBankService {
  /**
   * Get questions by type
   */
  static getQuestionsByType(type: QuestionTemplate['questionType']): QuestionTemplate[] {
    const allQuestions = Object.values(QUESTION_BANKS).flat();
    return allQuestions.filter(q => q.questionType === type);
  }

  /**
   * Get questions by category
   */
  static getQuestionsByCategory(category: QuestionTemplate['category']): QuestionTemplate[] {
    const allQuestions = Object.values(QUESTION_BANKS).flat();
    return allQuestions.filter(q => q.category === category);
  }

  /**
   * Get questions by difficulty
   */
  static getQuestionsByDifficulty(difficulty: QuestionTemplate['difficulty']): QuestionTemplate[] {
    const allQuestions = Object.values(QUESTION_BANKS).flat();
    return allQuestions.filter(q => q.difficulty === difficulty);
  }

  /**
   * Get questions by tags
   */
  static getQuestionsByTags(tags: string[]): QuestionTemplate[] {
    const allQuestions = Object.values(QUESTION_BANKS).flat();
    return allQuestions.filter(q => 
      q.tags && tags.some(tag => q.tags!.includes(tag))
    );
  }

  /**
   * Get all available question types
   */
  static getAvailableTypes(): QuestionTemplate['questionType'][] {
    return ['BEHAVIORAL', 'TECHNICAL', 'SITUATIONAL', 'COMPANY_CULTURE', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'STRESS_TEST', 'CASE_STUDY', 'ROLE_SPECIFIC'];
  }

  /**
   * Get all available categories
   */
  static getAvailableCategories(): QuestionTemplate['category'][] {
    return ['GENERAL', 'TECHNICAL_SKILLS', 'SOFT_SKILLS', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'TEAMWORK', 'ADAPTABILITY', 'CREATIVITY', 'ANALYTICAL_THINKING', 'CUSTOMER_SERVICE', 'SALES', 'MANAGEMENT', 'STRATEGY', 'ETHICS', 'INDUSTRY_KNOWLEDGE'];
  }

  /**
   * Get random questions with filters
   */
  static getRandomQuestions(
    count: number,
    filters?: {
      types?: QuestionTemplate['questionType'][];
      categories?: QuestionTemplate['category'][];
      difficulty?: QuestionTemplate['difficulty'];
      tags?: string[];
    }
  ): QuestionTemplate[] {
    let questions = Object.values(QUESTION_BANKS).flat();

    // Apply filters
    if (filters?.types) {
      questions = questions.filter(q => filters.types!.includes(q.questionType));
    }
    if (filters?.categories) {
      questions = questions.filter(q => filters.categories!.includes(q.category));
    }
    if (filters?.difficulty) {
      questions = questions.filter(q => q.difficulty === filters.difficulty);
    }
    if (filters?.tags) {
      questions = questions.filter(q => 
        q.tags && filters.tags!.some(tag => q.tags!.includes(tag))
      );
    }

    // Shuffle and return requested count
    const shuffled = questions.sort(() => Math.random() - 0.5);
    return shuffled.slice(0, count);
  }
}
