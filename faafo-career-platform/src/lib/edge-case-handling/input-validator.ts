/**
 * Input Validation Module
 * Handles input sanitization and validation for edge case handling
 */

export interface ValidationResult {
  success: boolean;
  error?: string;
  errorType?: string;
  sanitizedInput?: any;
}

export interface ValidationRules {
  maxInputSize: number;
  maxArraySize: number;
  maxStringLength: number;
  requiredFields: string[];
  allowedTypes: Record<string, string>;
}

export class InputValidator {
  private readonly MAX_INPUT_SIZE = 100000; // 100KB
  private readonly MAX_ARRAY_SIZE = 1000;
  private readonly MAX_STRING_LENGTH = 1000;

  private readonly validationRules: Record<string, ValidationRules> = {
    skillAssessment: {
      maxInputSize: this.MAX_INPUT_SIZE,
      maxArraySize: this.MAX_ARRAY_SIZE,
      maxStringLength: this.MAX_STRING_LENGTH,
      requiredFields: ['userId'],
      allowedTypes: {
        userId: 'string',
        skillIds: 'array',
        careerPathId: 'string',
        assessmentType: 'string'
      }
    },
    learningPath: {
      maxInputSize: this.MAX_INPUT_SIZE,
      maxArraySize: this.MAX_ARRAY_SIZE,
      maxStringLength: this.MAX_STRING_LENGTH,
      requiredFields: ['userId', 'targetRole'],
      allowedTypes: {
        userId: 'string',
        targetRole: 'string',
        currentSkills: 'array',
        timeframe: 'number',
        budget: 'number'
      }
    },
    marketData: {
      maxInputSize: this.MAX_INPUT_SIZE,
      maxArraySize: this.MAX_ARRAY_SIZE,
      maxStringLength: this.MAX_STRING_LENGTH,
      requiredFields: ['skill'],
      allowedTypes: {
        skill: 'string',
        location: 'string',
        forceRefresh: 'boolean'
      }
    }
  };

  /**
   * Validate input based on type
   */
  validateInput(input: any, type: string): ValidationResult {
    try {
      // Check if input exists
      if (!input) {
        return {
          success: false,
          error: 'Input is required',
          errorType: 'VALIDATION_ERROR'
        };
      }

      // Get validation rules for type
      const rules = this.validationRules[type];
      if (!rules) {
        return {
          success: false,
          error: `Unknown validation type: ${type}`,
          errorType: 'VALIDATION_ERROR'
        };
      }

      // Check input size
      const inputSize = JSON.stringify(input).length;
      if (inputSize > rules.maxInputSize) {
        return {
          success: false,
          error: `Input too large: ${inputSize} bytes (max: ${rules.maxInputSize})`,
          errorType: 'VALIDATION_ERROR'
        };
      }

      // Check required fields
      for (const field of rules.requiredFields) {
        if (!(field in input) || input[field] === null || input[field] === undefined) {
          return {
            success: false,
            error: `Required field missing: ${field}`,
            errorType: 'VALIDATION_ERROR'
          };
        }
      }

      // Validate field types and constraints
      const typeValidation = this.validateFieldTypes(input, rules);
      if (!typeValidation.success) {
        return typeValidation;
      }

      // Sanitize input
      const sanitizedInput = this.sanitizeInput(input, rules);

      return {
        success: true,
        sanitizedInput
      };
    } catch (error) {
      return {
        success: false,
        error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errorType: 'VALIDATION_ERROR'
      };
    }
  }

  /**
   * Validate field types according to rules
   */
  private validateFieldTypes(input: any, rules: ValidationRules): ValidationResult {
    for (const [field, expectedType] of Object.entries(rules.allowedTypes)) {
      if (field in input && input[field] !== null && input[field] !== undefined) {
        const actualType = Array.isArray(input[field]) ? 'array' : typeof input[field];
        
        if (actualType !== expectedType) {
          return {
            success: false,
            error: `Invalid type for field '${field}': expected ${expectedType}, got ${actualType}`,
            errorType: 'VALIDATION_ERROR'
          };
        }

        // Additional type-specific validations
        const specificValidation = this.validateSpecificType(input[field], expectedType, field, rules);
        if (!specificValidation.success) {
          return specificValidation;
        }
      }
    }

    return { success: true };
  }

  /**
   * Validate specific type constraints
   */
  private validateSpecificType(value: any, type: string, field: string, rules: ValidationRules): ValidationResult {
    switch (type) {
      case 'string':
        if (typeof value === 'string' && value.length > rules.maxStringLength) {
          return {
            success: false,
            error: `String field '${field}' too long: ${value.length} chars (max: ${rules.maxStringLength})`,
            errorType: 'VALIDATION_ERROR'
          };
        }
        break;

      case 'array':
        if (Array.isArray(value) && value.length > rules.maxArraySize) {
          return {
            success: false,
            error: `Array field '${field}' too large: ${value.length} items (max: ${rules.maxArraySize})`,
            errorType: 'VALIDATION_ERROR'
          };
        }
        break;

      case 'number':
        if (typeof value === 'number' && (isNaN(value) || !isFinite(value))) {
          return {
            success: false,
            error: `Invalid number for field '${field}': ${value}`,
            errorType: 'VALIDATION_ERROR'
          };
        }
        break;
    }

    return { success: true };
  }

  /**
   * Sanitize input to prevent injection attacks
   */
  sanitizeInput(input: any, rules?: ValidationRules): any {
    if (input === null || input === undefined) {
      return input;
    }

    if (typeof input === 'string') {
      return this.sanitizeString(input, rules?.maxStringLength);
    }

    if (Array.isArray(input)) {
      return this.sanitizeArray(input, rules?.maxArraySize);
    }

    if (typeof input === 'object') {
      return this.sanitizeObject(input, rules);
    }

    return input;
  }

  /**
   * Sanitize string input
   */
  private sanitizeString(str: string, maxLength?: number): string {
    // Remove potentially dangerous characters
    let sanitized = str
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/[<>]/g, '') // Remove angle brackets
      .trim();

    // Truncate if too long
    if (maxLength && sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }

    return sanitized;
  }

  /**
   * Sanitize array input
   */
  private sanitizeArray(arr: any[], maxSize?: number): any[] {
    let sanitized = arr.map(item => this.sanitizeInput(item));

    // Truncate if too large
    if (maxSize && sanitized.length > maxSize) {
      sanitized = sanitized.slice(0, maxSize);
    }

    return sanitized;
  }

  /**
   * Sanitize object input
   */
  private sanitizeObject(obj: any, rules?: ValidationRules): any {
    const sanitized: any = {};

    for (const [key, value] of Object.entries(obj)) {
      // Sanitize key
      const sanitizedKey = this.sanitizeString(key, 100);
      
      // Sanitize value
      sanitized[sanitizedKey] = this.sanitizeInput(value, rules);
    }

    return sanitized;
  }

  /**
   * Validate business logic constraints
   */
  validateBusinessLogic(input: any, type: string): ValidationResult {
    switch (type) {
      case 'skillAssessment':
        return this.validateSkillAssessmentLogic(input);
      case 'learningPath':
        return this.validateLearningPathLogic(input);
      case 'marketData':
        return this.validateMarketDataLogic(input);
      default:
        return { success: true };
    }
  }

  /**
   * Validate skill assessment business logic
   */
  private validateSkillAssessmentLogic(input: any): ValidationResult {
    // Check if user ID is valid format
    if (input.userId && typeof input.userId === 'string') {
      if (input.userId.length < 3 || input.userId.length > 50) {
        return {
          success: false,
          error: 'User ID must be between 3 and 50 characters',
          errorType: 'BUSINESS_LOGIC_ERROR'
        };
      }
    }

    // Check skill IDs array
    if (input.skillIds && Array.isArray(input.skillIds)) {
      if (input.skillIds.length === 0) {
        return {
          success: false,
          error: 'At least one skill ID is required',
          errorType: 'BUSINESS_LOGIC_ERROR'
        };
      }

      if (input.skillIds.length > 20) {
        return {
          success: false,
          error: 'Too many skills (max: 20)',
          errorType: 'BUSINESS_LOGIC_ERROR'
        };
      }
    }

    return { success: true };
  }

  /**
   * Validate learning path business logic
   */
  private validateLearningPathLogic(input: any): ValidationResult {
    // Check timeframe
    if (input.timeframe && typeof input.timeframe === 'number') {
      if (input.timeframe < 1 || input.timeframe > 52) {
        return {
          success: false,
          error: 'Timeframe must be between 1 and 52 weeks',
          errorType: 'BUSINESS_LOGIC_ERROR'
        };
      }
    }

    // Check budget
    if (input.budget && typeof input.budget === 'number') {
      if (input.budget < 0 || input.budget > 50000) {
        return {
          success: false,
          error: 'Budget must be between $0 and $50,000',
          errorType: 'BUSINESS_LOGIC_ERROR'
        };
      }
    }

    return { success: true };
  }

  /**
   * Validate market data business logic
   */
  private validateMarketDataLogic(input: any): ValidationResult {
    // Check skill name
    if (input.skill && typeof input.skill === 'string') {
      if (input.skill.length < 2 || input.skill.length > 100) {
        return {
          success: false,
          error: 'Skill name must be between 2 and 100 characters',
          errorType: 'BUSINESS_LOGIC_ERROR'
        };
      }
    }

    return { success: true };
  }

  /**
   * Get validation rules for a type
   */
  getValidationRules(type: string): ValidationRules | null {
    return this.validationRules[type] || null;
  }

  /**
   * Add custom validation rules
   */
  addValidationRules(type: string, rules: ValidationRules): void {
    this.validationRules[type] = rules;
  }
}
