/**
 * Edge Case Handling Module Exports
 * Centralized exports for all edge case handling modules
 */

// Input Validator
export {
  InputValidator,
  type ValidationResult,
  type ValidationRules
} from './input-validator';

// Security Checker
export {
  <PERSON><PERSON><PERSON><PERSON>,
  type SecurityResult,
  type <PERSON>Incident,
  type SecurityConfig
} from './security-checker';

// Circuit Breaker
export {
  CircuitBreaker,
  type CircuitBreakerConfig,
  type CircuitBreakerState,
  type CircuitBreakerResult
} from './circuit-breaker';

// Error Handler
export {
  ErrorHandler,
  type ErrorResult,
  type ErrorStatistics,
  type FallbackConfig
} from './error-handler';

// Retry Manager
export {
  RetryManager,
  type RetryConfig,
  type RetryResult,
  type RetryStatistics
} from './retry-manager';

// Main Refactored Edge Case Handler
export {
  RefactoredEdgeCaseHandler,
  refactoredEdgeCaseHandler,
  type EdgeCaseHandlerConfig,
  type <PERSON><PERSON><PERSON><PERSON><PERSON>lerResult,
  type EdgeCaseHandlerStats
} from './refactored-edge-case-handler';

// Legacy compatibility - export the refactored handler as the main handler
export { refactoredEdgeCaseHandler as edgeCaseHandler } from './refactored-edge-case-handler';
