/**
 * Circuit Breaker Module
 * Implements circuit breaker pattern for service resilience
 */

export interface CircuitBreakerConfig {
  failureThreshold: number;
  timeout: number;
  monitoringPeriod: number;
  expectedExceptionTypes: string[];
}

export interface CircuitBreakerState {
  failures: number;
  lastFailure: Date | null;
  isOpen: boolean;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  successCount: number;
  totalRequests: number;
  lastStateChange: Date;
}

export interface CircuitBreakerResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  circuitBreakerTriggered?: boolean;
  state?: string;
}

export class CircuitBreaker {
  private circuits: Map<string, CircuitBreakerState> = new Map();
  private readonly defaultConfig: CircuitBreakerConfig = {
    failureThreshold: 5,
    timeout: 60000, // 1 minute
    monitoringPeriod: 300000, // 5 minutes
    expectedExceptionTypes: ['TIMEOUT_ERROR', 'SYSTEM_ERROR', 'AI_SERVICE_ERROR']
  };

  private configs: Map<string, CircuitBreakerConfig> = new Map();

  /**
   * Execute function with circuit breaker protection
   */
  async execute<T>(
    serviceName: string,
    operation: () => Promise<T>,
    config?: Partial<CircuitBreakerConfig>
  ): Promise<CircuitBreakerResult<T>> {
    const circuitConfig = this.getConfig(serviceName, config);
    const state = this.getOrCreateState(serviceName);

    // Check if circuit is open
    if (this.isCircuitOpen(serviceName, circuitConfig)) {
      return {
        success: false,
        error: `Circuit breaker is open for service: ${serviceName}`,
        circuitBreakerTriggered: true,
        state: state.state
      };
    }

    // If half-open, allow limited requests
    if (state.state === 'HALF_OPEN') {
      return this.executeInHalfOpenState(serviceName, operation, circuitConfig);
    }

    // Execute operation
    try {
      state.totalRequests++;
      const result = await operation();
      
      // Success - reset failure count
      this.recordSuccess(serviceName);
      
      return {
        success: true,
        data: result,
        state: state.state
      };
    } catch (error) {
      // Failure - record and check if circuit should open
      this.recordFailure(serviceName, error as Error, circuitConfig);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        state: this.getState(serviceName)?.state
      };
    }
  }

  /**
   * Check if circuit breaker is open for a service
   */
  isCircuitOpen(serviceName: string, config?: CircuitBreakerConfig): boolean {
    const state = this.getState(serviceName);
    if (!state) return false;

    const circuitConfig = config || this.getConfig(serviceName);

    // If circuit is explicitly open
    if (state.isOpen) {
      // Check if timeout period has passed
      const now = new Date();
      const timeSinceLastFailure = state.lastFailure 
        ? now.getTime() - state.lastFailure.getTime()
        : 0;

      if (timeSinceLastFailure > circuitConfig.timeout) {
        // Move to half-open state
        state.state = 'HALF_OPEN';
        state.isOpen = false;
        state.successCount = 0;
        state.lastStateChange = now;
        return false;
      }

      return true;
    }

    return false;
  }

  /**
   * Execute operation in half-open state
   */
  private async executeInHalfOpenState<T>(
    serviceName: string,
    operation: () => Promise<T>,
    config: CircuitBreakerConfig
  ): Promise<CircuitBreakerResult<T>> {
    const state = this.getState(serviceName)!;

    try {
      state.totalRequests++;
      const result = await operation();
      
      // Success in half-open state
      state.successCount++;
      
      // If we have enough successful requests, close the circuit
      if (state.successCount >= 3) {
        this.closeCircuit(serviceName);
      }
      
      return {
        success: true,
        data: result,
        state: state.state
      };
    } catch (error) {
      // Failure in half-open state - reopen circuit
      this.openCircuit(serviceName, error as Error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        state: state.state
      };
    }
  }

  /**
   * Record successful operation
   */
  private recordSuccess(serviceName: string): void {
    const state = this.getOrCreateState(serviceName);
    
    // Reset failure count on success
    state.failures = 0;
    state.lastFailure = null;
    
    // If circuit was half-open, increment success count
    if (state.state === 'HALF_OPEN') {
      state.successCount++;
    }
  }

  /**
   * Record failed operation
   */
  private recordFailure(serviceName: string, error: Error, config: CircuitBreakerConfig): void {
    const state = this.getOrCreateState(serviceName);
    
    state.failures++;
    state.lastFailure = new Date();
    
    // Check if we should open the circuit
    if (state.failures >= config.failureThreshold) {
      this.openCircuit(serviceName, error);
    }
  }

  /**
   * Open circuit breaker
   */
  private openCircuit(serviceName: string, error: Error): void {
    const state = this.getOrCreateState(serviceName);
    
    state.isOpen = true;
    state.state = 'OPEN';
    state.lastStateChange = new Date();
    
    console.warn(`Circuit breaker opened for service: ${serviceName}`, {
      failures: state.failures,
      error: error.message,
      timestamp: state.lastStateChange
    });
  }

  /**
   * Close circuit breaker
   */
  private closeCircuit(serviceName: string): void {
    const state = this.getOrCreateState(serviceName);
    
    state.isOpen = false;
    state.state = 'CLOSED';
    state.failures = 0;
    state.successCount = 0;
    state.lastFailure = null;
    state.lastStateChange = new Date();
    
    console.info(`Circuit breaker closed for service: ${serviceName}`, {
      timestamp: state.lastStateChange
    });
  }

  /**
   * Get or create circuit breaker state
   */
  private getOrCreateState(serviceName: string): CircuitBreakerState {
    if (!this.circuits.has(serviceName)) {
      this.circuits.set(serviceName, {
        failures: 0,
        lastFailure: null,
        isOpen: false,
        state: 'CLOSED',
        successCount: 0,
        totalRequests: 0,
        lastStateChange: new Date()
      });
    }
    
    return this.circuits.get(serviceName)!;
  }

  /**
   * Get circuit breaker state
   */
  getState(serviceName: string): CircuitBreakerState | null {
    return this.circuits.get(serviceName) || null;
  }

  /**
   * Get configuration for service
   */
  private getConfig(serviceName: string, override?: Partial<CircuitBreakerConfig>): CircuitBreakerConfig {
    const baseConfig = this.configs.get(serviceName) || this.defaultConfig;
    return override ? { ...baseConfig, ...override } : baseConfig;
  }

  /**
   * Set configuration for service
   */
  setConfig(serviceName: string, config: Partial<CircuitBreakerConfig>): void {
    const currentConfig = this.configs.get(serviceName) || this.defaultConfig;
    this.configs.set(serviceName, { ...currentConfig, ...config });
  }

  /**
   * Reset circuit breaker for service
   */
  reset(serviceName: string): void {
    const state = this.getState(serviceName);
    if (state) {
      state.failures = 0;
      state.lastFailure = null;
      state.isOpen = false;
      state.state = 'CLOSED';
      state.successCount = 0;
      state.lastStateChange = new Date();
    }
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    for (const serviceName of Array.from(this.circuits.keys())) {
      this.reset(serviceName);
    }
  }

  /**
   * Get statistics for all circuit breakers
   */
  getStatistics(): Record<string, CircuitBreakerState & { config: CircuitBreakerConfig }> {
    const stats: Record<string, CircuitBreakerState & { config: CircuitBreakerConfig }> = {};
    
    for (const [serviceName, state] of Array.from(this.circuits.entries())) {
      stats[serviceName] = {
        ...state,
        config: this.getConfig(serviceName)
      };
    }
    
    return stats;
  }

  /**
   * Get health status of all circuits
   */
  getHealthStatus(): {
    healthy: string[];
    degraded: string[];
    unhealthy: string[];
    totalCircuits: number;
  } {
    const healthy: string[] = [];
    const degraded: string[] = [];
    const unhealthy: string[] = [];
    
    for (const [serviceName, state] of Array.from(this.circuits.entries())) {
      if (state.state === 'CLOSED' && state.failures === 0) {
        healthy.push(serviceName);
      } else if (state.state === 'HALF_OPEN' || (state.state === 'CLOSED' && state.failures > 0)) {
        degraded.push(serviceName);
      } else {
        unhealthy.push(serviceName);
      }
    }
    
    return {
      healthy,
      degraded,
      unhealthy,
      totalCircuits: this.circuits.size
    };
  }

  /**
   * Clean up old circuit breaker states
   */
  cleanup(maxAge: number = 24 * 60 * 60 * 1000): number { // 24 hours default
    const now = new Date();
    let cleaned = 0;
    
    for (const [serviceName, state] of Array.from(this.circuits.entries())) {
      const age = now.getTime() - state.lastStateChange.getTime();
      
      // Remove circuits that haven't been used recently and are in good state
      if (age > maxAge && state.state === 'CLOSED' && state.failures === 0) {
        this.circuits.delete(serviceName);
        this.configs.delete(serviceName);
        cleaned++;
      }
    }
    
    return cleaned;
  }

  /**
   * Force open circuit (for testing or maintenance)
   */
  forceOpen(serviceName: string): void {
    const state = this.getOrCreateState(serviceName);
    state.isOpen = true;
    state.state = 'OPEN';
    state.lastStateChange = new Date();
  }

  /**
   * Force close circuit (for testing or recovery)
   */
  forceClose(serviceName: string): void {
    this.closeCircuit(serviceName);
  }
}
