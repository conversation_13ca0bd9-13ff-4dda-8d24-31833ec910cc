/**
 * Security Checker Module
 * Handles security validation and incident logging for edge case handling
 */

export interface SecurityResult {
  success: boolean;
  error?: string;
  errorType?: string;
  securityAlert?: boolean;
  threatLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  blockedReason?: string;
}

export interface SecurityIncident {
  type: string;
  userId?: string;
  timestamp: Date;
  request: string;
  error: string;
  ipAddress?: string;
  userAgent?: string;
  threatLevel?: string;
}

export interface SecurityConfig {
  enableSqlInjectionCheck: boolean;
  enableXssCheck: boolean;
  enableCommandInjectionCheck: boolean;
  enablePathTraversalCheck: boolean;
  maxRequestsPerMinute: number;
  blockedPatterns: string[];
  allowedDomains: string[];
}

export class SecurityChecker {
  private incidents: SecurityIncident[] = [];
  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();
  
  private readonly config: SecurityConfig = {
    enableSqlInjectionCheck: true,
    enableXssCheck: true,
    enableCommandInjectionCheck: true,
    enablePathTraversalCheck: true,
    maxRequestsPerMinute: 100,
    blockedPatterns: [
      'DROP TABLE',
      'DELETE FROM',
      'INSERT INTO',
      'UPDATE SET',
      '<script',
      'javascript:',
      'eval(',
      'setTimeout(',
      'setInterval(',
      '../',
      '..\\',
      '/etc/passwd',
      '/proc/',
      'cmd.exe',
      'powershell'
    ],
    allowedDomains: [
      'localhost',
      '127.0.0.1',
      'your-domain.com'
    ]
  };

  /**
   * Perform comprehensive security checks
   */
  checkSecurity(request: any, context?: { ipAddress?: string; userAgent?: string }): SecurityResult {
    try {
      // Rate limiting check
      const rateLimitResult = this.checkRateLimit(request.userId, context?.ipAddress);
      if (!rateLimitResult.success) {
        return rateLimitResult;
      }

      // SQL injection check
      if (this.config.enableSqlInjectionCheck) {
        const sqlResult = this.checkSqlInjection(request);
        if (!sqlResult.success) {
          return sqlResult;
        }
      }

      // XSS check
      if (this.config.enableXssCheck) {
        const xssResult = this.checkXss(request);
        if (!xssResult.success) {
          return xssResult;
        }
      }

      // Command injection check
      if (this.config.enableCommandInjectionCheck) {
        const cmdResult = this.checkCommandInjection(request);
        if (!cmdResult.success) {
          return cmdResult;
        }
      }

      // Path traversal check
      if (this.config.enablePathTraversalCheck) {
        const pathResult = this.checkPathTraversal(request);
        if (!pathResult.success) {
          return pathResult;
        }
      }

      // Blocked patterns check
      const patternResult = this.checkBlockedPatterns(request);
      if (!patternResult.success) {
        return patternResult;
      }

      // Input size and complexity check
      const complexityResult = this.checkInputComplexity(request);
      if (!complexityResult.success) {
        return complexityResult;
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `Security check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errorType: 'SECURITY_ERROR',
        securityAlert: true,
        threatLevel: 'MEDIUM'
      };
    }
  }

  /**
   * Check rate limiting
   */
  private checkRateLimit(userId?: string, ipAddress?: string): SecurityResult {
    const identifier = userId || ipAddress || 'anonymous';
    const now = Date.now();
    const windowStart = now - 60000; // 1 minute window

    const current = this.requestCounts.get(identifier);
    
    if (!current || current.resetTime < windowStart) {
      // Reset or initialize counter
      this.requestCounts.set(identifier, { count: 1, resetTime: now });
      return { success: true };
    }

    if (current.count >= this.config.maxRequestsPerMinute) {
      return {
        success: false,
        error: 'Rate limit exceeded',
        errorType: 'SECURITY_ERROR',
        securityAlert: true,
        threatLevel: 'MEDIUM',
        blockedReason: `Too many requests: ${current.count}/${this.config.maxRequestsPerMinute} per minute`
      };
    }

    // Increment counter
    current.count++;
    return { success: true };
  }

  /**
   * Check for SQL injection patterns
   */
  private checkSqlInjection(request: any): SecurityResult {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /('|\"|;|--|\*|\/\*|\*\/)/,
      /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR)\b)/i
    ];

    const requestString = JSON.stringify(request).toLowerCase();
    
    for (const pattern of sqlPatterns) {
      if (pattern.test(requestString)) {
        return {
          success: false,
          error: 'Potential SQL injection detected',
          errorType: 'SECURITY_ERROR',
          securityAlert: true,
          threatLevel: 'HIGH',
          blockedReason: `SQL injection pattern detected: ${pattern.source}`
        };
      }
    }

    return { success: true };
  }

  /**
   * Check for XSS patterns
   */
  private checkXss(request: any): SecurityResult {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i,
      /eval\s*\(/i,
      /expression\s*\(/i
    ];

    const requestString = JSON.stringify(request);
    
    for (const pattern of xssPatterns) {
      if (pattern.test(requestString)) {
        return {
          success: false,
          error: 'Potential XSS attack detected',
          errorType: 'SECURITY_ERROR',
          securityAlert: true,
          threatLevel: 'HIGH',
          blockedReason: `XSS pattern detected: ${pattern.source}`
        };
      }
    }

    return { success: true };
  }

  /**
   * Check for command injection patterns
   */
  private checkCommandInjection(request: any): SecurityResult {
    const cmdPatterns = [
      /(\||&|;|`|\$\(|\$\{)/,
      /\b(cmd|powershell|bash|sh|exec|system|eval)\b/i,
      /\b(rm|del|format|fdisk)\b/i,
      /(>|<|>>)/
    ];

    const requestString = JSON.stringify(request);
    
    for (const pattern of cmdPatterns) {
      if (pattern.test(requestString)) {
        return {
          success: false,
          error: 'Potential command injection detected',
          errorType: 'SECURITY_ERROR',
          securityAlert: true,
          threatLevel: 'CRITICAL',
          blockedReason: `Command injection pattern detected: ${pattern.source}`
        };
      }
    }

    return { success: true };
  }

  /**
   * Check for path traversal patterns
   */
  private checkPathTraversal(request: any): SecurityResult {
    const pathPatterns = [
      /\.\.\//,
      /\.\.\\/,
      /\/etc\/passwd/i,
      /\/proc\//i,
      /\\windows\\system32/i,
      /%2e%2e%2f/i,
      /%2e%2e%5c/i
    ];

    const requestString = JSON.stringify(request);
    
    for (const pattern of pathPatterns) {
      if (pattern.test(requestString)) {
        return {
          success: false,
          error: 'Potential path traversal detected',
          errorType: 'SECURITY_ERROR',
          securityAlert: true,
          threatLevel: 'HIGH',
          blockedReason: `Path traversal pattern detected: ${pattern.source}`
        };
      }
    }

    return { success: true };
  }

  /**
   * Check for blocked patterns
   */
  private checkBlockedPatterns(request: any): SecurityResult {
    const requestString = JSON.stringify(request).toLowerCase();
    
    for (const pattern of this.config.blockedPatterns) {
      if (requestString.includes(pattern.toLowerCase())) {
        return {
          success: false,
          error: 'Blocked pattern detected',
          errorType: 'SECURITY_ERROR',
          securityAlert: true,
          threatLevel: 'MEDIUM',
          blockedReason: `Blocked pattern found: ${pattern}`
        };
      }
    }

    return { success: true };
  }

  /**
   * Check input complexity and size
   */
  private checkInputComplexity(request: any): SecurityResult {
    const requestString = JSON.stringify(request);
    
    // Check for extremely large inputs
    if (requestString.length > 1000000) { // 1MB
      return {
        success: false,
        error: 'Input too large',
        errorType: 'SECURITY_ERROR',
        securityAlert: true,
        threatLevel: 'MEDIUM',
        blockedReason: `Input size exceeds limit: ${requestString.length} bytes`
      };
    }

    // Check for deeply nested objects (potential DoS)
    const depth = this.getObjectDepth(request);
    if (depth > 20) {
      return {
        success: false,
        error: 'Input too complex',
        errorType: 'SECURITY_ERROR',
        securityAlert: true,
        threatLevel: 'MEDIUM',
        blockedReason: `Object nesting too deep: ${depth} levels`
      };
    }

    return { success: true };
  }

  /**
   * Get object nesting depth
   */
  private getObjectDepth(obj: any, depth = 0): number {
    if (depth > 50) return depth; // Prevent stack overflow
    
    if (obj && typeof obj === 'object') {
      const depths = Object.values(obj).map(value => 
        this.getObjectDepth(value, depth + 1)
      );
      return Math.max(depth, ...depths);
    }
    
    return depth;
  }

  /**
   * Log security incident
   */
  logSecurityIncident(incident: SecurityIncident): void {
    // Add timestamp if not provided
    if (!incident.timestamp) {
      incident.timestamp = new Date();
    }

    // Store incident
    this.incidents.push(incident);

    // Keep only last 1000 incidents to prevent memory issues
    if (this.incidents.length > 1000) {
      this.incidents = this.incidents.slice(-1000);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Security Incident:', incident);
    }

    // In production, you would send this to a security monitoring service
    // Example: await securityMonitoringService.reportIncident(incident);
  }

  /**
   * Get security statistics
   */
  getSecurityStats(): {
    totalIncidents: number;
    incidentsByType: Record<string, number>;
    recentIncidents: SecurityIncident[];
    threatLevels: Record<string, number>;
  } {
    const incidentsByType: Record<string, number> = {};
    const threatLevels: Record<string, number> = {};

    for (const incident of this.incidents) {
      incidentsByType[incident.type] = (incidentsByType[incident.type] || 0) + 1;
      if (incident.threatLevel) {
        threatLevels[incident.threatLevel] = (threatLevels[incident.threatLevel] || 0) + 1;
      }
    }

    return {
      totalIncidents: this.incidents.length,
      incidentsByType,
      recentIncidents: this.incidents.slice(-10), // Last 10 incidents
      threatLevels
    };
  }

  /**
   * Clear old incidents
   */
  clearOldIncidents(olderThanHours: number = 24): number {
    const cutoff = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    const initialCount = this.incidents.length;
    
    this.incidents = this.incidents.filter(incident => incident.timestamp > cutoff);
    
    return initialCount - this.incidents.length;
  }

  /**
   * Update security configuration
   */
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    Object.assign(this.config, newConfig);
  }

  /**
   * Get current security configuration
   */
  getConfig(): SecurityConfig {
    return { ...this.config };
  }
}
