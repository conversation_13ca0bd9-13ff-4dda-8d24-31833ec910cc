/**
 * Retry Manager Module
 * Handles timeout and retry logic with exponential backoff
 */

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  timeout: number;
  exponentialBase: number;
  jitterFactor: number;
}

export interface RetryResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  retryCount: number;
  totalTime: number;
  lastAttemptTime: number;
}

export interface RetryStatistics {
  totalAttempts: number;
  successfulRetries: number;
  failedRetries: number;
  averageRetryCount: number;
  averageSuccessTime: number;
}

export class RetryManager {
  private statistics: RetryStatistics = {
    totalAttempts: 0,
    successfulRetries: 0,
    failedRetries: 0,
    averageRetryCount: 0,
    averageSuccessTime: 0
  };

  private readonly defaultConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    timeout: 30000, // 30 seconds
    exponentialBase: 2,
    jitterFactor: 0.1
  };

  /**
   * Execute operation with retry logic
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    serviceName: string,
    config?: Partial<RetryConfig>
  ): Promise<RetryResult<T>> {
    const retryConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();
    let lastError: Error | null = null;
    let retryCount = 0;

    this.statistics.totalAttempts++;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        const result = await this.executeWithTimeout(operation, retryConfig.timeout);
        const totalTime = Date.now() - startTime;

        // Update statistics on success
        if (attempt > 0) {
          this.statistics.successfulRetries++;
        }
        this.updateAverageSuccessTime(totalTime);

        return {
          success: true,
          data: result,
          retryCount: attempt,
          totalTime,
          lastAttemptTime: Date.now()
        };
      } catch (error) {
        lastError = error as Error;
        retryCount = attempt;

        // If this is the last attempt, don't wait
        if (attempt === retryConfig.maxRetries) {
          break;
        }

        // Check if error is retryable
        if (!this.isRetryableError(lastError)) {
          break;
        }

        // Calculate delay for next attempt
        const delay = this.calculateDelay(attempt + 1, retryConfig);
        
        console.warn(`Attempt ${attempt + 1} failed for ${serviceName}, retrying in ${delay}ms:`, lastError.message);
        
        // Wait before next attempt
        await this.delay(delay);
      }
    }

    // All attempts failed
    this.statistics.failedRetries++;
    const totalTime = Date.now() - startTime;

    return {
      success: false,
      error: lastError?.message || 'Unknown error',
      retryCount,
      totalTime,
      lastAttemptTime: Date.now()
    };
  }

  /**
   * Execute operation with timeout and retry
   */
  async executeWithTimeoutAndRetry<T>(
    operation: () => Promise<T>,
    serviceName: string,
    timeout: number = 30000,
    maxRetries: number = 3
  ): Promise<RetryResult<T>> {
    return this.executeWithRetry(operation, serviceName, {
      timeout,
      maxRetries
    });
  }

  /**
   * Execute operation with timeout
   */
  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);

      operation()
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * Calculate delay for retry attempt with exponential backoff and jitter
   */
  private calculateDelay(attemptNumber: number, config: RetryConfig): number {
    // Exponential backoff
    const exponentialDelay = config.baseDelay * Math.pow(config.exponentialBase, attemptNumber - 1);
    
    // Add jitter to prevent thundering herd
    const jitter = exponentialDelay * config.jitterFactor * Math.random();
    
    // Calculate final delay
    const delay = exponentialDelay + jitter;
    
    // Cap at maximum delay
    return Math.min(delay, config.maxDelay);
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const message = error.message.toLowerCase();
    
    // Network and temporary errors are retryable
    const retryablePatterns = [
      'timeout',
      'network',
      'connection',
      'temporary',
      'service unavailable',
      'rate limit',
      'too many requests',
      'internal server error',
      'bad gateway',
      'service temporarily unavailable',
      'gateway timeout',
      'econnrefused',
      'enotfound',
      'etimedout'
    ];

    // Permanent errors are not retryable
    const nonRetryablePatterns = [
      'unauthorized',
      'forbidden',
      'not found',
      'bad request',
      'invalid',
      'malformed',
      'authentication failed',
      'access denied'
    ];

    // Check for non-retryable patterns first
    for (const pattern of nonRetryablePatterns) {
      if (message.includes(pattern)) {
        return false;
      }
    }

    // Check for retryable patterns
    for (const pattern of retryablePatterns) {
      if (message.includes(pattern)) {
        return true;
      }
    }

    // Default to retryable for unknown errors
    return true;
  }

  /**
   * Simple delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update average success time
   */
  private updateAverageSuccessTime(newTime: number): void {
    const currentAverage = this.statistics.averageSuccessTime;
    const totalSuccesses = this.statistics.successfulRetries + 1;
    
    this.statistics.averageSuccessTime = 
      (currentAverage * (totalSuccesses - 1) + newTime) / totalSuccesses;
  }

  /**
   * Execute with circuit breaker integration
   */
  async executeWithCircuitBreaker<T>(
    operation: () => Promise<T>,
    serviceName: string,
    circuitBreaker: any,
    config?: Partial<RetryConfig>
  ): Promise<RetryResult<T>> {
    // Check if circuit breaker is open
    if (circuitBreaker.isCircuitOpen(serviceName)) {
      return {
        success: false,
        error: `Circuit breaker is open for service: ${serviceName}`,
        retryCount: 0,
        totalTime: 0,
        lastAttemptTime: Date.now()
      };
    }

    // Execute with retry logic
    const result = await this.executeWithRetry(operation, serviceName, config);

    // Update circuit breaker based on result
    if (result.success) {
      // Record success (circuit breaker will handle this)
    } else {
      // Record failure (circuit breaker will handle this)
    }

    return result;
  }

  /**
   * Batch retry operations
   */
  async executeBatchWithRetry<T>(
    operations: Array<() => Promise<T>>,
    serviceName: string,
    config?: Partial<RetryConfig>
  ): Promise<Array<RetryResult<T>>> {
    const results: Array<RetryResult<T>> = [];
    
    // Execute operations in parallel with individual retry logic
    const promises = operations.map((operation, index) => 
      this.executeWithRetry(operation, `${serviceName}-${index}`, config)
    );

    const batchResults = await Promise.allSettled(promises);
    
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          success: false,
          error: result.reason?.message || 'Batch operation failed',
          retryCount: 0,
          totalTime: 0,
          lastAttemptTime: Date.now()
        });
      }
    }

    return results;
  }

  /**
   * Get retry statistics
   */
  getStatistics(): RetryStatistics {
    const totalRetries = this.statistics.successfulRetries + this.statistics.failedRetries;
    
    return {
      ...this.statistics,
      averageRetryCount: totalRetries > 0 ? 
        (this.statistics.successfulRetries + this.statistics.failedRetries) / this.statistics.totalAttempts : 0
    };
  }

  /**
   * Reset statistics
   */
  resetStatistics(): void {
    this.statistics = {
      totalAttempts: 0,
      successfulRetries: 0,
      failedRetries: 0,
      averageRetryCount: 0,
      averageSuccessTime: 0
    };
  }

  /**
   * Get recommended retry config for service type
   */
  getRecommendedConfig(serviceType: string): RetryConfig {
    const configs: Record<string, Partial<RetryConfig>> = {
      'ai-service': {
        maxRetries: 3,
        baseDelay: 2000,
        maxDelay: 60000,
        timeout: 45000
      },
      'database': {
        maxRetries: 5,
        baseDelay: 500,
        maxDelay: 10000,
        timeout: 15000
      },
      'external-api': {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 30000,
        timeout: 30000
      },
      'file-operation': {
        maxRetries: 2,
        baseDelay: 1000,
        maxDelay: 5000,
        timeout: 10000
      }
    };

    const serviceConfig = configs[serviceType] || {};
    return { ...this.defaultConfig, ...serviceConfig };
  }

  /**
   * Create retry function with predefined config
   */
  createRetryFunction<T>(
    serviceName: string,
    config?: Partial<RetryConfig>
  ): (operation: () => Promise<T>) => Promise<RetryResult<T>> {
    return (operation: () => Promise<T>) => 
      this.executeWithRetry(operation, serviceName, config);
  }
}
