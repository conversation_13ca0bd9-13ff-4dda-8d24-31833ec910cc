/**
 * Refactored Edge Case Handler
 * Main orchestrator for all edge case handling modules
 */

import { InputValidator, type ValidationResult } from './input-validator';
import { <PERSON><PERSON>he<PERSON>, type SecurityResult } from './security-checker';
import { CircuitBreaker, type CircuitBreakerResult } from './circuit-breaker';
import { <PERSON>rror<PERSON><PERSON>ler, type ErrorResult } from './error-handler';
import { RetryManager, type RetryResult } from './retry-manager';

export interface EdgeCaseHandlerConfig {
  enableValidation: boolean;
  enableSecurity: boolean;
  enableCircuitBreaker: boolean;
  enableRetry: boolean;
  enableFallbacks: boolean;
  logLevel: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
}

export interface EdgeCaseHandlerResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorType?: string;
  validationResult?: ValidationResult;
  securityResult?: SecurityResult;
  circuitBreakerTriggered?: boolean;
  retryCount?: number;
  fallbackUsed?: boolean;
  processingTime?: number;
  warnings?: string[];
}

export interface EdgeCaseHandlerStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  validationFailures: number;
  securityBlocks: number;
  circuitBreakerTrips: number;
  retriesPerformed: number;
  fallbacksUsed: number;
  averageProcessingTime: number;
}

export class RefactoredEdgeCaseHandler {
  private inputValidator: InputValidator;
  private securityChecker: SecurityChecker;
  private circuitBreaker: CircuitBreaker;
  private errorHandler: ErrorHandler;
  private retryManager: RetryManager;

  private config: EdgeCaseHandlerConfig = {
    enableValidation: true,
    enableSecurity: true,
    enableCircuitBreaker: true,
    enableRetry: true,
    enableFallbacks: true,
    logLevel: 'INFO'
  };

  private stats: EdgeCaseHandlerStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    validationFailures: 0,
    securityBlocks: 0,
    circuitBreakerTrips: 0,
    retriesPerformed: 0,
    fallbacksUsed: 0,
    averageProcessingTime: 0
  };

  constructor(config?: Partial<EdgeCaseHandlerConfig>) {
    this.config = { ...this.config, ...config };
    
    this.inputValidator = new InputValidator();
    this.securityChecker = new SecurityChecker();
    this.circuitBreaker = new CircuitBreaker();
    this.errorHandler = new ErrorHandler();
    this.retryManager = new RetryManager();
  }

  /**
   * Handle skill assessment with comprehensive edge case handling
   */
  async handleSkillAssessment(request: any, context?: any): Promise<EdgeCaseHandlerResult> {
    return this.handleRequest(
      request,
      'skillAssessment',
      async (validatedRequest) => {
        // Simulate skill assessment logic
        return this.processSkillAssessment(validatedRequest);
      },
      context
    );
  }

  /**
   * Handle learning path generation with edge case handling
   */
  async handleLearningPath(request: any, context?: any): Promise<EdgeCaseHandlerResult> {
    return this.handleRequest(
      request,
      'learningPath',
      async (validatedRequest) => {
        // Simulate learning path logic
        return this.processLearningPath(validatedRequest);
      },
      context
    );
  }

  /**
   * Handle market data retrieval with edge case handling
   */
  async handleMarketData(request: any, context?: any): Promise<EdgeCaseHandlerResult> {
    return this.handleRequest(
      request,
      'marketData',
      async (validatedRequest) => {
        // Simulate market data logic
        return this.processMarketData(validatedRequest);
      },
      context
    );
  }

  /**
   * Generic request handler with all edge case handling
   */
  async handleRequest<T>(
    request: any,
    requestType: string,
    processor: (validatedRequest: any) => Promise<T>,
    context?: any
  ): Promise<EdgeCaseHandlerResult<T>> {
    const startTime = Date.now();
    this.stats.totalRequests++;
    this.errorHandler.incrementRequests();

    const warnings: string[] = [];
    let validationResult: ValidationResult | undefined;
    let securityResult: SecurityResult | undefined;

    try {
      // Step 1: Input Validation
      if (this.config.enableValidation) {
        validationResult = this.inputValidator.validateInput(request, requestType);
        if (!validationResult.success) {
          this.stats.validationFailures++;
          return this.createFailureResult(
            validationResult.error!,
            validationResult.errorType!,
            startTime,
            { validationResult, warnings }
          );
        }

        // Also validate business logic
        const businessValidation = this.inputValidator.validateBusinessLogic(
          validationResult.sanitizedInput,
          requestType
        );
        if (!businessValidation.success) {
          this.stats.validationFailures++;
          return this.createFailureResult(
            businessValidation.error!,
            businessValidation.errorType!,
            startTime,
            { validationResult, warnings }
          );
        }

        request = validationResult.sanitizedInput;
      }

      // Step 2: Security Check
      if (this.config.enableSecurity) {
        securityResult = this.securityChecker.checkSecurity(request, context);
        if (!securityResult.success) {
          this.stats.securityBlocks++;
          
          // Log security incident
          this.securityChecker.logSecurityIncident({
            type: securityResult.errorType || 'SECURITY_VIOLATION',
            userId: request.userId,
            timestamp: new Date(),
            request: JSON.stringify(request),
            error: securityResult.error!,
            ipAddress: context?.ipAddress,
            userAgent: context?.userAgent,
            threatLevel: securityResult.threatLevel
          });

          return this.createFailureResult(
            securityResult.error!,
            securityResult.errorType!,
            startTime,
            { validationResult, securityResult, warnings }
          );
        }
      }

      // Step 3: Execute with Circuit Breaker and Retry
      let result: any;
      let retryCount = 0;
      let circuitBreakerTriggered = false;

      if (this.config.enableCircuitBreaker && this.config.enableRetry) {
        const retryResult = await this.retryManager.executeWithRetry(
          async () => {
            return await this.circuitBreaker.execute(
              requestType,
              () => processor(request)
            );
          },
          requestType
        );

        if (!retryResult.success) {
          this.stats.failedRequests++;
          this.errorHandler.recordError(retryResult.error!, requestType);
          
          // Check if circuit breaker was triggered
          const cbResult = retryResult.data as CircuitBreakerResult;
          if (cbResult?.circuitBreakerTriggered) {
            this.stats.circuitBreakerTrips++;
            circuitBreakerTriggered = true;
          }

          retryCount = retryResult.retryCount;
          if (retryCount > 0) {
            this.stats.retriesPerformed += retryCount;
          }

          // Try fallback if enabled
          if (this.config.enableFallbacks) {
            const fallbackResult = this.errorHandler.handleUnexpectedError(
              new Error(retryResult.error!),
              requestType,
              request
            );

            if (fallbackResult.fallbackData) {
              this.stats.fallbacksUsed++;
              warnings.push('Using fallback data due to service failure');
              
              return this.createSuccessResult(
                fallbackResult.fallbackData,
                startTime,
                {
                  validationResult,
                  securityResult,
                  retryCount,
                  circuitBreakerTriggered,
                  fallbackUsed: true,
                  warnings
                }
              );
            }
          }

          return this.createFailureResult(
            retryResult.error!,
            'SYSTEM_ERROR',
            startTime,
            {
              validationResult,
              securityResult,
              retryCount,
              circuitBreakerTriggered,
              warnings
            }
          );
        }

        result = retryResult.data;
        retryCount = retryResult.retryCount;
        if (retryCount > 0) {
          this.stats.retriesPerformed += retryCount;
          warnings.push(`Request succeeded after ${retryCount} retries`);
        }
      } else {
        // Execute without circuit breaker/retry
        result = await processor(request);
      }

      // Success
      this.stats.successfulRequests++;
      return this.createSuccessResult(
        result,
        startTime,
        {
          validationResult,
          securityResult,
          retryCount,
          circuitBreakerTriggered,
          warnings
        }
      );

    } catch (error) {
      this.stats.failedRequests++;
      this.errorHandler.recordError(error as Error, requestType);

      // Try fallback if enabled
      if (this.config.enableFallbacks) {
        const fallbackResult = this.errorHandler.handleUnexpectedError(
          error as Error,
          requestType,
          request
        );

        if (fallbackResult.fallbackData) {
          this.stats.fallbacksUsed++;
          warnings.push('Using fallback data due to unexpected error');
          
          return this.createSuccessResult(
            fallbackResult.fallbackData,
            startTime,
            {
              validationResult,
              securityResult,
              fallbackUsed: true,
              warnings
            }
          );
        }
      }

      return this.createFailureResult(
        (error as Error).message,
        this.errorHandler.categorizeError(error as Error),
        startTime,
        { validationResult, securityResult, warnings }
      );
    }
  }

  /**
   * Create success result
   */
  private createSuccessResult<T>(
    data: T,
    startTime: number,
    metadata: any = {}
  ): EdgeCaseHandlerResult<T> {
    const processingTime = Date.now() - startTime;
    this.updateAverageProcessingTime(processingTime);

    return {
      success: true,
      data,
      processingTime,
      ...metadata
    };
  }

  /**
   * Create failure result
   */
  private createFailureResult(
    error: string,
    errorType: string,
    startTime: number,
    metadata: any = {}
  ): EdgeCaseHandlerResult {
    const processingTime = Date.now() - startTime;
    this.updateAverageProcessingTime(processingTime);

    return {
      success: false,
      error,
      errorType,
      processingTime,
      ...metadata
    };
  }

  /**
   * Update average processing time
   */
  private updateAverageProcessingTime(newTime: number): void {
    const totalRequests = this.stats.totalRequests;
    this.stats.averageProcessingTime = 
      (this.stats.averageProcessingTime * (totalRequests - 1) + newTime) / totalRequests;
  }

  /**
   * Simulate skill assessment processing
   */
  private async processSkillAssessment(request: any): Promise<any> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      id: `assessment-${Date.now()}`,
      userId: request.userId,
      skillIds: request.skillIds || [],
      overallScore: Math.floor(Math.random() * 10) + 1,
      averageConfidence: Math.floor(Math.random() * 10) + 1,
      skillBreakdown: [],
      recommendations: ['Continue learning', 'Practice more'],
      completedAt: new Date()
    };
  }

  /**
   * Simulate learning path processing
   */
  private async processLearningPath(request: any): Promise<any> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 150));
    
    return {
      id: `path-${Date.now()}`,
      userId: request.userId,
      targetRole: request.targetRole,
      estimatedDuration: request.timeframe || 12,
      phases: [],
      resources: [],
      createdAt: new Date()
    };
  }

  /**
   * Simulate market data processing
   */
  private async processMarketData(request: any): Promise<any> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return {
      skill: request.skill,
      demand: Math.floor(Math.random() * 100),
      supply: Math.floor(Math.random() * 100),
      averageSalary: 50000 + Math.floor(Math.random() * 100000),
      growth: Math.floor(Math.random() * 20),
      lastUpdated: new Date()
    };
  }

  /**
   * Get comprehensive statistics
   */
  getStatistics(): EdgeCaseHandlerStats & {
    validation: any;
    security: any;
    circuitBreaker: any;
    errorHandler: any;
    retryManager: any;
  } {
    return {
      ...this.stats,
      validation: this.inputValidator.getValidationRules('skillAssessment'),
      security: this.securityChecker.getSecurityStats(),
      circuitBreaker: this.circuitBreaker.getStatistics(),
      errorHandler: this.errorHandler.getErrorStatistics(),
      retryManager: this.retryManager.getStatistics()
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<EdgeCaseHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): EdgeCaseHandlerConfig {
    return { ...this.config };
  }

  /**
   * Reset all statistics
   */
  resetStatistics(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      validationFailures: 0,
      securityBlocks: 0,
      circuitBreakerTrips: 0,
      retriesPerformed: 0,
      fallbacksUsed: 0,
      averageProcessingTime: 0
    };

    this.errorHandler.clearStatistics();
    this.retryManager.resetStatistics();
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    healthy: boolean;
    details: any;
  }> {
    const circuitBreakerHealth = this.circuitBreaker.getHealthStatus();
    const errorStats = this.errorHandler.getErrorStatistics();
    
    const healthy = 
      circuitBreakerHealth.unhealthy.length === 0 &&
      errorStats.errorRate < 10; // Less than 10% error rate

    return {
      healthy,
      details: {
        circuitBreaker: circuitBreakerHealth,
        errorRate: errorStats.errorRate,
        totalRequests: this.stats.totalRequests,
        averageProcessingTime: this.stats.averageProcessingTime
      }
    };
  }
}

// Export singleton instance
export const refactoredEdgeCaseHandler = new RefactoredEdgeCaseHandler();
