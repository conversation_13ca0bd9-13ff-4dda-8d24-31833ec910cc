/**
 * Error Handler Module
 * Handles error categorization, recovery strategies, and fallback data
 */

export interface ErrorResult {
  success: boolean;
  error?: string;
  errorType?: string;
  fallbackData?: any;
  retryable?: boolean;
  retryAfter?: number;
  retryCount?: number;
  suggestedAlternatives?: any[];
  partialResults?: any;
  correctedData?: any;
}

export interface ErrorStatistics {
  totalErrors: number;
  errorsByType: Record<string, number>;
  mostCommonError: string;
  errorRate: number;
  recentErrors: Array<{
    timestamp: Date;
    error: string;
    type: string;
    service: string;
  }>;
}

export interface FallbackConfig {
  enableFallbacks: boolean;
  fallbackTimeout: number;
  maxFallbackAttempts: number;
}

export class ErrorHandler {
  private errorStats: Map<string, number> = new Map();
  private recentErrors: Array<{
    timestamp: Date;
    error: string;
    type: string;
    service: string;
  }> = [];
  private totalRequests = 0;

  private readonly fallbackConfig: FallbackConfig = {
    enableFallbacks: true,
    fallbackTimeout: 5000,
    maxFallbackAttempts: 3
  };

  /**
   * Categorize error based on message and context
   */
  categorizeError(error: string | Error, context?: string): string {
    const errorMessage = error instanceof Error ? error.message : error;
    const lowerMessage = errorMessage.toLowerCase();

    // Network and connectivity errors
    if (lowerMessage.includes('network') || lowerMessage.includes('connection') || 
        lowerMessage.includes('timeout') || lowerMessage.includes('econnrefused')) {
      return 'TIMEOUT_ERROR';
    }

    // Authentication and authorization errors
    if (lowerMessage.includes('unauthorized') || lowerMessage.includes('forbidden') ||
        lowerMessage.includes('authentication') || lowerMessage.includes('token')) {
      return 'AUTHENTICATION_ERROR';
    }

    // Validation errors
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid') ||
        lowerMessage.includes('required') || lowerMessage.includes('missing')) {
      return 'VALIDATION_ERROR';
    }

    // Rate limiting errors
    if (lowerMessage.includes('rate limit') || lowerMessage.includes('too many requests') ||
        lowerMessage.includes('quota exceeded')) {
      return 'RESOURCE_ERROR';
    }

    // AI service specific errors
    if (lowerMessage.includes('ai') || lowerMessage.includes('gemini') ||
        lowerMessage.includes('openai') || lowerMessage.includes('model')) {
      return 'AI_SERVICE_ERROR';
    }

    // Database errors
    if (lowerMessage.includes('database') || lowerMessage.includes('sql') ||
        lowerMessage.includes('prisma') || lowerMessage.includes('connection pool')) {
      return 'SYSTEM_ERROR';
    }

    // Business logic errors
    if (lowerMessage.includes('not found') || lowerMessage.includes('already exists') ||
        lowerMessage.includes('conflict') || lowerMessage.includes('business rule')) {
      return 'BUSINESS_LOGIC_ERROR';
    }

    // Data consistency errors
    if (lowerMessage.includes('inconsistent') || lowerMessage.includes('corrupt') ||
        lowerMessage.includes('mismatch') || lowerMessage.includes('integrity')) {
      return 'DATA_CONSISTENCY_ERROR';
    }

    // Security errors
    if (lowerMessage.includes('security') || lowerMessage.includes('injection') ||
        lowerMessage.includes('xss') || lowerMessage.includes('csrf')) {
      return 'SECURITY_ERROR';
    }

    // Parsing errors
    if (lowerMessage.includes('parse') || lowerMessage.includes('json') ||
        lowerMessage.includes('syntax') || lowerMessage.includes('format')) {
      return 'PARSING_ERROR';
    }

    // Concurrency errors
    if (lowerMessage.includes('deadlock') || lowerMessage.includes('lock') ||
        lowerMessage.includes('concurrent') || lowerMessage.includes('race condition')) {
      return 'CONCURRENCY_ERROR';
    }

    // Default to system error
    return 'SYSTEM_ERROR';
  }

  /**
   * Check if error is retryable
   */
  isRetryableError(error: string | Error): boolean {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorType = this.categorizeError(errorMessage);

    // Retryable error types
    const retryableTypes = [
      'TIMEOUT_ERROR',
      'RESOURCE_ERROR',
      'AI_SERVICE_ERROR',
      'SYSTEM_ERROR',
      'CONCURRENCY_ERROR'
    ];

    // Non-retryable error types
    const nonRetryableTypes = [
      'VALIDATION_ERROR',
      'AUTHENTICATION_ERROR',
      'SECURITY_ERROR',
      'BUSINESS_LOGIC_ERROR'
    ];

    if (nonRetryableTypes.includes(errorType)) {
      return false;
    }

    if (retryableTypes.includes(errorType)) {
      return true;
    }

    // Check specific error messages
    const lowerMessage = errorMessage.toLowerCase();
    
    // Definitely retryable
    if (lowerMessage.includes('temporary') || lowerMessage.includes('retry') ||
        lowerMessage.includes('service unavailable') || lowerMessage.includes('timeout')) {
      return true;
    }

    // Definitely not retryable
    if (lowerMessage.includes('invalid') || lowerMessage.includes('forbidden') ||
        lowerMessage.includes('not found') || lowerMessage.includes('unauthorized')) {
      return false;
    }

    // Default to retryable for unknown errors
    return true;
  }

  /**
   * Get retry delay based on error type and attempt count
   */
  getRetryDelay(error: string | Error, attemptCount: number): number {
    const errorType = this.categorizeError(error);
    const baseDelay = 1000; // 1 second

    // Exponential backoff with jitter
    const exponentialDelay = baseDelay * Math.pow(2, attemptCount - 1);
    const jitter = Math.random() * 0.1 * exponentialDelay;
    
    let delay = exponentialDelay + jitter;

    // Adjust delay based on error type
    switch (errorType) {
      case 'RESOURCE_ERROR': // Rate limiting
        delay = Math.max(delay, 60000); // At least 1 minute
        break;
      case 'AI_SERVICE_ERROR':
        delay = Math.max(delay, 5000); // At least 5 seconds
        break;
      case 'TIMEOUT_ERROR':
        delay = Math.min(delay, 30000); // Max 30 seconds
        break;
      case 'CONCURRENCY_ERROR':
        delay = Math.min(delay, 5000); // Max 5 seconds
        break;
    }

    // Cap maximum delay at 5 minutes
    return Math.min(delay, 300000);
  }

  /**
   * Record error for statistics
   */
  recordError(error: Error | string, service: string = 'unknown'): void {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorType = this.categorizeError(errorMessage);

    // Update error statistics
    this.errorStats.set(errorType, (this.errorStats.get(errorType) || 0) + 1);

    // Add to recent errors
    this.recentErrors.push({
      timestamp: new Date(),
      error: errorMessage,
      type: errorType,
      service
    });

    // Keep only last 100 errors
    if (this.recentErrors.length > 100) {
      this.recentErrors = this.recentErrors.slice(-100);
    }
  }

  /**
   * Handle unexpected errors with comprehensive fallback
   */
  handleUnexpectedError(error: Error, service: string, originalRequest?: any): ErrorResult {
    this.recordError(error, service);

    const errorType = this.categorizeError(error.message);
    const isRetryable = this.isRetryableError(error.message);

    return {
      success: false,
      error: error.message,
      errorType,
      retryable: isRetryable,
      retryAfter: isRetryable ? this.getRetryDelay(error, 1) : undefined,
      fallbackData: this.getFallbackData(service, originalRequest),
      suggestedAlternatives: this.getSuggestedAlternatives(service, error.message)
    };
  }

  /**
   * Get fallback data based on service type
   */
  getFallbackData(service: string, originalRequest?: any): any {
    switch (service) {
      case 'skillAssessment':
        return this.getFallbackSkillAssessmentData(originalRequest);
      case 'learningPath':
        return this.getFallbackLearningPathData(originalRequest);
      case 'marketData':
        return this.getFallbackMarketData(originalRequest);
      default:
        return null;
    }
  }

  /**
   * Get fallback skill assessment data
   */
  private getFallbackSkillAssessmentData(request?: any): any {
    return {
      id: 'fallback-assessment',
      userId: request?.userId || 'unknown',
      overallScore: 5,
      averageConfidence: 5,
      skillBreakdown: [],
      recommendations: ['Complete a more detailed assessment when service is available'],
      message: 'Using simplified assessment results',
      isStale: true,
      source: 'fallback'
    };
  }

  /**
   * Get fallback learning path data
   */
  private getFallbackLearningPathData(request?: any): any {
    return {
      id: 'fallback-path',
      userId: request?.userId || 'unknown',
      targetRole: request?.targetRole || 'General Developer',
      estimatedDuration: 12,
      phases: [
        {
          name: 'Foundation',
          duration: 4,
          skills: ['Basic Programming', 'Problem Solving']
        },
        {
          name: 'Intermediate',
          duration: 4,
          skills: ['Framework Knowledge', 'Best Practices']
        },
        {
          name: 'Advanced',
          duration: 4,
          skills: ['System Design', 'Leadership']
        }
      ],
      resources: [],
      message: 'Using simplified learning path',
      isStale: true,
      source: 'fallback'
    };
  }

  /**
   * Get fallback market data
   */
  private getFallbackMarketData(request?: any): any {
    return {
      skill: request?.skill || 'unknown',
      demand: 50,
      supply: 50,
      averageSalary: 75000,
      growth: 5,
      difficulty: 5,
      timeToLearn: 12,
      category: 'General',
      lastUpdated: new Date(),
      isStale: true,
      source: 'fallback'
    };
  }

  /**
   * Get suggested alternatives based on service and error
   */
  getSuggestedAlternatives(service: string, error: string): any[] {
    const lowerError = error.toLowerCase();

    if (service === 'skillAssessment') {
      if (lowerError.includes('skill not found')) {
        return ['JavaScript', 'React', 'Node.js', 'Python', 'TypeScript'];
      }
    }

    if (service === 'learningPath') {
      if (lowerError.includes('career path not found')) {
        return ['Full Stack Developer', 'Frontend Developer', 'Backend Developer', 'DevOps Engineer'];
      }
    }

    if (service === 'marketData') {
      if (lowerError.includes('skill not found')) {
        return ['JavaScript', 'Python', 'Java', 'React', 'AWS'];
      }
    }

    return [];
  }

  /**
   * Get error statistics
   */
  getErrorStatistics(): ErrorStatistics {
    const totalErrors = Array.from(this.errorStats.values()).reduce((sum, count) => sum + count, 0);
    const errorsByType = Object.fromEntries(this.errorStats.entries());
    
    // Find most common error
    let mostCommonError = 'None';
    let maxCount = 0;
    for (const [errorType, count] of Array.from(this.errorStats.entries())) {
      if (count > maxCount) {
        maxCount = count;
        mostCommonError = errorType;
      }
    }

    const errorRate = this.totalRequests > 0 ? (totalErrors / this.totalRequests) * 100 : 0;

    return {
      totalErrors,
      errorsByType,
      mostCommonError,
      errorRate,
      recentErrors: this.recentErrors.slice(-10) // Last 10 errors
    };
  }

  /**
   * Clear error statistics
   */
  clearStatistics(): void {
    this.errorStats.clear();
    this.recentErrors = [];
    this.totalRequests = 0;
  }

  /**
   * Increment total requests counter
   */
  incrementRequests(): void {
    this.totalRequests++;
  }

  /**
   * Update fallback configuration
   */
  updateFallbackConfig(config: Partial<FallbackConfig>): void {
    Object.assign(this.fallbackConfig, config);
  }

  /**
   * Get current fallback configuration
   */
  getFallbackConfig(): FallbackConfig {
    return { ...this.fallbackConfig };
  }
}
