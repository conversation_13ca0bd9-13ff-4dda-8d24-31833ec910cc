import { PrismaClient } from '@prisma/client';
interface QueryMetrics {
  query: string;
  duration: number;
  timestamp: number;
  params?: any;
  stackTrace?: string;
}

interface N1QueryPattern {
  baseQuery: string;
  count: number;
  totalDuration: number;
  queries: QueryMetrics[];
  detectedAt: number;
}

class DatabaseMonitor {
  private static instance: DatabaseMonitor;
  private queryLog: QueryMetrics[] = [];
  private n1Patterns: Map<string, N1QueryPattern> = new Map();
  private isMonitoring = false;
  private maxLogSize = 1000;
  private n1DetectionWindow = 5000; // 5 seconds
  private n1Threshold = 3; // 3+ similar queries = potential N+1

  static getInstance(): DatabaseMonitor {
    if (!DatabaseMonitor.instance) {
      DatabaseMonitor.instance = new DatabaseMonitor();
    }
    return DatabaseMonitor.instance;
  }

  startMonitoring(prisma: PrismaClient): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    console.log('[DB-Monitor] Database query monitoring started');

    // Hook into Prisma query events
    // Note: Prisma query events are only available in development with specific log levels
    try {
      (prisma as any).$on('query', (e: any) => {
        this.logQuery({
          query: e.query,
          duration: e.duration,
          timestamp: Date.now(),
          params: e.params,
          stackTrace: this.captureStackTrace(),
        });
      });
    } catch (error) {
      console.warn('[DB-Monitor] Could not hook into Prisma query events:', error);
    }
  }

  stopMonitoring(): void {
    this.isMonitoring = false;
    console.log('[DB-Monitor] Database query monitoring stopped');
  }

  private logQuery(metrics: QueryMetrics): void {
    // Add to query log
    this.queryLog.push(metrics);

    // Maintain log size
    if (this.queryLog.length > this.maxLogSize) {
      this.queryLog.shift();
    }

    // Check for N+1 patterns
    this.detectN1Patterns(metrics);
  }

  private detectN1Patterns(newQuery: QueryMetrics): void {
    const now = Date.now();
    const windowStart = now - this.n1DetectionWindow;

    // Get recent queries within detection window
    const recentQueries = this.queryLog.filter(q => q.timestamp >= windowStart);

    // Normalize query for pattern matching (remove specific IDs, values)
    const normalizedQuery = this.normalizeQuery(newQuery.query);

    // Group similar queries
    const similarQueries = recentQueries.filter(q => 
      this.normalizeQuery(q.query) === normalizedQuery
    );

    if (similarQueries.length >= this.n1Threshold) {
      const patternKey = normalizedQuery;
      
      if (!this.n1Patterns.has(patternKey)) {
        const pattern: N1QueryPattern = {
          baseQuery: normalizedQuery,
          count: similarQueries.length,
          totalDuration: similarQueries.reduce((sum, q) => sum + q.duration, 0),
          queries: similarQueries,
          detectedAt: now,
        };

        this.n1Patterns.set(patternKey, pattern);
        
        console.warn(`[DB-Monitor] 🚨 Potential N+1 Query Detected:`, {
          pattern: normalizedQuery,
          count: similarQueries.length,
          totalDuration: `${pattern.totalDuration}ms`,
          avgDuration: `${(pattern.totalDuration / similarQueries.length).toFixed(2)}ms`,
          stackTrace: newQuery.stackTrace,
        });
      } else {
        // Update existing pattern
        const pattern = this.n1Patterns.get(patternKey)!;
        pattern.count = similarQueries.length;
        pattern.totalDuration = similarQueries.reduce((sum, q) => sum + q.duration, 0);
        pattern.queries = similarQueries;
      }
    }
  }

  private normalizeQuery(query: string): string {
    return query
      // Remove specific UUIDs
      .replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, 'UUID')
      // Remove specific numbers
      .replace(/\b\d+\b/g, 'NUM')
      // Remove quoted strings
      .replace(/'[^']*'/g, 'STRING')
      .replace(/"[^"]*"/g, 'STRING')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim();
  }

  private captureStackTrace(): string {
    const stack = new Error().stack || '';
    const lines = stack.split('\n');
    
    // Filter out internal Node.js and Prisma frames
    const relevantLines = lines.filter(line => 
      !line.includes('node_modules') &&
      !line.includes('internal/') &&
      !line.includes('DatabaseMonitor') &&
      line.includes('at ')
    );

    return relevantLines.slice(0, 5).join('\n');
  }

  getQueryMetrics(): {
    totalQueries: number;
    averageDuration: number;
    slowQueries: QueryMetrics[];
    n1Patterns: N1QueryPattern[];
    recentQueries: QueryMetrics[];
  } {
    const slowQueryThreshold = 100; // 100ms
    const slowQueries = this.queryLog.filter(q => q.duration > slowQueryThreshold);
    const totalDuration = this.queryLog.reduce((sum, q) => sum + q.duration, 0);
    const averageDuration = this.queryLog.length > 0 ? totalDuration / this.queryLog.length : 0;

    return {
      totalQueries: this.queryLog.length,
      averageDuration: Math.round(averageDuration * 100) / 100,
      slowQueries: slowQueries.slice(-10), // Last 10 slow queries
      n1Patterns: Array.from(this.n1Patterns.values()),
      recentQueries: this.queryLog.slice(-20), // Last 20 queries
    };
  }

  getN1Patterns(): N1QueryPattern[] {
    return Array.from(this.n1Patterns.values());
  }

  clearMetrics(): void {
    this.queryLog = [];
    this.n1Patterns.clear();
    console.log('[DB-Monitor] Query metrics cleared');
  }

  generateReport(): string {
    const metrics = this.getQueryMetrics();
    
    let report = `
📊 Database Query Report
========================
Total Queries: ${metrics.totalQueries}
Average Duration: ${metrics.averageDuration}ms
Slow Queries (>100ms): ${metrics.slowQueries.length}

`;

    if (metrics.n1Patterns.length > 0) {
      report += `🚨 N+1 Query Patterns Detected: ${metrics.n1Patterns.length}\n`;
      report += `==========================================\n`;
      
      metrics.n1Patterns.forEach((pattern, index) => {
        report += `
${index + 1}. Pattern: ${pattern.baseQuery}
   Count: ${pattern.count} queries
   Total Duration: ${pattern.totalDuration}ms
   Average Duration: ${(pattern.totalDuration / pattern.count).toFixed(2)}ms
   First Detected: ${new Date(pattern.detectedAt).toISOString()}
`;
      });
    }

    if (metrics.slowQueries.length > 0) {
      report += `\n🐌 Recent Slow Queries:\n`;
      report += `=======================\n`;
      
      metrics.slowQueries.slice(-5).forEach((query, index) => {
        report += `
${index + 1}. Duration: ${query.duration}ms
   Query: ${query.query.substring(0, 100)}...
   Time: ${new Date(query.timestamp).toISOString()}
`;
      });
    }

    return report;
  }

  // Method to be called from API endpoints for monitoring
  async monitorQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const duration = Date.now() - startTime;
      
      // Log custom query metrics
      this.logQuery({
        query: `[${queryName}]`,
        duration,
        timestamp: startTime,
        stackTrace: this.captureStackTrace(),
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logQuery({
        query: `[${queryName}] ERROR: ${error}`,
        duration,
        timestamp: startTime,
        stackTrace: this.captureStackTrace(),
      });
      
      throw error;
    }
  }
}

export const databaseMonitor = DatabaseMonitor.getInstance();
export type { QueryMetrics, N1QueryPattern };
