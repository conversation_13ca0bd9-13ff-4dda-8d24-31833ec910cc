/**
 * Advanced Request Deduplication Service
 * Provides intelligent request deduplication with semantic similarity and cross-user matching
 */

interface DeduplicationConfig {
  enableSemanticSimilarity?: boolean;
  enableCrossUserDeduplication?: boolean;
  enablePredictiveWarming?: boolean;
  similarityThreshold?: number;
  deduplicationWindow?: number;
}

interface DeduplicationOptions {
  userId?: string;
  enableCrossUserMatch?: boolean;
}

interface DeduplicationMetrics {
  totalRequests: number;
  deduplicatedRequests: number;
  cacheHits: number;
  deduplicationSavings: number;
  averageResponseTime: number;
}

interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
  userId?: string;
}

export class AdvancedRequestDeduplicationService {
  private config: Required<DeduplicationConfig>;
  private pendingRequests = new Map<string, PendingRequest>();
  private cache = new Map<string, { data: any; timestamp: number; userId?: string }>();
  private metrics: DeduplicationMetrics = {
    totalRequests: 0,
    deduplicatedRequests: 0,
    cacheHits: 0,
    deduplicationSavings: 0,
    averageResponseTime: 0
  };

  // Safe request types for cross-user deduplication
  private readonly crossUserSafeTypes = [
    'career-recommendations',
    'skill-trends',
    'industry-insights',
    'general-advice'
  ];

  constructor(config: DeduplicationConfig = {}) {
    this.config = {
      enableSemanticSimilarity: config.enableSemanticSimilarity ?? true,
      enableCrossUserDeduplication: config.enableCrossUserDeduplication ?? false,
      enablePredictiveWarming: config.enablePredictiveWarming ?? false,
      similarityThreshold: config.similarityThreshold ?? 0.8,
      deduplicationWindow: config.deduplicationWindow ?? 30000 // 30 seconds
    };
  }

  async deduplicateRequest<T>(
    requestKey: string,
    requestFunction: () => Promise<T>,
    options: DeduplicationOptions = {}
  ): Promise<T> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    // Check for exact match in pending requests
    const pending = this.pendingRequests.get(requestKey);
    if (pending && this.isWithinWindow(pending.timestamp)) {
      this.metrics.deduplicatedRequests++;
      return pending.promise;
    }

    // Check cache for exact match
    const cached = this.cache.get(requestKey);
    if (cached && this.isWithinWindow(cached.timestamp)) {
      this.metrics.cacheHits++;
      return cached.data;
    }

    // Check for cross-user deduplication if enabled
    if (this.config.enableCrossUserDeduplication && options.enableCrossUserMatch) {
      const crossUserResult = this.findCrossUserMatch(requestKey, options.userId);
      if (crossUserResult) {
        this.metrics.deduplicatedRequests++;
        return crossUserResult;
      }
    }

    // Check for semantic similarity if enabled
    if (this.config.enableSemanticSimilarity) {
      const similarResult = this.findSimilarRequest(requestKey);
      if (similarResult) {
        this.metrics.deduplicatedRequests++;
        return similarResult;
      }
    }

    // Execute new request
    const promise = requestFunction().then((result) => {
      const responseTime = Date.now() - startTime;
      this.updateMetrics(responseTime);
      
      // Cache the result
      this.cache.set(requestKey, {
        data: result,
        timestamp: Date.now(),
        userId: options.userId
      });

      // Remove from pending
      this.pendingRequests.delete(requestKey);
      
      return result;
    }).catch((error) => {
      this.pendingRequests.delete(requestKey);
      throw error;
    });

    // Store as pending
    this.pendingRequests.set(requestKey, {
      promise,
      timestamp: Date.now(),
      userId: options.userId
    });

    return promise;
  }

  private isWithinWindow(timestamp: number): boolean {
    return Date.now() - timestamp < this.config.deduplicationWindow;
  }

  private findCrossUserMatch(requestKey: string, userId?: string): any | null {
    // Only allow cross-user matching for safe request types
    const requestType = requestKey.split(':')[0];
    if (!this.crossUserSafeTypes.includes(requestType)) {
      return null;
    }

    // Look for cached results from other users
    const cacheEntries = Array.from(this.cache.entries());
    for (const [key, cached] of cacheEntries) {
      if (key === requestKey && cached.userId !== userId && this.isWithinWindow(cached.timestamp)) {
        return cached.data;
      }
    }

    return null;
  }

  private findSimilarRequest(requestKey: string): any | null {
    // Simple similarity check based on key components
    const keyParts = requestKey.split(':');
    const threshold = this.config.similarityThreshold;

    const cacheEntries = Array.from(this.cache.entries());
    for (const [key, cached] of cacheEntries) {
      if (key !== requestKey && this.isWithinWindow(cached.timestamp)) {
        const similarity = this.calculateSimilarity(requestKey, key);
        if (similarity >= threshold) {
          return cached.data;
        }
      }
    }

    return null;
  }

  private calculateSimilarity(key1: string, key2: string): number {
    const parts1 = key1.split(':');
    const parts2 = key2.split(':');

    if (parts1.length !== parts2.length) {
      return 0;
    }

    let matches = 0;
    for (let i = 0; i < parts1.length; i++) {
      if (parts1[i] === parts2[i]) {
        matches++;
      }
    }

    return matches / parts1.length;
  }

  private updateMetrics(responseTime: number): void {
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime + responseTime) / 2;
    
    if (this.metrics.deduplicatedRequests > 0) {
      this.metrics.deduplicationSavings = 
        (this.metrics.deduplicatedRequests / this.metrics.totalRequests) * 100;
    }
  }

  getMetrics(): DeduplicationMetrics {
    return { ...this.metrics };
  }

  clearCache(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  // Cleanup old entries
  cleanup(): void {
    const now = Date.now();
    
    // Clean cache
    const cacheEntries = Array.from(this.cache.entries());
    for (const [key, cached] of cacheEntries) {
      if (!this.isWithinWindow(cached.timestamp)) {
        this.cache.delete(key);
      }
    }

    // Clean pending requests
    const pendingEntries = Array.from(this.pendingRequests.entries());
    for (const [key, pending] of pendingEntries) {
      if (!this.isWithinWindow(pending.timestamp)) {
        this.pendingRequests.delete(key);
      }
    }
  }
}
