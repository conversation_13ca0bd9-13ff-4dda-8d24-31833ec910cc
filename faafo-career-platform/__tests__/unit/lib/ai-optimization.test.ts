/**
 * Phase 2 AI Service Call Optimization Tests
 * 
 * Tests for advanced request deduplication, semantic matching,
 * cross-user optimization, and performance improvements.
 */

import { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';
import { OptimizedAIService } from '@/lib/optimized-ai-service';
// import { AdvancedRequestDeduplicationService } from '@/lib/services/advanced-request-deduplication-service';

// Mock dependencies
jest.mock('@/lib/services/geminiService', () => ({
  geminiService: {
    analyzeSkillsGap: jest.fn(),
    generateCareerRecommendations: jest.fn(),
    generateInterviewQuestions: jest.fn(),
  },
  AIServiceLogger: {
    info: jest.fn(),
    debug: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('@/lib/cache', () => ({
  cacheService: {
    getJSON: jest.fn(),
    setJSON: jest.fn(),
    generateAIKey: jest.fn(),
  },
}));

jest.mock('@/lib/services/enhanced-cache-service', () => ({
  enhancedCacheService: {
    get: jest.fn(),
    set: jest.fn(),
    getMetrics: jest.fn(() => ({
      l1Hits: 10,
      l2Hits: 5,
      sharedCacheHits: 3,
      misses: 2,
      averageResponseTime: 150,
    })),
  },
}));

describe.skip('Advanced Request Deduplication Service', () => {
  let deduplicationService: AdvancedRequestDeduplicationService;
  let mockAIFunction: jest.Mock;

  beforeEach(() => {
    deduplicationService = new AdvancedRequestDeduplicationService({
      enableSemanticSimilarity: true,
      enableCrossUserDeduplication: true,
      enablePredictiveWarming: false, // Disable for testing
      similarityThreshold: 0.85,
      deduplicationWindow: 5000,
    });

    mockAIFunction = jest.fn().mockResolvedValue({
      success: true,
      data: { analysis: 'test result' },
    });

    jest.clearAllMocks();
  });

  describe('Exact Deduplication', () => {
    it('should deduplicate identical requests within the deduplication window', async () => {
      const requestKey = 'skills-analysis:software_engineer:mid_level:javascript,react';

      // First request
      const result1 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });

      // Second identical request should be deduplicated
      const result2 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });

      // Should only call the AI function once
      expect(mockAIFunction).toHaveBeenCalledTimes(1);
      expect(result1).toEqual(result2);

      const metrics = deduplicationService.getMetrics();
      expect(metrics.exactDuplicates).toBe(1);
    });

    it('should not deduplicate requests outside the deduplication window', async () => {
      const requestKey = 'skills-analysis:software_engineer:mid_level:javascript,react';
      
      // First request
      await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });
      
      // Wait for deduplication window to expire (simulate with shorter window)
      const shortWindowService = new AdvancedRequestDeduplicationService({
        deduplicationWindow: 1, // 1ms window
      });
      
      await new Promise(resolve => setTimeout(resolve, 10));
      
      // Second request after window expires
      await shortWindowService.deduplicateRequest(requestKey, mockAIFunction, { userId: 'user1' });
      
      expect(mockAIFunction).toHaveBeenCalledTimes(2);
    });
  });

  describe('Semantic Similarity Deduplication', () => {
    it('should deduplicate semantically similar requests', async () => {
      const requestKey1 = 'skills-analysis:software engineer:mid level:javascript,react';
      const requestKey2 = 'skills-analysis:software_engineer:mid_level:javascript,react';

      // First request
      const result1 = await deduplicationService.deduplicateRequest(requestKey1, mockAIFunction, { userId: 'user1' });

      // Second semantically similar request should be deduplicated
      const result2 = await deduplicationService.deduplicateRequest(requestKey2, mockAIFunction, { userId: 'user2' });

      // Should deduplicate based on semantic similarity
      expect(mockAIFunction).toHaveBeenCalledTimes(1);
      expect(result1).toEqual(result2);

      const metrics = deduplicationService.getMetrics();
      expect(metrics.semanticDuplicates).toBe(1);
    });

    it('should normalize common career path variations', async () => {
      const service = new AdvancedRequestDeduplicationService();
      
      // Test parameter normalization
      const normalized1 = (service as any).normalizeParameters('software engineer:entry level');
      const normalized2 = (service as any).normalizeParameters('software_engineer:entry_level');
      
      expect(normalized1).toBe(normalized2);
    });
  });

  describe('Cross-User Deduplication', () => {
    it('should deduplicate safe requests across users', async () => {
      // Use identical request keys but different users to test cross-user deduplication
      const requestKey = 'career-recommendations:javascript,react';

      mockAIFunction.mockResolvedValue({
        success: true,
        data: { recommendations: ['Frontend Developer', 'React Developer'] },
      });

      // First user makes request
      const result1 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, {
        userId: 'user1',
        enableCrossUserMatch: true
      });

      // Second user makes identical request
      const result2 = await deduplicationService.deduplicateRequest(requestKey, mockAIFunction, {
        userId: 'user2',
        enableCrossUserMatch: true
      });

      // Should reuse result for cross-user safe requests
      expect(mockAIFunction).toHaveBeenCalledTimes(1);
      expect(result1).toEqual(result2);
      expect(result2.success).toBe(true);

      const metrics = deduplicationService.getMetrics();
      // When semantic similarity is enabled, identical requests are caught by semantic deduplication
      expect(metrics.semanticDuplicates).toBe(1);
    });

    it('should use cross-user deduplication when semantic similarity is disabled', async () => {
      // Create service with semantic similarity disabled but cross-user enabled
      const crossUserService = new AdvancedRequestDeduplicationService({
        enableSemanticSimilarity: false,
        enableCrossUserDeduplication: true,
        enablePredictiveWarming: false,
        deduplicationWindow: 5000,
      });

      const requestKey = 'career-recommendations:javascript,react';

      mockAIFunction.mockResolvedValue({
        success: true,
        data: { recommendations: ['Frontend Developer', 'React Developer'] },
      });

      // First user makes request
      const result1 = await crossUserService.deduplicateRequest(requestKey, mockAIFunction, {
        userId: 'user1',
        enableCrossUserMatch: true
      });

      // Second user makes identical request
      const result2 = await crossUserService.deduplicateRequest(requestKey, mockAIFunction, {
        userId: 'user2',
        enableCrossUserMatch: true
      });

      // Should reuse result for cross-user safe requests
      expect(mockAIFunction).toHaveBeenCalledTimes(1);
      expect(result1).toEqual(result2);
      expect(result2.success).toBe(true);

      const metrics = crossUserService.getMetrics();
      expect(metrics.crossUserDuplicates).toBe(1);
      expect(metrics.semanticDuplicates).toBe(0);
    });

    it('should not cross-user deduplicate sensitive request types', async () => {
      // Create service with semantic similarity disabled to test cross-user logic specifically
      const sensitiveService = new AdvancedRequestDeduplicationService({
        enableSemanticSimilarity: false,
        enableCrossUserDeduplication: true,
        enablePredictiveWarming: false,
        deduplicationWindow: 5000,
      });

      // Use a request type that is not in the safe list
      const requestKey = 'skills-analysis:user_specific_data:sensitive';

      mockAIFunction.mockResolvedValue({
        success: true,
        data: { analysis: 'user specific analysis' },
      });

      // First user makes request
      await sensitiveService.deduplicateRequest(requestKey, mockAIFunction, {
        userId: 'user1',
        enableCrossUserMatch: true
      });

      // Second user makes same request
      await sensitiveService.deduplicateRequest(requestKey, mockAIFunction, {
        userId: 'user2',
        enableCrossUserMatch: true
      });

      // Should call AI function twice for sensitive data (no cross-user sharing)
      expect(mockAIFunction).toHaveBeenCalledTimes(2);

      const metrics = sensitiveService.getMetrics();
      expect(metrics.crossUserDuplicates).toBe(0);
    });
  });

  describe('Performance Metrics', () => {
    it('should track comprehensive metrics', async () => {
      const requestKey = 'skills-analysis:test:metrics';

      // Mock function with a small delay to ensure measurable response time
      mockAIFunction.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return {
          success: true,
          data: { analysis: 'test result' },
        };
      });

      await deduplicationService.deduplicateRequest(requestKey, mockAIFunction);
      await deduplicationService.deduplicateRequest(requestKey, mockAIFunction);

      const metrics = deduplicationService.getMetrics();

      expect(metrics.totalRequests).toBe(2);
      expect(metrics.exactDuplicates).toBe(1);
      expect(metrics.averageResponseTime).toBeGreaterThan(0);
    });

    it('should calculate deduplication savings', async () => {
      const requestKey = 'skills-analysis:performance:test';
      
      // Simulate slow AI function
      const slowMockFunction = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ success: true, data: {} }), 100))
      );

      await deduplicationService.deduplicateRequest(requestKey, slowMockFunction);
      await deduplicationService.deduplicateRequest(requestKey, slowMockFunction);
      
      const metrics = deduplicationService.getMetrics();
      expect(metrics.deduplicationSavings).toBeGreaterThan(0);
    });
  });
});

describe('Optimized AI Service', () => {
  let optimizedService: OptimizedAIService;

  beforeEach(() => {
    optimizedService = new OptimizedAIService({
      enableAdvancedDeduplication: true,
      enableIntelligentCaching: true,
      enablePerformanceMonitoring: true,
    });

    jest.clearAllMocks();
  });

  describe('Skills Analysis Optimization', () => {
    it('should use advanced deduplication for skills analysis', async () => {
      const mockGeminiService = require('@/lib/services/geminiService').geminiService;
      mockGeminiService.analyzeSkillsGap.mockResolvedValue({
        success: true,
        data: { skillsGap: ['React', 'TypeScript'] },
      });

      const result = await optimizedService.analyzeSkillsGap(
        ['JavaScript'],
        'Frontend Developer',
        'mid_level',
        {
          userId: 'test-user',
          priority: 'medium',
          enableDeduplication: true,
        }
      );

      expect(result.success).toBe(true);
      expect(result.metadata.source).toBe('deduplication');
      expect(result.metadata.requestId).toBeDefined();
    });

    it('should handle cache hits properly', async () => {
      const mockEnhancedCache = require('@/lib/services/enhanced-cache-service').enhancedCacheService;
      mockEnhancedCache.get.mockResolvedValue({
        skillsGap: ['React', 'TypeScript'],
      });

      const result = await optimizedService.analyzeSkillsGap(
        ['JavaScript'],
        'Frontend Developer',
        'mid_level',
        {
          userId: 'test-user',
          enableCaching: true,
        }
      );

      expect(result.success).toBe(true);
      expect(result.metadata.source).toBe('cache');
      expect(result.metadata.cacheHit).toBe(true);
    });
  });

  describe('Career Recommendations Optimization', () => {
    it('should optimize career recommendation requests', async () => {
      const mockGeminiService = require('@/lib/services/geminiService').geminiService;
      mockGeminiService.generateCareerRecommendations.mockResolvedValue({
        success: true,
        data: { recommendations: ['Frontend Developer', 'Full Stack Developer'] },
      });

      const result = await optimizedService.generateCareerRecommendations(
        ['JavaScript', 'React'],
        { workStyle: 'remote', experience: 'mid_level' },
        {
          userId: 'test-user',
          priority: 'high',
          enableCrossUserMatch: true, // Safe for career recommendations
        }
      );

      expect(result.success).toBe(true);
      expect(result.metadata.requestId).toBeDefined();
    });
  });

  describe('Interview Questions Optimization', () => {
    it('should enable cross-user deduplication for interview questions', async () => {
      const mockGeminiService = require('@/lib/services/geminiService').geminiService;
      mockGeminiService.generateInterviewQuestions.mockResolvedValue({
        success: true,
        data: { questions: ['Tell me about yourself', 'What are your strengths?'] },
      });

      const result = await optimizedService.generateInterviewQuestions(
        {
          sessionType: 'BEHAVIORAL',
          careerPath: 'Frontend Developer',
          experienceLevel: 'mid_level',
        },
        {
          userId: 'test-user',
          enableCrossUserMatch: true, // Interview questions are safe to share
        }
      );

      expect(result.success).toBe(true);
      expect(result.metadata.source).toBe('deduplication');
    });
  });

  describe('Metrics and Monitoring', () => {
    it('should provide comprehensive optimization metrics', () => {
      const metrics = optimizedService.getOptimizationMetrics();
      
      expect(metrics).toHaveProperty('deduplication');
      expect(metrics).toHaveProperty('activeRequests');
      expect(metrics).toHaveProperty('totalRequests');
      expect(metrics).toHaveProperty('config');
    });
  });
});
