/**
 * Skills Analysis Comprehensive Tests
 * 
 * Tests Skills Analysis Comprehensive API endpoints, request/response handling, validation, and error scenarios.
 * 
 * @category unit
 * @requires API mocking, request simulation
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST } from '@/app/api/ai/skills-analysis/comprehensive/route';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { geminiService } from '@/lib/services/geminiService';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    skillAssessment: {
      findMany: jest.fn(),
    },
    careerPath: {
      findFirst: jest.fn(),
    },
    skillGapAnalysis: {
      create: jest.fn(),
    },
  },
}));

jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('@/lib/services/geminiService', () => ({
  geminiService: {
    analyzeComprehensiveSkillGap: jest.fn(),
  },
}));

jest.mock('@/lib/services/consolidated-cache-service', () => ({
  consolidatedCache: {
    get: jest.fn(),
    set: jest.fn(),
    generateAIKey: jest.fn(),
  },
}));

jest.mock('@/lib/auth', () => ({
  authOptions: {},
}));

jest.mock('@/lib/errorHandler', () => ({
  withErrorHandler: (handler: any) => handler,
}));

jest.mock('@/lib/rateLimit', () => ({
  withRateLimit: (request: any, config: any, handler: any) => handler(),
}));

jest.mock('@/lib/csrf', () => ({
  withCSRFProtection: (request: any, handler: any) => handler(),
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockGeminiService = geminiService as jest.Mocked<typeof geminiService>;
const mockConsolidatedCache = consolidatedCache as jest.Mocked<typeof consolidatedCache>;

describe('Comprehensive Skills Analysis API', () => {
  const mockUserId = 'test-user-id';
  
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetServerSession.mockResolvedValue({
      user: { id: mockUserId },
    } as any);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('POST /api/ai/skills-analysis/comprehensive', () => {
    const validRequestData = {
      currentSkills: [
        {
          skillName: 'JavaScript',
          selfRating: 7,
          confidenceLevel: 8,
          yearsOfExperience: 3,
        },
        {
          skillName: 'React',
          selfRating: 6,
          confidenceLevel: 7,
          yearsOfExperience: 2,
        },
      ],
      targetCareerPath: {
        careerPathName: 'Full Stack Developer',
        targetLevel: 'ADVANCED' as const,
      },
      preferences: {
        timeframe: 'ONE_YEAR' as const,
        hoursPerWeek: 10,
        learningStyle: ['VISUAL', 'HANDS_ON'],
        budget: 'FREEMIUM' as const,
        focusAreas: ['Backend Development', 'Database Design'],
      },
      includeMarketData: true,
      includePersonalizedPaths: true,
    };

    it('should perform comprehensive skills analysis successfully', async () => {
      // Arrange
      const mockCareerPathData = {
        id: 'fallback-career-path-id',
        name: 'Full Stack Developer',
        requiredSkills: [],
        learningResources: [],
        learningPaths: [],
      };

      const mockAIResponse = {
        success: true,
        data: {
          skillGaps: [
            {
              skillId: 'skill-2',
              skillName: 'Node.js',
              currentLevel: 3,
              targetLevel: 8,
              gapSeverity: 'HIGH',
              priority: 85,
              estimatedLearningTime: 120,
              marketDemand: 'VERY_HIGH',
              salaryImpact: 15,
            },
            {
              skillId: 'skill-3',
              skillName: 'PostgreSQL',
              currentLevel: 2,
              targetLevel: 7,
              gapSeverity: 'CRITICAL',
              priority: 95,
              estimatedLearningTime: 80,
              marketDemand: 'HIGH',
              salaryImpact: 12,
            },
          ],
          learningPlan: {
            totalEstimatedHours: 200,
            milestones: [
              {
                month: 3,
                skills: ['Node.js Basics'],
                estimatedHours: 60,
                learningPaths: ['Backend Development Path'],
              },
              {
                month: 6,
                skills: ['PostgreSQL Fundamentals'],
                estimatedHours: 40,
                learningPaths: ['Database Design Path'],
              },
            ],
            recommendedResources: [
              {
                resourceId: 'resource-1',
                resourceType: 'COURSE',
                priority: 'HIGH',
                skillsAddressed: ['Node.js'],
                estimatedHours: 60,
              },
            ],
          },
          careerReadiness: {
            currentScore: 65,
            targetScore: 85,
            improvementPotential: 20,
            timeToTarget: 8,
          },
          marketInsights: {
            industryTrends: [
              {
                skill: 'Node.js',
                trend: 'GROWING',
                demandLevel: 'VERY_HIGH',
              },
            ],
            salaryProjections: {
              currentEstimate: 75000,
              targetEstimate: 95000,
              improvementPotential: 26.7,
            },
          },
        },
      };

      const mockSkillGapAnalysis = {
        id: 'analysis-id',
        userId: mockUserId,
        targetCareerPathName: 'Full Stack Developer',
        status: 'ACTIVE',
        createdAt: new Date(),
      };

      mockConsolidatedCache.get.mockResolvedValue(null); // No cache
      mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
      mockPrisma.careerPath.findFirst.mockResolvedValue(mockCareerPathData as any);
      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(mockAIResponse);
      mockPrisma.skillGapAnalysis.create.mockResolvedValue(mockSkillGapAnalysis as any);

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const result = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.analysisId).toBe('analysis-id');
      expect(result.data.skillGaps).toHaveLength(2);
      expect(result.data.skillGaps[0].skillName).toBe('Node.js');
      expect(result.data.skillGaps[1].skillName).toBe('PostgreSQL');
      expect(result.data.learningPlan.totalEstimatedHours).toBe(200);
      expect(result.data.careerReadiness.currentScore).toBe(65);
      expect(result.data.marketInsights).toBeDefined();
      expect(result.cached).toBe(false);

      // Verify AI service was called with correct parameters
      expect(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ skillName: 'JavaScript' }),
          expect.objectContaining({ skillName: 'React' }),
        ]),
        expect.objectContaining({
          careerPathName: 'Full Stack Developer',
          targetLevel: 'ADVANCED',
        }),
        expect.objectContaining({
          timeframe: 'ONE_YEAR',
          hoursPerWeek: 10,
        }),
        expect.objectContaining({
          id: 'fallback-career-path-id',
          name: 'Full Stack Developer',
          requiredSkills: [],
          learningResources: [],
          learningPaths: [],
        }),
        mockUserId
      );

      // Verify skill gap analysis was created
      expect(mockPrisma.skillGapAnalysis.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: mockUserId,
          targetCareerPathName: 'Full Stack Developer',
          experienceLevel: 'ADVANCED',
          timeframe: 'ONE_YEAR',
          status: 'ACTIVE',
        }),
      });
    });

    it('should return cached results when available', async () => {
      // Arrange
      const cachedData = {
        analysisId: 'cached-analysis-id',
        skillGaps: [],
        learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },
        careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },
        generatedAt: '2024-01-01T00:00:00.000Z',
      };

      mockConsolidatedCache.get.mockResolvedValue(cachedData);

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const result = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(cachedData);
      expect(result.cached).toBe(true);
      expect(result.generatedAt).toBe('2024-01-01T00:00:00.000Z');

      // Verify AI service was not called
      expect(mockGeminiService.analyzeComprehensiveSkillGap).not.toHaveBeenCalled();
      expect(mockPrisma.skillGapAnalysis.create).not.toHaveBeenCalled();
    });

    it('should fail with invalid request data', async () => {
      // Arrange
      const invalidRequestData = {
        currentSkills: [], // Empty array - should fail validation
        targetCareerPath: {
          careerPathName: 'A', // Too short
          targetLevel: 'INVALID_LEVEL', // Invalid enum value
        },
        preferences: {
          timeframe: 'INVALID_TIMEFRAME',
          hoursPerWeek: -5, // Negative hours
          learningStyle: [],
          budget: 'INVALID_BUDGET',
          focusAreas: [],
        },
      };

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(invalidRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const result = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid request data');
      expect(result.details).toBeDefined();
      expect(Array.isArray(result.details)).toBe(true);
    });

    it('should handle AI service failures gracefully', async () => {
      // Arrange
      mockConsolidatedCache.get.mockResolvedValue(null);
      mockPrisma.skillAssessment.findMany.mockResolvedValue([]);
      mockPrisma.careerPath.findFirst.mockResolvedValue(null);
      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({
        success: false,
        error: 'AI service temporarily unavailable',
      });

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const result = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(result.success).toBe(false);
      expect(result.error).toBe('AI service temporarily unavailable');
    });

    it('should require authentication', async () => {
      // Arrange
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const result = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(result.success).toBe(false);
      expect(result.error.code).toBe('UNAUTHORIZED');
      expect(result.error.message).toBe('Authentication required');
    });

    it('should merge existing skill assessments with current skills', async () => {
      // Arrange
      const existingAssessments = [
        {
          skillId: 'skill-1',
          skill: { name: 'TypeScript' },
          selfRating: 8,
          confidenceLevel: 9,
          assessmentDate: new Date(),
          assessmentType: 'SELF_ASSESSMENT',
        },
      ];

      mockConsolidatedCache.get.mockResolvedValue(null);
      mockPrisma.skillAssessment.findMany.mockResolvedValue(existingAssessments as any);
      mockPrisma.careerPath.findFirst.mockResolvedValue(null);
      mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue({
        success: true,
        data: {
          skillGaps: [],
          learningPlan: { totalEstimatedHours: 0, milestones: [], recommendedResources: [] },
          careerReadiness: { currentScore: 70, targetScore: 85, improvementPotential: 15, timeToTarget: 6 },
        },
      });
      mockPrisma.skillGapAnalysis.create.mockResolvedValue({ id: 'analysis-id' } as any);

      const request = new NextRequest('http://localhost/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        body: JSON.stringify(validRequestData),
        headers: { 'Content-Type': 'application/json' },
      });

      // Act
      const response = await POST(request);

      // Assert
      expect(response.status).toBe(200);
      
      // Verify that AI service was called with merged skills (including TypeScript from assessments)
      expect(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ skillName: 'JavaScript' }),
          expect.objectContaining({ skillName: 'React' }),
          expect.objectContaining({ skillName: 'TypeScript' }),
        ]),
        expect.any(Object),
        expect.any(Object),
        expect.objectContaining({
          id: 'fallback-career-path-id',
          name: 'Full Stack Developer',
        }),
        mockUserId
      );
    });
  });
});
