/**
 * Memory leak prevention utilities for tests
 * Provides cleanup functions and resource management
 */

interface MemoryUsage {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
}

interface MemoryCheck {
  initial: MemoryUsage | null;
  current: MemoryUsage;
  increase: number;
  isLeaking: boolean;
}

interface LeakCheck {
  hasLeaks: boolean;
  warnings: string[];
}

/**
 * Clean up timers and intervals
 */
export function cleanupTimers(): void {
  // Clear all timeouts and intervals
  const highestTimeoutId = setTimeout(() => {}, 0);
  for (let i = 0; i <= highestTimeoutId; i++) {
    clearTimeout(i);
  }
  
  const highestIntervalId = setInterval(() => {}, 1000);
  for (let i = 0; i <= highestIntervalId; i++) {
    clearInterval(i);
  }
}

/**
 * Get current memory usage
 */
export function getMemoryUsage(): MemoryUsage | null {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    return process.memoryUsage();
  }
  return null;
}

/**
 * Force garbage collection if available
 */
export function forceGarbageCollection(): void {
  if (typeof global !== 'undefined' && global.gc) {
    global.gc();
  }
}

/**
 * Detect memory leaks
 */
export function detectMemoryLeaks(): LeakCheck {
  const warnings: string[] = [];
  let hasLeaks = false;

  // Check for common leak sources
  if (typeof window !== 'undefined') {
    // Check for event listeners
    const eventListenerCount = Object.keys(window).filter(key => 
      key.startsWith('on') || key.includes('listener')
    ).length;
    
    if (eventListenerCount > 50) {
      warnings.push(`High event listener count: ${eventListenerCount}`);
      hasLeaks = true;
    }
  }

  return { hasLeaks, warnings };
}

/**
 * Comprehensive cleanup function
 */
export function performCleanup(): void {
  cleanupTimers();
  forceGarbageCollection();
  
  // Clear any global test state
  if (typeof global !== 'undefined') {
    // Clear Jest mocks
    if (global.jest) {
      global.jest.clearAllMocks?.();
      global.jest.clearAllTimers?.();
    }
  }
}

/**
 * Create a clean mock that automatically cleans up
 */
export function createCleanMock(): jest.Mock {
  const mock = jest.fn();
  
  // Store reference for cleanup
  if (!(global as any).testMocks) {
    (global as any).testMocks = [];
  }
  (global as any).testMocks.push(mock);
  
  return mock;
}

/**
 * Resource pool for reusing objects
 */
export class ResourcePool<T> {
  private pool: T[] = [];
  
  constructor(
    private createFn: () => T,
    private resetFn?: (resource: T) => T,
    private maxSize: number = 10
  ) {}

  acquire(): T {
    if (this.pool.length > 0) {
      const resource = this.pool.pop()!;
      return this.resetFn ? this.resetFn(resource) : resource;
    }
    return this.createFn();
  }

  release(resource: T): void {
    if (this.pool.length < this.maxSize) {
      this.pool.push(resource);
    }
  }

  clear(): void {
    this.pool.length = 0;
  }
}

/**
 * Setup memory monitoring for tests
 */
export function setupMemoryMonitoring() {
  let initialMemory: MemoryUsage | null = null;

  return {
    start(): MemoryUsage | null {
      initialMemory = getMemoryUsage();
      return initialMemory;
    },

    check(): MemoryCheck | null {
      const current = getMemoryUsage();
      if (!initialMemory || !current) return null;

      const increase = current.heapUsed - initialMemory.heapUsed;
      return {
        initial: initialMemory,
        current,
        increase,
        isLeaking: increase > 50 * 1024 * 1024 // 50MB increase threshold
      };
    },

    cleanup(): MemoryUsage | null {
      performCleanup();
      return getMemoryUsage();
    }
  };
}

/**
 * Jest setup for memory leak prevention
 */
export function setupJestMemoryFixes(): void {
  // Before each test
  beforeEach(() => {
    cleanupTimers();
    forceGarbageCollection();
  });

  // After each test
  afterEach(() => {
    performCleanup();
    
    const leakCheck = detectMemoryLeaks();
    if (leakCheck.hasLeaks) {
      console.warn('⚠️ Memory leak detected:', leakCheck.warnings.join(', '));
    }
  });

  // After all tests
  afterAll(() => {
    performCleanup();
  });
}
