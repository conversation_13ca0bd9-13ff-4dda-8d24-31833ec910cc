#!/usr/bin/env tsx

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

interface TestResult {
  suite: string;
  passed: number;
  failed: number;
  total: number;
  duration: number;
  coverage?: number;
  errors: string[];
}

interface TestReport {
  timestamp: string;
  totalTests: number;
  totalPassed: number;
  totalFailed: number;
  totalDuration: number;
  overallCoverage: number;
  suites: TestResult[];
  summary: string;
  recommendations: string[];
}

class ComprehensiveTestRunner {
  private results: TestResult[] = [];
  private startTime: number = 0;

  async runAllTests(): Promise<TestReport> {
    console.log('🚀 Starting Comprehensive End-to-End Testing Suite');
    console.log('=' .repeat(80));
    
    this.startTime = Date.now();

    // Test suites in order of dependency
    const testSuites = [
      { name: 'Unit Tests', command: 'npm run test:unit', critical: true },
      { name: 'Integration Tests', command: 'npm run test:integration', critical: true },
      { name: 'API Tests', command: 'npm run test:api', critical: true },
      { name: 'Component Tests', command: 'npm run test:components', critical: false },
      { name: 'End-to-End Tests', command: 'npm run test:e2e', critical: false }
    ];

    for (const suite of testSuites) {
      console.log(`\n📋 Running ${suite.name}...`);
      console.log('-' .repeat(50));
      
      try {
        const result = await this.runTestSuite(suite.name, suite.command);
        this.results.push(result);
        
        if (result.failed > 0 && suite.critical) {
          console.log(`❌ Critical test suite failed: ${suite.name}`);
          console.log('Stopping execution due to critical failure.');
          break;
        }
        
        console.log(`✅ ${suite.name} completed: ${result.passed}/${result.total} passed`);
      } catch (error) {
        console.error(`❌ Failed to run ${suite.name}:`, error);
        this.results.push({
          suite: suite.name,
          passed: 0,
          failed: 1,
          total: 1,
          duration: 0,
          errors: [error instanceof Error ? error.message : String(error)]
        });
        
        if (suite.critical) {
          console.log('Stopping execution due to critical failure.');
          break;
        }
      }
    }

    // Run coverage analysis
    console.log('\n📊 Generating Coverage Report...');
    await this.generateCoverageReport();

    // Generate final report
    const report = this.generateFinalReport();
    await this.saveReport(report);
    
    this.displayFinalResults(report);
    
    return report;
  }

  private async runTestSuite(suiteName: string, command: string): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const output = execSync(command, { 
        encoding: 'utf8',
        stdio: 'pipe',
        timeout: 300000 // 5 minutes timeout
      });
      
      const duration = Date.now() - startTime;
      return this.parseTestOutput(suiteName, output, duration);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      // Parse Jest output even from failed runs
      const output = error.stdout || error.stderr || '';
      const result = this.parseTestOutput(suiteName, output, duration);
      
      if (result.total === 0) {
        // If we couldn't parse any results, create a failure record
        return {
          suite: suiteName,
          passed: 0,
          failed: 1,
          total: 1,
          duration,
          errors: [error instanceof Error ? error.message : String(error) || 'Unknown error']
        };
      }
      
      return result;
    }
  }

  private parseTestOutput(suiteName: string, output: string, duration: number): TestResult {
    const result: TestResult = {
      suite: suiteName,
      passed: 0,
      failed: 0,
      total: 0,
      duration,
      errors: []
    };

    // Parse Jest output for test results
    const testResultRegex = /Tests:\s+(\d+)\s+failed,\s+(\d+)\s+passed,\s+(\d+)\s+total/;
    const passedOnlyRegex = /Tests:\s+(\d+)\s+passed,\s+(\d+)\s+total/;
    const failedOnlyRegex = /Tests:\s+(\d+)\s+failed,\s+(\d+)\s+total/;
    
    let match = output.match(testResultRegex);
    if (match) {
      result.failed = parseInt(match[1]);
      result.passed = parseInt(match[2]);
      result.total = parseInt(match[3]);
    } else {
      match = output.match(passedOnlyRegex);
      if (match) {
        result.passed = parseInt(match[1]);
        result.total = parseInt(match[2]);
        result.failed = 0;
      } else {
        match = output.match(failedOnlyRegex);
        if (match) {
          result.failed = parseInt(match[1]);
          result.total = parseInt(match[2]);
          result.passed = 0;
        }
      }
    }

    // Parse coverage if available
    const coverageRegex = /All files\s+\|\s+([\d.]+)/;
    const coverageMatch = output.match(coverageRegex);
    if (coverageMatch) {
      result.coverage = parseFloat(coverageMatch[1]);
    }

    // Extract error messages
    const errorLines = output.split('\n').filter(line => 
      line.includes('FAIL') || 
      line.includes('Error:') || 
      line.includes('TypeError:') ||
      line.includes('ReferenceError:')
    );
    
    result.errors = errorLines.slice(0, 5); // Limit to first 5 errors

    return result;
  }

  private async generateCoverageReport(): Promise<void> {
    try {
      execSync('npm run test:coverage', { 
        encoding: 'utf8',
        stdio: 'pipe',
        timeout: 120000 // 2 minutes timeout
      });
      console.log('✅ Coverage report generated');
    } catch (error) {
      console.log('⚠️  Coverage report generation failed');
    }
  }

  private generateFinalReport(): TestReport {
    const totalDuration = Date.now() - this.startTime;
    const totalTests = this.results.reduce((sum, result) => sum + result.total, 0);
    const totalPassed = this.results.reduce((sum, result) => sum + result.passed, 0);
    const totalFailed = this.results.reduce((sum, result) => sum + result.failed, 0);
    
    const coverageResults = this.results.filter(r => r.coverage !== undefined);
    const overallCoverage = coverageResults.length > 0 
      ? coverageResults.reduce((sum, r) => sum + (r.coverage || 0), 0) / coverageResults.length
      : 0;

    const passRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0;
    
    let summary = '';
    let recommendations: string[] = [];

    if (passRate === 100) {
      summary = '🎉 All tests passed! The application is ready for production.';
    } else if (passRate >= 90) {
      summary = '✅ Most tests passed. Minor issues need attention.';
      recommendations.push('Review and fix the failing tests');
      recommendations.push('Consider increasing test coverage for critical paths');
    } else if (passRate >= 70) {
      summary = '⚠️  Significant test failures detected. Review required.';
      recommendations.push('Priority: Fix failing unit and integration tests');
      recommendations.push('Review API endpoint implementations');
      recommendations.push('Increase test coverage to at least 80%');
    } else {
      summary = '❌ Critical test failures. Application not ready for deployment.';
      recommendations.push('URGENT: Fix all critical test failures');
      recommendations.push('Review core functionality implementation');
      recommendations.push('Implement comprehensive error handling');
      recommendations.push('Increase test coverage significantly');
    }

    if (overallCoverage < 80) {
      recommendations.push(`Increase code coverage from ${overallCoverage.toFixed(1)}% to at least 80%`);
    }

    return {
      timestamp: new Date().toISOString(),
      totalTests,
      totalPassed,
      totalFailed,
      totalDuration,
      overallCoverage,
      suites: this.results,
      summary,
      recommendations
    };
  }

  private async saveReport(report: TestReport): Promise<void> {
    const reportsDir = path.join(__dirname, '..', 'test-reports');
    
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(reportsDir, `test-report-${timestamp}.json`);
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Also save as latest report
    const latestReportPath = path.join(reportsDir, 'latest-test-report.json');
    fs.writeFileSync(latestReportPath, JSON.stringify(report, null, 2));
    
    console.log(`📄 Test report saved to: ${reportPath}`);
  }

  private displayFinalResults(report: TestReport): void {
    console.log('\n' + '=' .repeat(80));
    console.log('🏁 COMPREHENSIVE TEST RESULTS SUMMARY');
    console.log('=' .repeat(80));
    
    console.log(`\n📊 Overall Statistics:`);
    console.log(`   Total Tests: ${report.totalTests}`);
    console.log(`   Passed: ${report.totalPassed} (${((report.totalPassed / report.totalTests) * 100).toFixed(1)}%)`);
    console.log(`   Failed: ${report.totalFailed} (${((report.totalFailed / report.totalTests) * 100).toFixed(1)}%)`);
    console.log(`   Duration: ${(report.totalDuration / 1000).toFixed(2)}s`);
    console.log(`   Coverage: ${report.overallCoverage.toFixed(1)}%`);

    console.log(`\n📋 Test Suite Breakdown:`);
    report.suites.forEach(suite => {
      const passRate = suite.total > 0 ? (suite.passed / suite.total) * 100 : 0;
      const status = passRate === 100 ? '✅' : passRate >= 90 ? '⚠️' : '❌';
      
      console.log(`   ${status} ${suite.suite}: ${suite.passed}/${suite.total} (${passRate.toFixed(1)}%)`);
      
      if (suite.errors.length > 0) {
        console.log(`      Errors: ${suite.errors.slice(0, 2).join(', ')}`);
      }
    });

    console.log(`\n📝 Summary: ${report.summary}`);
    
    if (report.recommendations.length > 0) {
      console.log(`\n💡 Recommendations:`);
      report.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

    console.log('\n' + '=' .repeat(80));
    
    // Exit with appropriate code
    process.exit(report.totalFailed > 0 ? 1 : 0);
  }
}

// Run if called directly
if (require.main === module) {
  const runner = new ComprehensiveTestRunner();
  runner.runAllTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

export default ComprehensiveTestRunner;
