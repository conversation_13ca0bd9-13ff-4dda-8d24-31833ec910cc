/**
 * Comprehensive tests for AI Service Monitoring
 * Tests metrics collection, analytics, and performance insights
 */

import { aiServiceMonitor } from '@/lib/ai-service-monitor';

describe('AI Service Monitoring', () => {
  beforeEach(() => {
    // Reset monitoring state
    (aiServiceMonitor as any).metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      requestsByType: {},
      requestsByUser: {},
      errorsByType: {},
      lastRequestTime: null,
      uptime: Date.now()
    };
    (aiServiceMonitor as any).requestHistory = [];
  });

  describe('Request Tracking', () => {
    it('should record successful requests', () => {
      const operationType = 'resume-analysis';
      const userId = 'test-user-123';
      const responseTime = 1500;

      aiServiceMonitor.recordRequest(operationType, userId, true, responseTime);

      const metrics = aiServiceMonitor.getMetrics();
      
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(1);
      expect(metrics.failedRequests).toBe(0);
      expect(metrics.averageResponseTime).toBe(responseTime);
      expect(metrics.requestsByType[operationType]).toBe(1);
      expect(metrics.requestsByUser[userId]).toBe(1);
    });

    it('should record failed requests', () => {
      const operationType = 'career-recommendations';
      const userId = 'test-user-456';
      const responseTime = 2000;

      aiServiceMonitor.recordRequest(operationType, userId, false, responseTime);

      const metrics = aiServiceMonitor.getMetrics();
      
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(0);
      expect(metrics.failedRequests).toBe(1);
      expect(metrics.averageResponseTime).toBe(responseTime);
    });

    it('should calculate average response time correctly', () => {
      aiServiceMonitor.recordRequest('test-op', 'user1', true, 1000);
      aiServiceMonitor.recordRequest('test-op', 'user2', true, 2000);
      aiServiceMonitor.recordRequest('test-op', 'user3', true, 3000);

      const metrics = aiServiceMonitor.getMetrics();
      
      expect(metrics.averageResponseTime).toBe(2000);
      expect(metrics.totalRequests).toBe(3);
    });

    it('should track requests by operation type', () => {
      aiServiceMonitor.recordRequest('resume-analysis', 'user1', true, 1000);
      aiServiceMonitor.recordRequest('resume-analysis', 'user2', true, 1500);
      aiServiceMonitor.recordRequest('career-recommendations', 'user3', true, 2000);

      const metrics = aiServiceMonitor.getMetrics();
      
      expect(metrics.requestsByType['resume-analysis']).toBe(2);
      expect(metrics.requestsByType['career-recommendations']).toBe(1);
    });

    it('should track requests by user', () => {
      aiServiceMonitor.recordRequest('resume-analysis', 'user1', true, 1000);
      aiServiceMonitor.recordRequest('career-recommendations', 'user1', true, 1500);
      aiServiceMonitor.recordRequest('resume-analysis', 'user2', true, 2000);

      const metrics = aiServiceMonitor.getMetrics();
      
      expect(metrics.requestsByUser['user1']).toBe(2);
      expect(metrics.requestsByUser['user2']).toBe(1);
    });
  });

  describe('Error Tracking', () => {
    it('should record errors by type', () => {
      const errorType = 'timeout';
      const errorMessage = 'Request timeout after 30 seconds';
      const context = { userId: 'test-user', operation: 'resume-analysis' };

      aiServiceMonitor.recordError(errorType, errorMessage, context);

      const metrics = aiServiceMonitor.getMetrics();
      
      expect(metrics.errorsByType[errorType]).toBe(1);
    });

    it('should track multiple error types', () => {
      aiServiceMonitor.recordError('timeout', 'Timeout error', {});
      aiServiceMonitor.recordError('quota', 'Quota exceeded', {});
      aiServiceMonitor.recordError('timeout', 'Another timeout', {});

      const metrics = aiServiceMonitor.getMetrics();
      
      expect(metrics.errorsByType['timeout']).toBe(2);
      expect(metrics.errorsByType['quota']).toBe(1);
    });

    it('should provide error details in analytics', () => {
      const errorContext = { 
        userId: 'test-user', 
        operation: 'resume-analysis',
        requestId: 'req-123'
      };

      aiServiceMonitor.recordError('validation', 'Invalid input', errorContext);

      const analytics = aiServiceMonitor.getUsageAnalytics();
      
      expect(analytics.errorAnalysis).toBeDefined();
      expect(analytics.errorAnalysis.totalErrors).toBe(1);
    });
  });

  describe('Usage Analytics', () => {
    beforeEach(() => {
      // Set up test data
      aiServiceMonitor.recordRequest('resume-analysis', 'user1', true, 1000);
      aiServiceMonitor.recordRequest('resume-analysis', 'user2', true, 1500);
      aiServiceMonitor.recordRequest('career-recommendations', 'user1', true, 2000);
      aiServiceMonitor.recordRequest('interview-questions', 'user3', false, 3000);
    });

    it('should calculate success rate', () => {
      const analytics = aiServiceMonitor.getUsageAnalytics();
      
      expect(analytics.successRate).toBe(0.75); // 3 success out of 4 total
    });

    it('should identify most popular operations', () => {
      const analytics = aiServiceMonitor.getUsageAnalytics();
      
      expect(analytics.popularOperations[0]).toEqual({
        operation: 'resume-analysis',
        count: 2,
        percentage: 50
      });
    });

    it('should identify most active users', () => {
      const analytics = aiServiceMonitor.getUsageAnalytics();
      
      expect(analytics.activeUsers[0]).toEqual({
        userId: 'user1',
        requestCount: 2,
        successRate: 1.0
      });
    });

    it('should provide time-based analytics', () => {
      const analytics = aiServiceMonitor.getUsageAnalytics();
      
      expect(analytics.timeAnalysis).toBeDefined();
      expect(analytics.timeAnalysis.averageResponseTime).toBe(1875); // (1000+1500+2000+3000)/4
    });
  });

  describe('Performance Insights', () => {
    beforeEach(() => {
      // Set up performance test data
      const now = Date.now();
      
      // Simulate requests over time
      for (let i = 0; i < 10; i++) {
        const responseTime = 1000 + (i * 100); // Increasing response times
        aiServiceMonitor.recordRequest('test-op', `user${i}`, true, responseTime);
      }
    });

    it('should detect performance trends', () => {
      const insights = aiServiceMonitor.getPerformanceInsights();
      
      expect(insights.performanceTrend).toBeDefined();
      expect(insights.performanceTrend?.direction).toBe('degrading'); // Response times increasing
    });

    it('should identify bottlenecks', () => {
      // Record some slow operations
      aiServiceMonitor.recordRequest('slow-operation', 'user1', true, 5000);
      aiServiceMonitor.recordRequest('slow-operation', 'user2', true, 6000);

      const insights = aiServiceMonitor.getPerformanceInsights();
      
      expect(insights.bottlenecks).toBeDefined();
      expect(insights.bottlenecks?.length).toBeGreaterThan(0);
    });

    it('should provide optimization recommendations', () => {
      const insights = aiServiceMonitor.getPerformanceInsights();
      
      expect(insights.recommendations).toBeDefined();
      expect(Array.isArray(insights.recommendations)).toBe(true);
    });

    it('should calculate performance scores', () => {
      const insights = aiServiceMonitor.getPerformanceInsights();
      
      expect(insights.performanceScore).toBeDefined();
      expect(insights.performanceScore).toBeGreaterThanOrEqual(0);
      expect(insights.performanceScore).toBeLessThanOrEqual(100);
    });
  });

  describe('Health Monitoring', () => {
    it('should provide system health status', async () => {
      const health = await aiServiceMonitor.getHealthStatus();

      expect(health.status).toBeDefined();
      expect(health.uptime).toBeDefined();
      expect(health.lastRequestTime).toBeDefined();
    });

    it('should detect unhealthy conditions', async () => {
      // Simulate high error rate
      for (let i = 0; i < 10; i++) {
        aiServiceMonitor.recordRequest('test-op', `user${i}`, false, 1000);
      }

      const health = await aiServiceMonitor.getHealthStatus();

      expect(health.status).toBe('unhealthy');
      expect(health.issues).toBeDefined();
      expect(health.issues!.length).toBeGreaterThan(0);
    });

    it('should provide health recommendations', async () => {
      // Simulate performance issues
      for (let i = 0; i < 5; i++) {
        aiServiceMonitor.recordRequest('test-op', `user${i}`, true, 10000); // Very slow
      }

      const health = await aiServiceMonitor.getHealthStatus();

      expect(health.recommendations).toBeDefined();
      expect(health.recommendations!.length).toBeGreaterThan(0);
    });
  });

  describe('Real-time Monitoring', () => {
    it('should track requests in real-time', () => {
      const initialMetrics = aiServiceMonitor.getMetrics();
      
      aiServiceMonitor.recordRequest('real-time-test', 'user1', true, 1000);
      
      const updatedMetrics = aiServiceMonitor.getMetrics();
      
      expect(updatedMetrics.totalRequests).toBe(initialMetrics.totalRequests + 1);
    });

    it('should maintain request history', () => {
      aiServiceMonitor.recordRequest('history-test', 'user1', true, 1000);
      
      const history = (aiServiceMonitor as any).requestHistory;
      
      expect(history.length).toBe(1);
      expect(history[0]).toMatchObject({
        operationType: 'history-test',
        userId: 'user1',
        success: true,
        responseTime: 1000
      });
    });

    it('should limit history size to prevent memory issues', () => {
      // Record more than the limit (assuming limit is 1000)
      for (let i = 0; i < 1200; i++) {
        aiServiceMonitor.recordRequest('test-op', `user${i}`, true, 1000);
      }

      const history = (aiServiceMonitor as any).requestHistory;
      
      expect(history.length).toBeLessThanOrEqual(1000);
    });
  });

  describe('Data Export and Reporting', () => {
    beforeEach(() => {
      // Set up test data for reporting
      aiServiceMonitor.recordRequest('resume-analysis', 'user1', true, 1000);
      aiServiceMonitor.recordRequest('career-recommendations', 'user2', false, 2000);
      aiServiceMonitor.recordError('timeout', 'Request timeout', { userId: 'user2' });
    });

    it('should export metrics in JSON format', () => {
      const exportData = aiServiceMonitor.exportMetrics();
      
      expect(exportData).toBeDefined();
      expect(typeof exportData).toBe('string');
      
      const parsedData = JSON.parse(exportData);
      expect(parsedData.metrics).toBeDefined();
      expect(parsedData.analytics).toBeDefined();
      expect(parsedData.insights).toBeDefined();
    });

    it('should generate summary reports', () => {
      const report = aiServiceMonitor.generateReport();
      
      expect(report.summary).toBeDefined();
      expect(report.summary.totalRequests).toBe(2);
      expect(report.summary.successRate).toBe(0.5);
      expect(report.recommendations).toBeDefined();
    });

    it('should provide time-range filtered reports', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));

      const report = aiServiceMonitor.generateReport(oneHourAgo, now);

      expect(report.timeRange).toBeDefined();
      expect(report.timeRange.start).toBe(oneHourAgo.toISOString());
      expect(report.timeRange.end).toBe(now.toISOString());
    });
  });

  describe('Alert System', () => {
    it('should trigger alerts for high error rates', () => {
      const alertSpy = jest.spyOn(aiServiceMonitor as any, 'triggerAlert');
      
      // Simulate high error rate
      for (let i = 0; i < 10; i++) {
        aiServiceMonitor.recordRequest('test-op', `user${i}`, false, 1000);
      }

      expect(alertSpy).toHaveBeenCalledWith('high_error_rate', expect.any(Object));
    });

    it('should trigger alerts for performance degradation', () => {
      const alertSpy = jest.spyOn(aiServiceMonitor as any, 'triggerAlert');
      
      // Simulate performance degradation
      for (let i = 0; i < 5; i++) {
        aiServiceMonitor.recordRequest('test-op', `user${i}`, true, 15000); // Very slow
      }

      expect(alertSpy).toHaveBeenCalledWith('performance_degradation', expect.any(Object));
    });

    it('should provide alert configuration', () => {
      const config = aiServiceMonitor.getAlertConfig();
      
      expect(config.errorRateThreshold).toBeDefined();
      expect(config.responseTimeThreshold).toBeDefined();
      expect(config.alertCooldown).toBeDefined();
    });
  });
});
