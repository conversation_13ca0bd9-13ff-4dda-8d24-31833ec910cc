/**
 * Comprehensive tests for Consolidated Cache Service
 * Tests caching functionality, fallback mechanisms, and performance
 */

import { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';
import { MemoryCache } from '@/lib/cache';
import Redis from 'ioredis';

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    ttl: jest.fn(),
    ping: jest.fn(),
    on: jest.fn(),
    disconnect: jest.fn(),
    status: 'ready'
  }));
});

// Mock MemoryCache
jest.mock('@/lib/cache', () => ({
  MemoryCache: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    clear: jest.fn(),
    size: 0
  }))
}));

describe('ConsolidatedCacheService', () => {
  let cacheService: ConsolidatedCacheService;
  let mockRedis: jest.Mocked<Redis>;
  let mockMemoryCache: jest.Mocked<MemoryCache>;

  beforeEach(() => {
    jest.clearAllMocks();
    cacheService = new ConsolidatedCacheService();
    mockRedis = (cacheService as any).redis;
    mockMemoryCache = (cacheService as any).memoryCache;
  });

  describe('Initialization', () => {
    it('should initialize with Redis connection', async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      
      await cacheService.initialize();
      
      expect(mockRedis.ping).toHaveBeenCalled();
      expect((cacheService as any).isRedisAvailable).toBe(true);
    });

    it('should fallback to memory cache when Redis fails', async () => {
      mockRedis.ping.mockRejectedValue(new Error('Redis connection failed'));
      
      await cacheService.initialize();
      
      expect((cacheService as any).isRedisAvailable).toBe(false);
      expect((cacheService as any).useMemoryFallback).toBe(true);
    });

    it('should handle Redis connection timeout', async () => {
      mockRedis.ping.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );
      
      await cacheService.initialize();
      
      expect((cacheService as any).isRedisAvailable).toBe(false);
    });
  });

  describe('Cache Operations - Redis Available', () => {
    beforeEach(async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();
    });

    it('should set and get values from Redis', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };
      const ttl = 3600;

      mockRedis.set.mockResolvedValue('OK');
      mockRedis.get.mockResolvedValue(JSON.stringify(value));

      await cacheService.set(key, value, { ttl });
      const result = await cacheService.get(key);

      expect(mockRedis.set).toHaveBeenCalledWith(key, JSON.stringify(value), 'EX', ttl);
      expect(result).toEqual(value);
    });

    it('should delete values from Redis', async () => {
      const key = 'test-key';
      mockRedis.del.mockResolvedValue(1);

      const result = await cacheService.delete(key);

      expect(mockRedis.del).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });

    it('should check if key exists in Redis', async () => {
      const key = 'test-key';
      mockRedis.exists.mockResolvedValue(1);

      const result = await cacheService.exists(key);

      expect(mockRedis.exists).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });

    it('should get TTL from Redis', async () => {
      const key = 'test-key';
      const expectedTtl = 1800;
      mockRedis.ttl.mockResolvedValue(expectedTtl);

      const result = await cacheService.getTTL(key);

      expect(mockRedis.ttl).toHaveBeenCalledWith(key);
      expect(result).toBe(expectedTtl);
    });

    it('should handle Redis operation failures gracefully', async () => {
      const key = 'test-key';
      mockRedis.get.mockRejectedValue(new Error('Redis operation failed'));

      const result = await cacheService.get(key);

      expect(result).toBeNull();
      // Should fallback to memory cache
      expect(mockMemoryCache.get).toHaveBeenCalledWith(key);
    });
  });

  describe('Cache Operations - Memory Fallback', () => {
    beforeEach(async () => {
      mockRedis.ping.mockRejectedValue(new Error('Redis unavailable'));
      await cacheService.initialize();
    });

    it('should set and get values from memory cache', async () => {
      const key = 'test-key';
      const value = { data: 'test-value' };
      const ttl = 3600;

      mockMemoryCache.get.mockReturnValue(value);

      await cacheService.set(key, value, { ttl });
      const result = await cacheService.get(key);

      expect(mockMemoryCache.set).toHaveBeenCalledWith(key, value, ttl * 1000);
      expect(result).toEqual(value);
    });

    it('should delete values from memory cache', async () => {
      const key = 'test-key';
      mockMemoryCache.delete.mockReturnValue(true);

      const result = await cacheService.delete(key);

      expect(mockMemoryCache.delete).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });

    it('should check if key exists in memory cache', async () => {
      const key = 'test-key';
      mockMemoryCache.get.mockReturnValue({ data: 'exists' });

      const result = await cacheService.exists(key);

      expect(mockMemoryCache.get).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });
  });

  describe('Health Check', () => {
    it('should return healthy status when Redis is available', async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();

      const health = await cacheService.healthCheck();

      expect(health.redis).toBe(true);
      expect(health.memory).toBe(true);
      expect(health.status).toBe('healthy');
    });

    it('should return degraded status when using memory fallback', async () => {
      mockRedis.ping.mockRejectedValue(new Error('Redis unavailable'));
      await cacheService.initialize();

      const health = await cacheService.healthCheck();

      expect(health.redis).toBe(false);
      expect(health.memory).toBe(true);
      expect(health.status).toBe('degraded');
    });

    it('should include performance metrics in health check', async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();

      // Simulate some cache operations
      await cacheService.set('test1', 'value1', { ttl: 3600 });
      await cacheService.get('test1');
      await cacheService.get('nonexistent');

      const health = await cacheService.healthCheck();

      expect(health.metrics).toBeDefined();
      expect(health.metrics.hits).toBeDefined();
      expect(health.metrics.misses).toBeDefined();
      expect(health.metrics.hitRate).toBeDefined();
    });
  });

  describe('Cache Key Management', () => {
    beforeEach(async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();
    });

    it('should generate consistent cache keys', () => {
      const key1 = cacheService.generateKey('resume-analysis', 'user123', 'param:value');
      const key2 = cacheService.generateKey('resume-analysis', 'user123', 'param:value');

      expect(key1).toBe(key2);
      expect(key1).toContain('resume-analysis');
      expect(key1).toContain('user123');
    });

    it('should generate different keys for different parameters', () => {
      const key1 = cacheService.generateKey('resume-analysis', 'user123', 'param:value1');
      const key2 = cacheService.generateKey('resume-analysis', 'user123', 'param:value2');

      expect(key1).not.toBe(key2);
    });

    it('should handle cache key invalidation patterns', async () => {
      const pattern = 'user123:*';
      mockRedis.del.mockResolvedValue(5);

      const result = await cacheService.invalidatePattern(pattern);

      expect(result).toBe(5);
    });
  });

  describe('Performance Optimization', () => {
    beforeEach(async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();
    });

    it('should batch multiple cache operations', async () => {
      const operations = [
        { key: 'key1', value: 'value1', ttl: 3600 },
        { key: 'key2', value: 'value2', ttl: 3600 },
        { key: 'key3', value: 'value3', ttl: 3600 }
      ];

      await cacheService.setBatch(operations);

      expect(mockRedis.set).toHaveBeenCalledTimes(3);
    });

    it('should retrieve multiple keys efficiently', async () => {
      const keys = ['key1', 'key2', 'key3'];
      mockRedis.get
        .mockResolvedValueOnce(JSON.stringify('value1'))
        .mockResolvedValueOnce(JSON.stringify('value2'))
        .mockResolvedValueOnce(JSON.stringify('value3'));

      const results = await cacheService.getBatch(keys);

      expect(results).toEqual({
        key1: 'value1',
        key2: 'value2',
        key3: 'value3'
      });
    });

    it('should handle cache warming', async () => {
      const warmupData = [
        { key: 'common-key1', value: 'common-value1', ttl: 7200 },
        { key: 'common-key2', value: 'common-value2', ttl: 7200 }
      ];

      await cacheService.warmup(warmupData);

      expect(mockRedis.set).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Recovery', () => {
    it('should recover from Redis connection loss', async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();

      // Simulate connection loss
      mockRedis.get.mockRejectedValue(new Error('Connection lost'));
      
      const result = await cacheService.get('test-key');

      expect(result).toBeNull();
      expect((cacheService as any).useMemoryFallback).toBe(true);
    });

    it('should attempt Redis reconnection', async () => {
      mockRedis.ping.mockRejectedValue(new Error('Redis unavailable'));
      await cacheService.initialize();

      // Simulate Redis becoming available
      mockRedis.ping.mockResolvedValue('PONG');
      
      await cacheService.checkRedisConnection();

      expect((cacheService as any).isRedisAvailable).toBe(true);
    });

    it('should handle memory cache overflow', async () => {
      mockRedis.ping.mockRejectedValue(new Error('Redis unavailable'));
      await cacheService.initialize();

      // Simulate memory cache at capacity
      mockMemoryCache.set.mockImplementation(() => {
        throw new Error('Memory cache full');
      });

      const result = await cacheService.set('test-key', 'test-value', { ttl: 3600 });

      expect(result).toBe(false);
    });
  });

  describe('Monitoring and Metrics', () => {
    beforeEach(async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();
    });

    it('should track cache hit/miss ratios', async () => {
      // Simulate cache hits and misses
      mockRedis.get
        .mockResolvedValueOnce(JSON.stringify('hit1'))
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(JSON.stringify('hit2'))
        .mockResolvedValueOnce(null);

      await cacheService.get('key1'); // hit
      await cacheService.get('key2'); // miss
      await cacheService.get('key3'); // hit
      await cacheService.get('key4'); // miss

      const metrics = cacheService.getMetrics();

      expect(metrics.hits).toBe(2);
      expect(metrics.misses).toBe(2);
      expect(metrics.hitRate).toBe(0.5);
    });

    it('should track cache operation performance', async () => {
      mockRedis.get.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(JSON.stringify('value')), 10))
      );

      await cacheService.get('test-key');

      const metrics = cacheService.getMetrics();

      expect(metrics.averageResponseTime).toBeGreaterThan(0);
    });

    it('should provide cache size information', async () => {
      const metrics = cacheService.getMetrics();

      expect(metrics.memoryUsage).toBeDefined();
      expect(metrics.keyCount).toBeDefined();
    });
  });

  describe('Cleanup and Maintenance', () => {
    beforeEach(async () => {
      mockRedis.ping.mockResolvedValue('PONG');
      await cacheService.initialize();
    });

    it('should clean up expired entries', async () => {
      await cacheService.cleanup();

      // Should trigger cleanup operations
      expect(mockMemoryCache.clear).toHaveBeenCalled();
    });

    it('should disconnect properly', async () => {
      await cacheService.disconnect();

      expect(mockRedis.disconnect).toHaveBeenCalled();
    });
  });
});
