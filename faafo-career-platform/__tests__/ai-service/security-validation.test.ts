/**
 * Comprehensive tests for AI Service Security Validation
 * Tests input validation, threat detection, and security hardening
 */

import { AIInputValidator } from '@/lib/ai-input-validator';
import { AIServiceLogger } from '@/lib/ai-service-logger';

jest.mock('@/lib/ai-service-logger');

describe('AI Service Security Validation', () => {
  let validator: AIInputValidator;

  beforeEach(() => {
    validator = new AIInputValidator();
    jest.clearAllMocks();
  });

  describe('Input Sanitization', () => {
    it('should sanitize HTML tags', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello World';
      const result = validator.sanitizeInput(maliciousInput);
      
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
      expect(result).toContain('Hello World');
    });

    it('should remove SQL injection attempts', () => {
      const sqlInjection = "'; DROP TABLE users; --";
      const result = validator.sanitizeInput(sqlInjection);
      
      expect(result).not.toContain('DROP TABLE');
      expect(result).not.toContain('--');
    });

    it('should handle JavaScript injection attempts', () => {
      const jsInjection = 'javascript:void(0)';
      const result = validator.sanitizeInput(jsInjection);
      
      expect(result).not.toContain('javascript:');
    });

    it('should preserve legitimate content', () => {
      const legitimateInput = 'John Doe\nSoftware Engineer\nSkills: JavaScript, React';
      const result = validator.sanitizeInput(legitimateInput);
      
      expect(result).toBe(legitimateInput);
    });
  });

  describe('Threat Detection', () => {
    it('should detect XSS attempts', () => {
      const xssAttempts = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert(1)',
        '<svg onload="alert(1)">',
        '<iframe src="javascript:alert(1)"></iframe>'
      ];

      xssAttempts.forEach(attempt => {
        const threats = validator.detectThreats(attempt);
        expect(threats).toContain('Potential XSS detected');
      });
    });

    it('should detect SQL injection attempts', () => {
      const sqlAttempts = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "UNION SELECT * FROM users",
        "'; DELETE FROM users WHERE '1'='1",
        "' OR 1=1 --"
      ];

      sqlAttempts.forEach(attempt => {
        const threats = validator.detectThreats(attempt);
        expect(threats).toContain('Potential SQL injection detected');
      });
    });

    it('should detect code injection attempts', () => {
      const codeAttempts = [
        'eval("malicious code")',
        'require("fs").readFileSync("/etc/passwd")',
        'process.exit(1)',
        '__import__("os").system("rm -rf /")',
        'exec("malicious command")'
      ];

      codeAttempts.forEach(attempt => {
        const threats = validator.detectThreats(attempt);
        expect(threats).toContain('Potential code injection detected');
      });
    });

    it('should detect path traversal attempts', () => {
      const pathAttempts = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32',
        '/etc/shadow',
        'C:\\Windows\\System32\\config\\SAM'
      ];

      pathAttempts.forEach(attempt => {
        const threats = validator.detectThreats(attempt);
        expect(threats).toContain('Potential path traversal detected');
      });
    });

    it('should not flag legitimate content', () => {
      const legitimateInputs = [
        'John Doe\nSoftware Engineer',
        'Experience with JavaScript and React',
        'Email: <EMAIL>',
        'Phone: (*************',
        'Skills: Python, SQL databases, API development'
      ];

      legitimateInputs.forEach(input => {
        const threats = validator.detectThreats(input);
        expect(threats).toHaveLength(0);
      });
    });
  });

  describe('Resume Validation', () => {
    it('should validate legitimate resume content', () => {
      const validResume = `
        John Doe
        Software Engineer
        Email: <EMAIL>
        Phone: (*************
        
        EXPERIENCE:
        Senior Developer at TechCorp (2020-2024)
        - Developed web applications using React and Node.js
        - Led team of 5 developers
        
        SKILLS:
        JavaScript, Python, SQL, AWS, Docker
        
        EDUCATION:
        Bachelor of Computer Science
        University of Technology (2016-2020)
      `;

      const result = validator.validateResumeInput(validResume);
      
      expect(result.isValid).toBe(true);
      expect(result.threats).toHaveLength(0);
    });

    it('should reject resume with malicious content', () => {
      const maliciousResume = `
        John Doe
        <script>alert("xss")</script>
        Software Engineer
        '; DROP TABLE users; --
      `;

      const result = validator.validateResumeInput(maliciousResume);
      
      expect(result.isValid).toBe(false);
      expect(result.threats?.length).toBeGreaterThan(0);
    });

    it('should handle empty or invalid resume', () => {
      const emptyResume = '';
      const result = validator.validateResumeInput(emptyResume);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Resume content cannot be empty');
    });

    it('should handle extremely long resume content', () => {
      const longResume = 'A'.repeat(100000); // 100KB of text
      const result = validator.validateResumeInput(longResume);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Resume content too long');
    });
  });

  describe('Skills Validation', () => {
    it('should validate legitimate skills array', () => {
      const validSkills = ['JavaScript', 'React', 'Node.js', 'Python', 'SQL'];
      const result = validator.validateSkillsInput(validSkills);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedSkills).toEqual(validSkills);
    });

    it('should sanitize skills with malicious content', () => {
      const maliciousSkills = [
        'JavaScript',
        '<script>alert("xss")</script>',
        'React',
        "'; DROP TABLE skills; --"
      ];

      const result = validator.validateSkillsInput(maliciousSkills);
      
      expect(result.isValid).toBe(false);
      expect(result.threats?.length).toBeGreaterThan(0);
    });

    it('should handle empty skills array', () => {
      const result = validator.validateSkillsInput([]);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Skills array cannot be empty');
    });

    it('should limit number of skills', () => {
      const tooManySkills = Array(101).fill('JavaScript'); // 101 skills
      const result = validator.validateSkillsInput(tooManySkills);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Too many skills');
    });
  });

  describe('Interview Parameters Validation', () => {
    it('should validate legitimate interview parameters', () => {
      const validParams = {
        sessionType: 'TECHNICAL_PRACTICE',
        careerPath: 'Software Engineer',
        experienceLevel: 'SENIOR',
        difficulty: 'INTERMEDIATE',
        count: 5,
        focusAreas: ['technical', 'problem-solving']
      };

      const result = validator.validateInterviewParams(validParams);
      
      expect(result.isValid).toBe(true);
    });

    it('should reject invalid session types', () => {
      const invalidParams = {
        sessionType: '<script>alert("xss")</script>',
        careerPath: 'Software Engineer'
      };

      const result = validator.validateInterviewParams(invalidParams);
      
      expect(result.isValid).toBe(false);
      expect(result.threats?.length).toBeGreaterThan(0);
    });

    it('should validate question count limits', () => {
      const invalidParams = {
        sessionType: 'TECHNICAL_PRACTICE',
        careerPath: 'Software Engineer',
        count: 1000 // Too many questions
      };

      const result = validator.validateInterviewParams(invalidParams);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Question count too high');
    });
  });

  describe('Content Filtering', () => {
    it('should detect inappropriate content', () => {
      const inappropriateContent = [
        'This contains profanity and inappropriate language',
        'Violent content that should be blocked',
        'Adult content that is not suitable'
      ];

      inappropriateContent.forEach(content => {
        const result = validator.validateContent(content);
        // Note: This would require actual content filtering implementation
        expect(result).toBeDefined();
      });
    });

    it('should allow professional content', () => {
      const professionalContent = [
        'Software development experience',
        'Project management skills',
        'Technical expertise in cloud computing'
      ];

      professionalContent.forEach(content => {
        const result = validator.validateContent(content);
        expect(result.isValid).toBe(true);
      });
    });
  });

  describe('Rate Limiting Validation', () => {
    it('should track validation attempts per user', () => {
      const userId = 'test-user-validation';
      
      for (let i = 0; i < 5; i++) {
        validator.validateResumeInput('Test resume', userId);
      }

      // Should track validation attempts
      expect(AIServiceLogger.debug).toHaveBeenCalledWith(
        'Validation attempt tracked',
        expect.objectContaining({ userId })
      );
    });

    it('should detect rapid validation attempts', () => {
      const userId = 'test-user-rapid';
      
      // Simulate rapid requests
      for (let i = 0; i < 20; i++) {
        validator.validateResumeInput('Test resume', userId);
      }

      expect(AIServiceLogger.warn).toHaveBeenCalledWith(
        'High validation frequency detected',
        expect.objectContaining({ userId })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle validation errors gracefully', () => {
      // Test with null input
      const result = validator.validateResumeInput(null as any);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle undefined input', () => {
      const result = validator.validateResumeInput(undefined as any);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle non-string input', () => {
      const result = validator.validateResumeInput(123 as any);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid input type');
    });
  });

  describe('Logging and Monitoring', () => {
    it('should log security threats', () => {
      const maliciousInput = '<script>alert("xss")</script>';
      validator.validateResumeInput(maliciousInput);

      expect(AIServiceLogger.error).toHaveBeenCalledWith(
        'High-risk content detected',
        expect.objectContaining({
          error: expect.objectContaining({
            threats: expect.arrayContaining(['Potential XSS detected'])
          })
        })
      );
    });

    it('should log validation metrics', () => {
      const validInput = 'John Doe\nSoftware Engineer';
      validator.validateResumeInput(validInput);

      expect(AIServiceLogger.debug).toHaveBeenCalledWith(
        'Input validation completed',
        expect.objectContaining({
          isValid: true,
          inputLength: validInput.length
        })
      );
    });
  });
});
