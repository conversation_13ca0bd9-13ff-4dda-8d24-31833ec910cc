/**
 * Skill Gap Analyzer Performance Tests
 * 
 * Performance tests for Skill Gap Analyzer Performance measuring response times, throughput, and resource usage.
 * 
 * @category performance
 * @requires Performance monitoring, load testing
 */

// Mock environment variables first
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-testing';

// Mock all dependencies before importing
jest.mock('@/lib/services/geminiService', () => ({
  geminiService: {
    analyzeSkillsGap: jest.fn(),
    generateQuestions: jest.fn(),
  },
  AIServiceLogger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  }
}));

jest.mock('@/lib/ai-service-monitor');
jest.mock('@/lib/services/consolidated-cache-service');
jest.mock('@/lib/performance-monitor');

import { PerformanceMonitor } from '@/lib/performance-monitor';
import { AIServiceMonitor } from '@/lib/ai-service-monitor';
import { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';

// Mock performance monitoring
const mockPerformanceMonitor = {
  recordResponseTime: jest.fn(),
  recordThroughput: jest.fn(),
  recordError: jest.fn(),
  recordCacheHit: jest.fn(),
  recordMemoryUsage: jest.fn(),
  getMetrics: jest.fn(),
  getPerformanceStatus: jest.fn(),
  startMonitoring: jest.fn(),
  stopMonitoring: jest.fn(),
};

const mockAIServiceMonitor: any = {
  recordOperation: jest.fn(),
  recordRateLimitHit: jest.fn(),
  getMetrics: jest.fn(),
  getInstance: jest.fn((): any => mockAIServiceMonitor),
};

const mockCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  setJSON: jest.fn(),
  getWithFallback: jest.fn(),
  delete: jest.fn(),
  clear: jest.fn(),
  getMetrics: jest.fn(),
  healthCheck: jest.fn(),
  invalidateByTags: jest.fn()
};

// Mock the actual classes
(PerformanceMonitor as jest.MockedClass<typeof PerformanceMonitor>) = jest.fn().mockImplementation(() => mockPerformanceMonitor);
(AIServiceMonitor.getInstance as jest.Mock) = jest.fn(() => mockAIServiceMonitor);
(ConsolidatedCacheService as jest.MockedClass<typeof ConsolidatedCacheService>) = jest.fn().mockImplementation(() => mockCacheService);

describe('Skill Gap Analyzer Performance Monitoring - TDD', () => {
  let performanceMonitor: any;
  let aiServiceMonitor: any;
  let cacheService: any;

  beforeEach(() => {
    jest.clearAllMocks();
    performanceMonitor = new PerformanceMonitor();
    aiServiceMonitor = AIServiceMonitor.getInstance();
    cacheService = new ConsolidatedCacheService();
  });

  describe('Performance Metrics Collection', () => {
    it('should record skill search response times', async () => {
      // Test: Performance monitoring should track skill search operations
      const operation = 'skill_search';
      const responseTime = 150; // ms
      const success = true;

      performanceMonitor.recordResponseTime(operation, responseTime);

      expect(mockPerformanceMonitor.recordResponseTime).toHaveBeenCalledWith(
        operation,
        responseTime
      );
    });

    it('should record skill assessment submission performance', async () => {
      // Test: Performance monitoring should track assessment submissions
      const operation = 'skill_assessment_submit';
      const responseTime = 300; // ms
      const success = true;
      const userId = 'user-123';

      aiServiceMonitor.recordOperation(operation, responseTime, success, false, userId);

      expect(mockAIServiceMonitor.recordOperation).toHaveBeenCalledWith(
        operation,
        responseTime,
        success,
        false,
        userId
      );
    });

    it('should record comprehensive skill analysis performance', async () => {
      // Test: Performance monitoring should track comprehensive analysis operations
      const operation = 'comprehensive_skill_analysis';
      const responseTime = 2500; // ms
      const success = true;
      const cacheHit = false;
      const userId = 'user-456';

      aiServiceMonitor.recordOperation(operation, responseTime, success, cacheHit, userId);

      expect(mockAIServiceMonitor.recordOperation).toHaveBeenCalledWith(
        operation,
        responseTime,
        success,
        cacheHit,
        userId
      );
    });

    it('should track cache hit rates for skill data', async () => {
      // Test: Performance monitoring should track cache effectiveness
      const operation = 'skill_data_cache';
      const cacheHit = true;

      performanceMonitor.recordCacheHit(operation, cacheHit);

      expect(mockPerformanceMonitor.recordCacheHit).toHaveBeenCalledWith(
        operation,
        cacheHit
      );
    });

    it('should monitor memory usage for skill analysis', async () => {
      // Test: Performance monitoring should track memory consumption
      const component = 'skill_analyzer';
      const usageBytes = 25 * 1024 * 1024; // 25MB
      const maxBytes = 100 * 1024 * 1024; // 100MB

      performanceMonitor.recordMemoryUsage(component, usageBytes, maxBytes);

      expect(mockPerformanceMonitor.recordMemoryUsage).toHaveBeenCalledWith(
        component,
        usageBytes,
        maxBytes
      );
    });
  });

  describe('Caching Strategy Implementation', () => {
    it('should cache skill search results with appropriate TTL', async () => {
      // Test: Skill search results should be cached for performance
      const searchQuery = 'javascript';
      const searchResults = [
        { id: 'skill-1', name: 'JavaScript', category: 'Programming' },
        { id: 'skill-2', name: 'React', category: 'Frontend' }
      ];
      const cacheKey = `skill_search:${searchQuery}`;
      const ttlSeconds = 300; // 5 minutes

      await cacheService.set(cacheKey, searchResults, { ttl: ttlSeconds * 1000, tags: ['skills', 'search'] });

      expect(mockCacheService.set).toHaveBeenCalledWith(
        cacheKey,
        searchResults,
        { ttl: ttlSeconds * 1000, tags: ['skills', 'search'] }
      );
    });

    it('should cache user skill assessments with longer TTL', async () => {
      // Test: User assessments should be cached with longer expiration
      const userId = 'user-789';
      const assessments = [
        { skillId: 'skill-1', skillName: 'JavaScript', selfRating: 8 }
      ];
      const cacheKey = `user_assessments:${userId}`;
      const ttlSeconds = 3600; // 1 hour

      await cacheService.setJSON(cacheKey, assessments, ttlSeconds);

      expect(mockCacheService.setJSON).toHaveBeenCalledWith(
        cacheKey,
        assessments,
        ttlSeconds
      );
    });

    it('should cache comprehensive analysis results with medium TTL', async () => {
      // Test: Analysis results should be cached to avoid expensive recomputation
      const analysisId = 'analysis-123';
      const analysisResult = {
        skillGaps: [],
        learningPlan: {},
        careerReadiness: { currentScore: 75 }
      };
      const cacheKey = `skill_analysis:${analysisId}`;
      const ttlSeconds = 1800; // 30 minutes

      await cacheService.setJSON(cacheKey, analysisResult, ttlSeconds);

      expect(mockCacheService.setJSON).toHaveBeenCalledWith(
        cacheKey,
        analysisResult,
        ttlSeconds
      );
    });

    it('should implement cache fallback strategy', async () => {
      // Test: Cache should have fallback mechanism for reliability
      const cacheKey = 'skill_data:popular';
      const fallbackData = { skills: ['JavaScript', 'Python', 'React'] };

      mockCacheService.getWithFallback.mockResolvedValue(fallbackData);

      const result = await cacheService.getWithFallback(cacheKey);

      expect(mockCacheService.getWithFallback).toHaveBeenCalledWith(cacheKey);
      expect(result).toEqual(fallbackData);
    });
  });

  describe('Performance Thresholds and Alerts', () => {
    it('should detect slow skill search operations', async () => {
      // Test: Performance monitoring should alert on slow operations
      const operation = 'skill_search';
      const slowResponseTime = 3000; // 3 seconds - above threshold
      const success = true;

      performanceMonitor.recordResponseTime(operation, slowResponseTime);

      expect(mockPerformanceMonitor.recordResponseTime).toHaveBeenCalledWith(
        operation,
        slowResponseTime
      );

      // Verify that slow operations are tracked
      mockPerformanceMonitor.getMetrics.mockReturnValue({
        averageResponseTime: slowResponseTime,
        operationsAboveThreshold: 1
      });

      const metrics = performanceMonitor.getMetrics();
      expect(metrics.averageResponseTime).toBeGreaterThan(2000); // Above warning threshold
    });

    it('should detect low cache hit rates', async () => {
      // Test: Performance monitoring should alert on poor cache performance
      const operation = 'skill_cache';
      
      // Simulate low cache hit rate
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordCacheHit(operation, i < 3); // 30% hit rate
      }

      expect(mockPerformanceMonitor.recordCacheHit).toHaveBeenCalledTimes(10);

      // Verify cache hit rate monitoring
      mockPerformanceMonitor.getPerformanceStatus.mockReturnValue({
        cacheHitRate: 30,
        isHealthy: false,
        alerts: ['Low cache hit rate detected']
      });

      const status = performanceMonitor.getPerformanceStatus();
      expect(status.cacheHitRate).toBeLessThan(70); // Below threshold
      expect(status.isHealthy).toBe(false);
    });

    it('should monitor error rates for skill operations', async () => {
      // Test: Performance monitoring should track error rates
      const operation = 'skill_analysis';
      const errorType = 'ai_service_timeout';
      const userId = 'user-error-test';

      performanceMonitor.recordError(operation, errorType, userId);

      expect(mockPerformanceMonitor.recordError).toHaveBeenCalledWith(
        operation,
        errorType,
        userId
      );
    });
  });

  describe('Performance Optimization Actions', () => {
    it('should trigger cache warming for popular skills', async () => {
      // Test: System should proactively warm cache for performance
      const popularSkills = ['JavaScript', 'Python', 'React', 'Node.js'];
      
      // Simulate cache warming
      for (const skill of popularSkills) {
        const cacheKey = `skill_data:${skill}`;
        await cacheService.setJSON(cacheKey, { name: skill, popular: true }, 3600);
      }

      expect(mockCacheService.setJSON).toHaveBeenCalledTimes(popularSkills.length);
    });

    it('should implement request batching for skill searches', async () => {
      // Test: System should batch requests for better performance
      const batchedQueries = ['java', 'javascript', 'python'];
      const batchKey = `skill_search_batch:${batchedQueries.join(',')}`;
      
      await cacheService.setJSON(batchKey, { queries: batchedQueries, batched: true }, 600);

      expect(mockCacheService.setJSON).toHaveBeenCalledWith(
        batchKey,
        { queries: batchedQueries, batched: true },
        600
      );
    });
  });

  describe('Health Monitoring', () => {
    it('should perform cache health checks', async () => {
      // Test: System should monitor cache health
      mockCacheService.healthCheck.mockResolvedValue(true);

      const isHealthy = await cacheService.healthCheck();

      expect(mockCacheService.healthCheck).toHaveBeenCalled();
      expect(isHealthy).toBe(true);
    });

    it('should provide comprehensive performance status', async () => {
      // Test: System should provide detailed performance status
      const expectedStatus = {
        currentMetrics: {
          cacheHitRate: 85,
          averageResponseTime: 150,
          errorRate: 2,
          memoryUsage: 45 * 1024 * 1024
        },
        isHealthy: true,
        healthScore: 92,
        recentAlerts: [],
        recentOptimizations: []
      };

      mockPerformanceMonitor.getPerformanceStatus.mockReturnValue(expectedStatus);

      const status = performanceMonitor.getPerformanceStatus();

      expect(mockPerformanceMonitor.getPerformanceStatus).toHaveBeenCalled();
      expect(status.isHealthy).toBe(true);
      expect(status.healthScore).toBeGreaterThan(90);
    });
  });
});
