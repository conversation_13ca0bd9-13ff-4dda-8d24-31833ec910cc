/**
 * Resend Verification Tests
 * 
 * Tests Resend Verification API endpoints, request/response handling, validation, and error scenarios.
 * 
 * @category unit
 * @requires API mocking, request simulation
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/auth/resend-verification/route';
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import { v4 as uuidv4 } from 'uuid';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  user: {
    findUnique: jest.fn(),
  },
  verificationToken: {
    findFirst: jest.fn(),
    deleteMany: jest.fn(),
    create: jest.fn(),
  },
}));

jest.mock('@/lib/email', () => ({
  sendEmail: jest.fn(),
}));

jest.mock('uuid', () => ({
  v4: jest.fn(),
}));

// Mock crypto for Node.js environment
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: jest.fn((arr: Uint8Array) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
    randomUUID: jest.fn(() => 'mock-uuid-' + Math.random().toString(36).substring(2, 11)),
    subtle: {
      digest: jest.fn(() => Promise.resolve(new ArrayBuffer(32)))
    }
  },
  writable: true
});

const mockPrisma = prisma as jest.Mocked<typeof prisma>;
const mockSendEmail = sendEmail as jest.MockedFunction<typeof sendEmail>;
const mockUuidv4 = uuidv4 as jest.MockedFunction<() => string>;

// Type-safe mock helpers
const mockUserFindUnique = mockPrisma.user.findUnique as jest.MockedFunction<any>;
const mockVerificationTokenFindFirst = mockPrisma.verificationToken.findFirst as jest.MockedFunction<any>;
const mockVerificationTokenDeleteMany = mockPrisma.verificationToken.deleteMany as jest.MockedFunction<any>;
const mockVerificationTokenCreate = mockPrisma.verificationToken.create as jest.MockedFunction<any>;

describe('/api/auth/resend-verification', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.NEXTAUTH_URL = 'http://localhost:3000';
  });

  it('should send verification email for unverified user', async () => {
    const email = '<EMAIL>';
    const token = 'mock-uuid-token';

    mockUuidv4.mockReturnValue(token);

    // Mock unverified user
    mockUserFindUnique.mockResolvedValue({
      id: 'user123',
      email: email,
      emailVerified: null, // Unverified
      name: 'Test User',
      password: 'hashedpassword',
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    // Mock no recent tokens
    mockVerificationTokenFindFirst.mockResolvedValue(null);

    // Mock token operations
    mockVerificationTokenDeleteMany.mockResolvedValue({ count: 0 });
    mockVerificationTokenCreate.mockResolvedValue({
      identifier: email,
      token: token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });

    // Mock successful email sending
    mockSendEmail.mockResolvedValue({ success: true, data: { id: 'email123' } });

    const request = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.data.message).toBe('Verification email sent successfully.');
    expect(mockSendEmail).toHaveBeenCalledWith({
      to: email,
      subject: 'Verify your email for FAAFO Career Platform',
      template: expect.any(Object),
    });
    expect(mockVerificationTokenCreate).toHaveBeenCalledWith({
      data: {
        identifier: email,
        token: token,
        expires: expect.any(Date),
      },
    });
  });

  it('should return error for missing email', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({}),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error.message).toBe('Email is required.');
  });

  it('should return success message for non-existent user (security)', async () => {
    const email = '<EMAIL>';

    mockUserFindUnique.mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.data.message).toBe('If an account with that email exists and is unverified, a verification email has been sent.');
  });

  it('should return success message for already verified user', async () => {
    const email = '<EMAIL>';

    mockUserFindUnique.mockResolvedValue({
      id: 'user123',
      email: email,
      emailVerified: new Date(), // Already verified
      name: 'Test User',
      password: 'hashedpassword',
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    const request = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.data.message).toBe('Email is already verified.');
  });

  it('should enforce rate limiting', async () => {
    const email = '<EMAIL>';

    mockUserFindUnique.mockResolvedValue({
      id: 'user123',
      email: email,
      emailVerified: null,
      name: 'Test User',
      password: 'hashedpassword',
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    // Mock recent token (within 5 minutes)
    mockVerificationTokenFindFirst.mockResolvedValue({
      identifier: email,
      token: 'recent-token',
      expires: new Date(Date.now() + 23 * 60 * 60 * 1000), // 23 hours from now
    });

    const request = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(429);
    expect(data.error.message).toBe('A verification email was recently sent. Please wait 5 minutes before requesting another.');
  });

  it('should handle email sending failure', async () => {
    const email = '<EMAIL>';
    const token = 'mock-uuid-token';

    mockUuidv4.mockReturnValue(token);

    mockUserFindUnique.mockResolvedValue({
      id: 'user123',
      email: email,
      emailVerified: null,
      name: 'Test User',
      password: 'hashedpassword',
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    mockVerificationTokenFindFirst.mockResolvedValue(null);
    mockVerificationTokenDeleteMany.mockResolvedValue({ count: 0 });
    mockVerificationTokenCreate.mockResolvedValue({
      identifier: email,
      token: token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });

    // Mock email sending failure
    mockSendEmail.mockRejectedValue(new Error('Email service unavailable'));

    const request = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error.message).toBe('Failed to send verification email.');
  });

  it('should clean up expired tokens before creating new one', async () => {
    const email = '<EMAIL>';
    const token = 'mock-uuid-token';

    mockUuidv4.mockReturnValue(token);

    mockUserFindUnique.mockResolvedValue({
      id: 'user123',
      email: email,
      emailVerified: null,
      name: 'Test User',
      password: 'hashedpassword',
      passwordResetToken: null,
      passwordResetExpires: null,
      failedLoginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      image: null,
    });

    mockVerificationTokenFindFirst.mockResolvedValue(null);
    mockVerificationTokenDeleteMany
      .mockResolvedValueOnce({ count: 2 }) // Expired tokens cleanup
      .mockResolvedValueOnce({ count: 1 }); // Existing tokens cleanup

    mockVerificationTokenCreate.mockResolvedValue({
      identifier: email,
      token: token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });

    mockSendEmail.mockResolvedValue({ success: true, data: { id: 'email123' } });

    const request = new NextRequest('http://localhost:3000/api/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify({ email }),
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await POST(request);

    expect(response.status).toBe(200);
    expect(mockVerificationTokenDeleteMany).toHaveBeenCalledTimes(2);
    // First call: clean up expired tokens
    expect(mockVerificationTokenDeleteMany).toHaveBeenNthCalledWith(1, {
      where: {
        identifier: email,
        expires: { lt: expect.any(Date) },
      },
    });
    // Second call: clean up existing tokens
    expect(mockVerificationTokenDeleteMany).toHaveBeenNthCalledWith(2, {
      where: {
        identifier: email,
      },
    });
  });
});
