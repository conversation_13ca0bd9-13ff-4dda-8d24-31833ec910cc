/**
 * Comprehensive SignupForm Component Tests
 * Tests registration UI, form validation, error handling, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import SignupForm from '@/components/SignupForm';

// Mock hooks
jest.mock('@/hooks/useCSRF', () => ({
  useCSRF: jest.fn(() => ({
    getHeaders: jest.fn(() => ({ 'X-CSRF-Token': 'test-token' })),
    isLoading: false,
  })),
}));

jest.mock('@/hooks/useFormValidation', () => ({
  useValidatedForm: jest.fn((initialData, rules, onSubmit) => ({
    data: initialData,
    updateField: jest.fn(),
    handleSubmit: jest.fn((e) => {
      e.preventDefault();
      onSubmit(initialData, initialData);
    }),
    isSubmitting: false,
    validation: {
      errors: {},
      isValid: true,
    },
    validationActions: {},
  })),
}));

// Mock fetch
global.fetch = jest.fn();

describe('SignupForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
  });

  it('should render signup form correctly', () => {
    render(<SignupForm />);
    
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
    expect(screen.getByText(/terms of service/i)).toBeInTheDocument();
    expect(screen.getByText(/privacy policy/i)).toBeInTheDocument();
  });

  it('should handle successful signup with verification required', async () => {
    const mockResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as unknown as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    expect(screen.getByText(/we've sent a verification email to/i)).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /resend verification email/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /register a different email/i })).toBeInTheDocument();

    expect(fetch).toHaveBeenCalledWith('/api/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    });
  });

  it('should handle signup error', async () => {
    const mockResponse = {
      ok: false,
      json: async () => ({
        message: 'User already exists',
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as unknown as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/error: user already exists/i)).toBeInTheDocument();
    });

    // Should still show the form, not the verification success screen
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();
  });

  it('should handle resend verification email', async () => {
    // First, simulate successful signup
    const signupResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as unknown as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    // Now test resend verification
    const resendResponse = {
      ok: true,
      json: async () => ({
        message: 'Verification email sent successfully.',
      }),
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(resendResponse as unknown as Response);

    const resendButton = screen.getByRole('button', { name: /resend verification email/i });
    fireEvent.click(resendButton);

    await waitFor(() => {
      expect(screen.getByText(/verification email sent successfully/i)).toBeInTheDocument();
    });

    expect(fetch).toHaveBeenLastCalledWith('/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
      }),
    });
  });

  it('should handle resend verification error', async () => {
    // First, simulate successful signup
    const signupResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as unknown as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    // Mock resend error
    const resendResponse = {
      ok: false,
      json: async () => ({
        error: 'A verification email was recently sent. Please wait 5 minutes before requesting another.',
      }),
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(resendResponse as unknown as Response);

    const resendButton = screen.getByRole('button', { name: /resend verification email/i });
    fireEvent.click(resendButton);

    await waitFor(() => {
      expect(screen.getByText(/error: a verification email was recently sent/i)).toBeInTheDocument();
    });
  });

  it('should allow registering different email', async () => {
    // First, simulate successful signup
    const signupResponse = {
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    };

    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce(signupResponse as unknown as Response);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });

    // Click "Register a different email"
    const differentEmailButton = screen.getByRole('button', { name: /register a different email/i });
    fireEvent.click(differentEmailButton);

    // Should return to the form
    await waitFor(() => {
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.queryByText(/registration successful!/i)).not.toBeInTheDocument();
    });

    // Form should be cleared
    expect(screen.getByLabelText(/email address/i)).toHaveValue('');
    expect(screen.getByLabelText(/password/i)).toHaveValue('');
  });

  it('should show loading state during signup', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    (fetch as jest.MockedFunction<typeof fetch>).mockReturnValue(promise as Promise<Response>);

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    // Should show loading state
    await waitFor(() => {
      expect(screen.getByRole('status')).toHaveTextContent('Signing up...');
    });

    // Resolve the promise
    resolvePromise!({
      ok: true,
      json: async () => ({
        message: 'User created successfully. Please check your email to verify your account.',
        requiresVerification: true,
      }),
      headers: {
        get: () => 'application/json',
      },
    });

    await waitFor(() => {
      expect(screen.getByText(/registration successful!/i)).toBeInTheDocument();
    });
  });

  it('should handle network errors gracefully', async () => {
    (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValue(new Error('Network error'));

    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/error: an unexpected error occurred/i)).toBeInTheDocument();
    });
  });

  it('should require email and password fields', () => {
    render(<SignupForm />);

    const emailInput = screen.getByLabelText(/email address/i);
    const passwordInput = screen.getByLabelText(/password/i);

    expect(emailInput).toBeRequired();
    expect(passwordInput).toBeRequired();
  });

  describe('Security and Edge Cases', () => {
    it('should prevent XSS in error messages', async () => {
      const xssPayload = '<script>alert("xss")</script>';
      const errorResponse = {
        ok: false,
        json: async () => ({
          message: xssPayload,
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(errorResponse as unknown as Response);

      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        // Error should be displayed as text, not executed as HTML
        expect(screen.getByText(new RegExp(xssPayload.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')))).toBeInTheDocument();
        expect(document.querySelector('script')).toBeNull();
      });
    });

    it('should handle extremely long email addresses', async () => {
      const longEmail = 'a'.repeat(250) + '@example.com';

      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);
      fireEvent.change(emailInput, { target: { value: longEmail } });

      expect(emailInput).toHaveValue(longEmail);
    });

    it('should handle special characters in credentials', async () => {
      const specialEmail = '<EMAIL>';
      const specialPassword = 'P@ssw0rd!#$%^&*()';

      const successResponse = {
        ok: true,
        json: async () => ({
          message: 'Registration successful! Please check your email for verification.',
          requiresVerification: true,
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(successResponse as unknown as Response);

      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      fireEvent.change(emailInput, { target: { value: specialEmail } });
      fireEvent.change(passwordInput, { target: { value: specialPassword } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('/api/auth/signup', expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            email: specialEmail,
            password: specialPassword,
          }),
        }));
      });
    });

    it('should handle rapid form submissions', async () => {
      const successResponse = {
        ok: true,
        json: async () => ({
          message: 'Registration successful!',
          requiresVerification: true,
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve(successResponse as unknown as Response), 100))
      );

      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });

      // Rapid clicks
      fireEvent.click(submitButton);
      fireEvent.click(submitButton);
      fireEvent.click(submitButton);

      // Should only call fetch once due to form submission protection
      await waitFor(() => {
        expect(fetch).toHaveBeenCalledTimes(1);
      });
    });

    it('should validate password strength', () => {
      render(<SignupForm />);

      const passwordInput = screen.getByLabelText(/password/i);

      // Test weak password
      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.blur(passwordInput);

      // Should show validation error for weak password
      expect(passwordInput).toHaveValue('123');
    });

    it('should validate email format', () => {
      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);

      // Test invalid email
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.blur(emailInput);

      // Should show validation error for invalid email
      expect(emailInput).toHaveValue('invalid-email');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      expect(emailInput).toHaveAttribute('aria-required', 'true');
      expect(passwordInput).toHaveAttribute('aria-required', 'true');
      expect(submitButton).toHaveAttribute('type', 'submit');
    });

    it('should announce errors to screen readers', async () => {
      const errorResponse = {
        ok: false,
        json: async () => ({
          message: 'Email already exists',
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(errorResponse as unknown as Response);

      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        const errorElement = screen.getByText(/email already exists/i);
        expect(errorElement).toHaveAttribute('role', 'alert');
      });
    });

    it('should support keyboard navigation', () => {
      render(<SignupForm />);

      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign up/i });

      // Tab order should be email -> password -> submit
      emailInput.focus();
      expect(document.activeElement).toBe(emailInput);

      fireEvent.keyDown(emailInput, { key: 'Tab' });
      expect(document.activeElement).toBe(passwordInput);

      fireEvent.keyDown(passwordInput, { key: 'Tab' });
      expect(document.activeElement).toBe(submitButton);
    });

    it('should have proper form labels and descriptions', () => {
      render(<SignupForm />);

      // Check for terms of service and privacy policy links
      expect(screen.getByText(/terms of service/i)).toBeInTheDocument();
      expect(screen.getByText(/privacy policy/i)).toBeInTheDocument();

      // Check for proper form structure
      const form = screen.getByRole('form');
      expect(form).toBeInTheDocument();
    });
  });
});
