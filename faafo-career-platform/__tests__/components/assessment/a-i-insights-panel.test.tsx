/**
 * A I Insights Panel Tests
 * 
 * Tests A I Insights Panel component functionality, rendering, user interactions, and edge cases.
 * 
 * @category unit
 * @requires React Testing Library, component mocking
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AIInsightsPanel from '@/components/assessment/AIInsightsPanel';

// Mock the AI service
jest.mock('@/lib/ai/geminiService', () => ({
  generateInsights: jest.fn(),
}));

// Mock the tabs component
jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, ...props }: any) => <div data-testid="tabs" {...props}>{children}</div>,
  TabsContent: ({ children, ...props }: any) => <div data-testid="tabs-content" {...props}>{children}</div>,
  TabsList: ({ children, ...props }: any) => <div data-testid="tabs-list" {...props}>{children}</div>,
  TabsTrigger: ({ children, ...props }: any) => <button data-testid="tabs-trigger" {...props}>{children}</button>,
}));

// Mock other UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h3 data-testid="card-title" {...props}>{children}</h3>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button data-testid="button" onClick={onClick} {...props}>{children}</button>
  ),
}));

jest.mock('@/components/ui/alert', () => ({
  Alert: ({ children, ...props }: any) => <div data-testid="alert" {...props}>{children}</div>,
  AlertDescription: ({ children, ...props }: any) => <div data-testid="alert-description" {...props}>{children}</div>,
}));

const mockAssessmentData = {
  responses: {
    'career-interests': ['technology', 'problem-solving'],
    'work-style': ['collaborative', 'analytical'],
    'skills': ['programming', 'communication'],
    'values': ['innovation', 'work-life-balance'],
  },
  scores: {
    technology: 85,
    business: 60,
    creative: 45,
  },
  careerPaths: [
    { id: '1', title: 'Software Developer', match: 90 },
    { id: '2', title: 'Data Scientist', match: 85 },
  ],
};

const mockAIInsights = {
  personalityAnalysis: {
    workStyle: 'Analytical and collaborative',
    motivationFactors: ['Growth', 'Impact', 'Recognition'],
    preferredEnvironment: 'Dynamic workplace',
    communicationStyle: 'Direct and clear',
    leadershipPotential: 'Developing',
    stressManagement: 'Proactive',
    adaptabilityScore: 85,
    teamworkPreference: 'Balanced',
    innovationTendency: 'Open to change',
    riskTolerance: 'Moderate'
  },
  careerFitAnalysis: [
    {
      careerPath: 'Software Developer',
      fitScore: 90,
      aiReasoning: 'Strong technical skills and analytical mindset',
      personalityAlignment: ['Problem-solving', 'Technical skills'],
      potentialChallenges: ['Staying updated with technology'],
      successPredictors: ['Continuous learning', 'Technical expertise'],
      marketOutlook: 'High demand',
      salaryGrowthPotential: 'Excellent',
      workLifeBalanceRating: 8,
      stressLevel: 6
    }
  ],
  skillGapInsights: {
    criticalGaps: [
      {
        skill: 'Cloud computing',
        importance: 9,
        currentLevel: 3,
        marketDemand: 'High',
        learningDifficulty: 'Medium',
        aiRecommendation: 'Focus on AWS or Azure certification',
        prerequisiteSkills: ['Basic networking'],
        complementarySkills: ['DevOps', 'Containerization']
      }
    ],
    hiddenStrengths: ['Leadership potential', 'Communication'],
    transferableSkills: ['Problem-solving', 'Analysis'],
    learningPriority: [
      {
        skill: 'Cloud computing',
        priority: 1,
        reasoning: 'High market demand',
        optimalLearningPath: 'Certification course',
        estimatedHours: 120,
        milestones: ['Basic concepts', 'Hands-on practice', 'Certification']
      }
    ],
    timeToCompetency: {
      optimisticTimeline: '3-4 months',
      realisticTimeline: '6-8 months',
      conservativeTimeline: '10-12 months',
      factorsAffectingSpeed: ['Prior experience', 'Time commitment'],
      accelerationOpportunities: ['Intensive courses', 'Mentorship']
    },
    alternativePathways: ['DevOps Engineer', 'Cloud Architect']
  },
  learningStyleRecommendations: {
    primaryLearningStyle: 'Visual and hands-on',
    recommendedFormats: ['Online courses', 'Practice projects', 'Workshops'],
    studySchedule: {
      optimalSessionLength: '45-60 minutes',
      frequencyPerWeek: 4,
      bestTimeOfDay: 'Evening',
      breakIntervals: '15 minutes every hour',
      reviewSchedule: 'Weekly review sessions'
    },
    motivationTechniques: ['Goal setting', 'Progress tracking'],
    progressTrackingMethods: ['Assessments', 'Project milestones'],
    socialLearningPreference: 'Mixed'
  },
  marketTrendAnalysis: {
    industryGrowth: 'High demand in tech sector',
    emergingSkills: ['AI/ML', 'Cloud computing', 'Cybersecurity'],
    decliningSkills: ['Legacy systems', 'Outdated frameworks'],
    salaryTrends: 'Increasing for specialized roles',
    remoteWorkOpportunities: 'Excellent',
    geographicHotspots: ['San Francisco', 'Seattle', 'Austin'],
    futureOutlook: 'Continued growth and innovation'
  },
  personalizationScore: 85,
  confidenceLevel: 88,
  generatedAt: new Date().toISOString(),
  version: '1.0.0'
};

describe('AIInsightsPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading state initially', () => {
    render(<AIInsightsPanel assessmentId="test-assessment-id" isVisible={true} />);

    expect(screen.getByText(/generating ai insights/i)).toBeInTheDocument();
  });

  it('should display AI insights after loading', async () => {
    // Mock fetch for the AI insights API
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      status: 200,
      json: () => Promise.resolve(mockAIInsights),
    });

    render(<AIInsightsPanel assessmentId="test-assessment-id" isVisible={true} />);

    await waitFor(() => {
      expect(screen.getByText(/ai-powered insights/i)).toBeInTheDocument();
    });
  });

  it('should handle AI service errors gracefully', async () => {
    // Mock fetch to return an error
    global.fetch = jest.fn().mockResolvedValue({
      ok: false,
      status: 500,
      json: () => Promise.resolve({ error: 'AI service unavailable' }),
    });

    render(<AIInsightsPanel assessmentId="test-assessment-id" isVisible={true} />);

    await waitFor(() => {
      expect(screen.getByText(/ai insights unavailable/i)).toBeInTheDocument();
    });
  });

  it('should allow retrying after error', async () => {
    // Mock fetch to fail first, then succeed
    global.fetch = jest.fn()
      .mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ error: 'AI service unavailable' }),
      })
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockAIInsights),
      });

    render(<AIInsightsPanel assessmentId="test-assessment-id" isVisible={true} />);

    await waitFor(() => {
      expect(screen.getByText(/ai insights unavailable/i)).toBeInTheDocument();
    });

    const retryButton = screen.getByText(/try again/i);
    fireEvent.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText(/ai-powered insights/i)).toBeInTheDocument();
    });
  });

  it('should display confidence scores for each insight', async () => {
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      status: 200,
      json: () => Promise.resolve(mockAIInsights),
    });

    render(<AIInsightsPanel assessmentId="test-assessment-id" isVisible={true} />);

    await waitFor(() => {
      expect(screen.getByText(/ai-powered insights/i)).toBeInTheDocument();
    });
  });

  it('should switch between different insight tabs', async () => {
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      status: 200,
      json: () => Promise.resolve(mockAIInsights),
    });

    render(<AIInsightsPanel assessmentId="test-assessment-id" isVisible={true} />);

    await waitFor(() => {
      expect(screen.getByText(/ai-powered insights/i)).toBeInTheDocument();
    });
  });

  it('should handle missing assessment data gracefully', () => {
    render(<AIInsightsPanel assessmentId="" isVisible={true} />);

    // Component should still render but may show loading or error state
    expect(screen.getByText(/generating ai insights/i)).toBeInTheDocument();
  });

  it('should not render when not visible', () => {
    render(<AIInsightsPanel assessmentId="test-assessment-id" isVisible={false} />);

    // Component should not render anything when isVisible is false
    expect(screen.queryByText(/ai-powered insights/i)).not.toBeInTheDocument();
  });
});
