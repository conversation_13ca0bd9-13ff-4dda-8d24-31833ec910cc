import '@testing-library/jest-dom';

// Import memory leak fixes
const { setupJestMemoryFixes } = require('./__tests__/test-utils/memory-leak-fixes');

// Setup memory leak prevention
setupJestMemoryFixes();

// Load test environment variables from .env.test
const path = require('path');
const dotenv = require('dotenv');

// Load test environment
dotenv.config({ path: path.resolve(__dirname, '.env.test') });

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: function(query: string) {
    return {
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    };
  },
});

// Lightweight NextAuth mocks
jest.mock('next-auth/providers/credentials', () => jest.fn(() => ({
  id: 'credentials',
  name: 'Credentials',
  type: 'credentials',
  authorize: jest.fn(),
})));

jest.mock('next-auth/providers/email', () => jest.fn(() => ({
  id: 'email',
  name: 'Email',
  type: 'email',
})));

jest.mock('next-auth', () => ({
  default: jest.fn(() => ({
    handlers: { GET: jest.fn(), POST: jest.fn() },
    auth: jest.fn(),
  })),
  getServerSession: jest.fn(),
}));

jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
      },
    },
    status: 'authenticated',
  })),
  signIn: jest.fn(),
  signOut: jest.fn(),
  SessionProvider: ({ children }: { children: React.ReactNode }) => children,
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    pathname: '/test',
    query: {},
    asPath: '/test',
    route: '/test',
  })),
}));

// Mock Sentry to prevent import issues
jest.mock('@sentry/nextjs', () => ({
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  withScope: jest.fn((callback) => callback({ setTag: jest.fn(), setContext: jest.fn() })),
  init: jest.fn(),
  configureScope: jest.fn(),
}));

// Mock jose module to prevent ES module issues
jest.mock('jose', () => ({
  jwtVerify: jest.fn(),
  SignJWT: jest.fn().mockImplementation(() => ({
    setProtectedHeader: jest.fn().mockReturnThis(),
    setIssuedAt: jest.fn().mockReturnThis(),
    setExpirationTime: jest.fn().mockReturnThis(),
    sign: jest.fn().mockResolvedValue('mock-jwt-token'),
  })),
  importSPKI: jest.fn(),
  importPKCS8: jest.fn(),
}));

// Create a mock useSession that can be controlled in tests
const mockUseSession = jest.fn(() => ({
  data: null,
  status: 'unauthenticated',
  update: jest.fn(),
}));

jest.mock('next-auth/react', () => ({
  useSession: mockUseSession,
  signIn: jest.fn(),
  signOut: jest.fn(),
  getSession: jest.fn(),
  SessionProvider: ({ children, session }: any) => {
    // Update the mock based on the session prop
    if (session) {
      mockUseSession.mockReturnValue({
        data: session,
        status: 'authenticated',
        update: jest.fn(),
      });
    }
    return children;
  },
}));

// Make mockUseSession available globally for tests
global.mockUseSession = mockUseSession;

// Enhanced Prisma mock with proper method signatures
const createMockModel = () => ({
  findUnique: jest.fn().mockResolvedValue(null),
  findMany: jest.fn().mockResolvedValue([]),
  findFirst: jest.fn().mockResolvedValue(null),
  create: jest.fn().mockResolvedValue({}),
  update: jest.fn().mockResolvedValue({}),
  upsert: jest.fn().mockResolvedValue({}),
  delete: jest.fn().mockResolvedValue({}),
  count: jest.fn().mockResolvedValue(0),
  aggregate: jest.fn().mockResolvedValue({}),
  groupBy: jest.fn().mockResolvedValue([]),
  createMany: jest.fn().mockResolvedValue({ count: 0 }),
  updateMany: jest.fn().mockResolvedValue({ count: 0 }),
  deleteMany: jest.fn().mockResolvedValue({ count: 0 }),
});

const mockPrisma = new Proxy({}, {
  get: (target, prop) => {
    if (prop === '$transaction') return jest.fn().mockResolvedValue(undefined);
    if (prop === '$connect') return jest.fn().mockResolvedValue(undefined);
    if (prop === '$disconnect') return jest.fn().mockResolvedValue(undefined);
    if (prop === '$executeRaw') return jest.fn().mockResolvedValue(0);
    if (prop === '$queryRaw') return jest.fn().mockResolvedValue([]);
    if (prop === '$use') return jest.fn();
    if (prop === '$on') return jest.fn();
    return createMockModel();
  }
});

jest.mock('@/lib/prisma', () => ({
  __esModule: true,
  default: mockPrisma,
}));

// Make mockPrisma available globally for test helpers
global.mockPrisma = mockPrisma;

// Mock useAuthState hook to prevent React Act warnings - removed for now to isolate issues

// Mock navigation hooks to prevent errors
jest.mock('@/hooks/useNavigationState', () => ({
  useNavigationState: jest.fn(() => ({
    isMobileMenuOpen: false,
    isToolsDropdownOpen: false,
    toggleMobileMenu: jest.fn(),
    closeMobileMenu: jest.fn(),
    toggleToolsDropdown: jest.fn(),
    closeToolsDropdown: jest.fn(),
  })),
  useNavigationShortcuts: jest.fn(),
}));

// Mock theme hook
jest.mock('next-themes', () => ({
  useTheme: jest.fn(() => ({
    theme: 'dark',
    setTheme: jest.fn(),
    resolvedTheme: 'dark',
    themes: ['light', 'dark'],
    systemTheme: 'dark',
  })),
  ThemeProvider: ({ children }: any) => children,
}));

// Essential mocks only
jest.mock('bcrypt', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Essential global mocks
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
  root: null,
  rootMargin: '',
  thresholds: [],
  takeRecords: jest.fn(() => []),
}));

// Duplicate window.matchMedia mock removed - already defined above

global.fetch = jest.fn(() => Promise.resolve({
  ok: true,
  status: 200,
  statusText: 'OK',
  headers: new Headers(),
  url: '',
  redirected: false,
  type: 'basic' as ResponseType,
  body: null,
  bodyUsed: false,
  clone: jest.fn(),
  arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
  blob: () => Promise.resolve(new Blob()),
  formData: () => Promise.resolve(new FormData()),
  json: () => Promise.resolve({}),
  text: () => Promise.resolve(''),
  bytes: () => Promise.resolve(new Uint8Array()),
} as Response));
// Environment setup
if (!process.env.NODE_ENV) {
  (process.env as any).NODE_ENV = 'test';
}
process.env.NEXTAUTH_URL = 'http://localhost:3000';
process.env.NEXTAUTH_SECRET = 'test-secret';

// Test cleanup
beforeEach(() => {
  jest.clearAllMocks();
});



