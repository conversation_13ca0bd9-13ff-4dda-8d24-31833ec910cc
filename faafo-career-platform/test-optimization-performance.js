#!/usr/bin/env node

/**
 * Performance Testing Script for Comprehensive Skills Analysis API Optimizations
 * Tests Phase 1 optimizations: Request Batching, Enhanced Caching, Concurrent Database Operations
 */

const https = require('https');
const http = require('http');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test-user-123';
const CONCURRENT_REQUESTS = 5;
const TOTAL_TEST_REQUESTS = 20;

// Test data for comprehensive skills analysis
const testRequestData = {
  targetCareerPath: {
    careerPathId: null,
    careerPathName: 'Software Engineer'
  },
  currentSkills: [
    { name: 'JavaScript', level: 'INTERMEDIATE' },
    { name: 'React', level: 'BEGINNER' },
    { name: 'Node.js', level: 'INTERMEDIATE' }
  ],
  desiredSkills: [
    { name: 'TypeScript', level: 'ADVANCED' },
    { name: 'AWS', level: 'INTERMEDIATE' },
    { name: 'Docker', level: 'BEGINNER' }
  ],
  experienceLevel: 'INTERMEDIATE',
  careerGoals: ['Full Stack Development', 'Cloud Architecture'],
  timeframe: '6 months'
};

// Performance metrics tracking
const metrics = {
  requestTimes: [],
  cacheHits: 0,
  cacheMisses: 0,
  errors: 0,
  successfulRequests: 0,
  batchingEfficiency: 0,
  concurrentOperations: 0
};

/**
 * Make HTTP request with timing
 */
function makeRequest(path, data, headers = {}) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'x-test-user-id': TEST_USER_ID,
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            responseTime,
            data: parsedData,
            headers: res.headers
          });
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error instanceof Error ? error.message : String(error)}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * Test request batching efficiency
 */
async function testRequestBatching() {
  console.log('\n🔄 Testing Request Batching Service...');
  
  const batchStartTime = Date.now();
  const promises = [];
  
  // Send multiple similar requests simultaneously to test batching
  for (let i = 0; i < CONCURRENT_REQUESTS; i++) {
    const promise = makeRequest('/api/ai/skills-analysis/comprehensive', {
      ...testRequestData,
      requestId: `batch-test-${i}`
    });
    promises.push(promise);
  }
  
  try {
    const results = await Promise.all(promises);
    const batchEndTime = Date.now();
    const totalBatchTime = batchEndTime - batchStartTime;
    
    console.log(`✅ Batch of ${CONCURRENT_REQUESTS} requests completed in ${totalBatchTime}ms`);
    
    // Analyze results
    const successfulResults = results.filter(r => r.statusCode === 200);
    const avgResponseTime = successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length;
    
    console.log(`   - Successful requests: ${successfulResults.length}/${CONCURRENT_REQUESTS}`);
    console.log(`   - Average response time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`   - Total batch time: ${totalBatchTime}ms`);
    
    // Check for batching indicators in response headers
    const batchingIndicators = results.filter(r => 
      r.headers['x-batch-processed'] || 
      r.headers['x-request-batched']
    );
    
    console.log(`   - Requests processed via batching: ${batchingIndicators.length}`);
    
    metrics.successfulRequests += successfulResults.length;
    metrics.errors += (CONCURRENT_REQUESTS - successfulResults.length);
    metrics.batchingEfficiency = batchingIndicators.length / CONCURRENT_REQUESTS;
    
    return { avgResponseTime, totalBatchTime, batchingEfficiency: metrics.batchingEfficiency };
    
  } catch (error) {
    console.error(`❌ Batch testing failed: ${error instanceof Error ? error.message : String(error)}`);
    metrics.errors += CONCURRENT_REQUESTS;
    throw error;
  }
}

/**
 * Test caching effectiveness
 */
async function testCaching() {
  console.log('\n💾 Testing Enhanced Caching System...');
  
  // First request (should be cache miss)
  console.log('   Making first request (cache miss expected)...');
  const firstRequest = await makeRequest('/api/ai/skills-analysis/comprehensive', {
    ...testRequestData,
    requestId: 'cache-test-1'
  });
  
  const firstResponseTime = firstRequest.responseTime;
  console.log(`   - First request: ${firstResponseTime}ms`);
  
  // Second identical request (should be cache hit)
  console.log('   Making identical request (cache hit expected)...');
  const secondRequest = await makeRequest('/api/ai/skills-analysis/comprehensive', {
    ...testRequestData,
    requestId: 'cache-test-2'
  });
  
  const secondResponseTime = secondRequest.responseTime;
  console.log(`   - Second request: ${secondResponseTime}ms`);
  
  // Calculate cache effectiveness
  const speedImprovement = ((firstResponseTime - secondResponseTime) / firstResponseTime) * 100;
  const isCacheHit = secondResponseTime < (firstResponseTime * 0.5); // 50% faster indicates cache hit
  
  console.log(`   - Speed improvement: ${speedImprovement.toFixed(2)}%`);
  console.log(`   - Cache hit detected: ${isCacheHit ? 'YES' : 'NO'}`);
  
  if (isCacheHit) {
    metrics.cacheHits++;
  } else {
    metrics.cacheMisses++;
  }
  
  metrics.requestTimes.push(firstResponseTime, secondResponseTime);
  metrics.successfulRequests += 2;
  
  return { firstResponseTime, secondResponseTime, speedImprovement, isCacheHit };
}

/**
 * Test concurrent database operations
 */
async function testConcurrentDatabaseOps() {
  console.log('\n🗄️  Testing Concurrent Database Operations...');
  
  const concurrentStartTime = Date.now();
  
  // Send requests with different data to test concurrent DB operations
  const promises = [];
  const testVariations = [
    { ...testRequestData, experienceLevel: 'BEGINNER', requestId: 'db-test-1' },
    { ...testRequestData, experienceLevel: 'INTERMEDIATE', requestId: 'db-test-2' },
    { ...testRequestData, experienceLevel: 'ADVANCED', requestId: 'db-test-3' }
  ];
  
  for (const variation of testVariations) {
    promises.push(makeRequest('/api/ai/skills-analysis/comprehensive', variation));
  }
  
  try {
    const results = await Promise.all(promises);
    const concurrentEndTime = Date.now();
    const totalConcurrentTime = concurrentEndTime - concurrentStartTime;
    
    const successfulResults = results.filter(r => r.statusCode === 200);
    const avgResponseTime = successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length;
    
    console.log(`✅ Concurrent DB operations completed in ${totalConcurrentTime}ms`);
    console.log(`   - Successful requests: ${successfulResults.length}/${testVariations.length}`);
    console.log(`   - Average response time: ${avgResponseTime.toFixed(2)}ms`);
    
    metrics.successfulRequests += successfulResults.length;
    metrics.errors += (testVariations.length - successfulResults.length);
    metrics.concurrentOperations = successfulResults.length;
    
    return { avgResponseTime, totalConcurrentTime, successfulOperations: successfulResults.length };
    
  } catch (error) {
    console.error(`❌ Concurrent DB testing failed: ${error instanceof Error ? error.message : String(error)}`);
    metrics.errors += testVariations.length;
    throw error;
  }
}

/**
 * Test error handling and edge cases
 */
async function testErrorHandling() {
  console.log('\n⚠️  Testing Error Handling and Edge Cases...');
  
  const errorTests = [
    {
      name: 'Invalid career path',
      data: { ...testRequestData, targetCareerPath: { careerPathName: 'NonExistentCareer' } }
    },
    {
      name: 'Missing required fields',
      data: { targetCareerPath: { careerPathName: 'Software Engineer' } }
    },
    {
      name: 'Invalid skill levels',
      data: { 
        ...testRequestData, 
        currentSkills: [{ name: 'JavaScript', level: 'INVALID_LEVEL' }] 
      }
    }
  ];
  
  let errorHandlingScore = 0;
  
  for (const test of errorTests) {
    try {
      console.log(`   Testing: ${test.name}...`);
      const result = await makeRequest('/api/ai/skills-analysis/comprehensive', test.data);
      
      if (result.statusCode >= 400 && result.statusCode < 500) {
        console.log(`   ✅ Properly handled with status ${result.statusCode}`);
        errorHandlingScore++;
      } else {
        console.log(`   ⚠️  Unexpected status ${result.statusCode}`);
      }
    } catch (error) {
      console.log(`   ✅ Properly rejected: ${error instanceof Error ? error.message : String(error)}`);
      errorHandlingScore++;
    }
  }
  
  console.log(`   Error handling score: ${errorHandlingScore}/${errorTests.length}`);
  return errorHandlingScore / errorTests.length;
}

/**
 * Generate performance report
 */
function generateReport(testResults) {
  console.log('\n📊 PERFORMANCE OPTIMIZATION REPORT');
  console.log('=====================================');
  
  console.log('\n🎯 PHASE 1 OPTIMIZATION RESULTS:');
  
  // Request Batching Results
  if (testResults.batching) {
    console.log(`\n🔄 Request Batching Service:`);
    console.log(`   - Batching efficiency: ${(testResults.batching.batchingEfficiency * 100).toFixed(1)}%`);
    console.log(`   - Average response time: ${testResults.batching.avgResponseTime.toFixed(2)}ms`);
    console.log(`   - Total batch time: ${testResults.batching.totalBatchTime}ms`);
  }
  
  // Caching Results
  if (testResults.caching) {
    console.log(`\n💾 Enhanced Caching System:`);
    console.log(`   - Cache hit detected: ${testResults.caching.isCacheHit ? 'YES' : 'NO'}`);
    console.log(`   - Speed improvement: ${testResults.caching.speedImprovement.toFixed(2)}%`);
    console.log(`   - First request: ${testResults.caching.firstResponseTime}ms`);
    console.log(`   - Cached request: ${testResults.caching.secondResponseTime}ms`);
  }
  
  // Concurrent Database Operations
  if (testResults.concurrent) {
    console.log(`\n🗄️  Concurrent Database Operations:`);
    console.log(`   - Successful operations: ${testResults.concurrent.successfulOperations}`);
    console.log(`   - Average response time: ${testResults.concurrent.avgResponseTime.toFixed(2)}ms`);
    console.log(`   - Total concurrent time: ${testResults.concurrent.totalConcurrentTime}ms`);
  }
  
  // Error Handling
  if (testResults.errorHandling !== undefined) {
    console.log(`\n⚠️  Error Handling:`);
    console.log(`   - Error handling score: ${(testResults.errorHandling * 100).toFixed(1)}%`);
  }
  
  // Overall Metrics
  console.log(`\n📈 OVERALL METRICS:`);
  console.log(`   - Total successful requests: ${metrics.successfulRequests}`);
  console.log(`   - Total errors: ${metrics.errors}`);
  console.log(`   - Success rate: ${((metrics.successfulRequests / (metrics.successfulRequests + metrics.errors)) * 100).toFixed(1)}%`);
  
  if (metrics.requestTimes.length > 0) {
    const avgResponseTime = metrics.requestTimes.reduce((sum, time) => sum + time, 0) / metrics.requestTimes.length;
    const minResponseTime = Math.min(...metrics.requestTimes);
    const maxResponseTime = Math.max(...metrics.requestTimes);
    
    console.log(`   - Average response time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`   - Min response time: ${minResponseTime}ms`);
    console.log(`   - Max response time: ${maxResponseTime}ms`);
  }
  
  // Performance Targets Assessment
  console.log(`\n🎯 PERFORMANCE TARGETS ASSESSMENT:`);
  const targetCacheHitRate = 0.8; // 80%
  const actualCacheHitRate = metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses);
  
  if (testResults.caching && testResults.caching.isCacheHit) {
    console.log(`   ✅ Cache hit rate target: ACHIEVED (detected cache hits)`);
  } else {
    console.log(`   ⚠️  Cache hit rate target: NEEDS VALIDATION (${(actualCacheHitRate * 100).toFixed(1)}%)`);
  }
  
  if (testResults.batching && testResults.batching.batchingEfficiency > 0.5) {
    console.log(`   ✅ Request batching: WORKING (${(testResults.batching.batchingEfficiency * 100).toFixed(1)}% efficiency)`);
  } else {
    console.log(`   ⚠️  Request batching: NEEDS IMPROVEMENT`);
  }
  
  if (testResults.concurrent && testResults.concurrent.successfulOperations >= 3) {
    console.log(`   ✅ Concurrent operations: WORKING (${testResults.concurrent.successfulOperations} operations)`);
  } else {
    console.log(`   ⚠️  Concurrent operations: NEEDS VALIDATION`);
  }
}

/**
 * Main test execution
 */
async function runPerformanceTests() {
  console.log('🚀 STARTING COMPREHENSIVE SKILLS ANALYSIS API OPTIMIZATION TESTS');
  console.log('================================================================');
  
  const testResults = {};
  
  try {
    // Test 1: Request Batching
    testResults.batching = await testRequestBatching();
    
    // Test 2: Caching
    testResults.caching = await testCaching();
    
    // Test 3: Concurrent Database Operations
    testResults.concurrent = await testConcurrentDatabaseOps();
    
    // Test 4: Error Handling
    testResults.errorHandling = await testErrorHandling();
    
    // Generate comprehensive report
    generateReport(testResults);
    
    console.log('\n✅ ALL TESTS COMPLETED SUCCESSFULLY');
    
  } catch (error) {
    console.error(`\n❌ TEST EXECUTION FAILED: ${error instanceof Error ? error.message : String(error)}`);
    console.error(error instanceof Error ? error.stack : String(error));
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runPerformanceTests().catch(console.error);
}

module.exports = { runPerformanceTests, testRequestBatching, testCaching, testConcurrentDatabaseOps };
