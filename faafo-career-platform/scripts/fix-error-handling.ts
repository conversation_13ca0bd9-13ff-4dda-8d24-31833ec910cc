#!/usr/bin/env tsx

/**
 * Script to fix error handling type issues across the codebase
 */

import { readFileSync, writeFileSync } from 'fs';
import { glob } from 'glob';

class ErrorHandlingFixer {
  private fixedFiles = 0;
  private totalReplacements = 0;

  async fixErrorHandling() {
    console.log('🔧 FIXING ERROR HANDLING TYPE ISSUES');
    console.log('====================================\n');

    // Find all TypeScript and JavaScript files that might have error handling issues
    const files = await glob('**/*.{ts,tsx,js,jsx}', {
      ignore: ['node_modules/**', 'dist/**', '.next/**', 'scripts/**'],
      cwd: process.cwd()
    });

    console.log(`Found ${files.length} files to check...\n`);

    for (const file of files) {
      await this.fixFileErrorHandling(file);
    }

    console.log(`\n✅ COMPLETED:`);
    console.log(`   Files fixed: ${this.fixedFiles}`);
    console.log(`   Total replacements: ${this.totalReplacements}`);
  }

  private async fixFileErrorHandling(filePath: string) {
    try {
      const content = readFileSync(filePath, 'utf-8');
      let newContent = content;
      let fileChanged = false;

      // Pattern 1: error.message where error is unknown
      const errorMessagePattern = /(\w+)\.message/g;
      const errorMessageMatches = content.match(errorMessagePattern);
      
      if (errorMessageMatches) {
        // Replace with type-safe error handling
        newContent = newContent.replace(
          /(\w+)\.message/g,
          (match, errorVar) => {
            // Only replace if it's in a catch block context
            if (content.includes(`catch (${errorVar})`)) {
              this.totalReplacements++;
              return `${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar})`;
            }
            return match;
          }
        );
      }

      // Pattern 2: Direct error property access in catch blocks
      const catchBlockPattern = /catch\s*\(\s*(\w+)\s*\)\s*{[^}]*}/g;
      const catchMatches = content.match(catchBlockPattern);
      
      if (catchMatches) {
        for (const catchBlock of catchMatches) {
          const errorVarMatch = catchBlock.match(/catch\s*\(\s*(\w+)\s*\)/);
          if (errorVarMatch) {
            const errorVar = errorVarMatch[1];
            
            // Replace unsafe error property access
            const unsafePatterns = [
              new RegExp(`${errorVar}\\.message`, 'g'),
              new RegExp(`${errorVar}\\.stack`, 'g'),
              new RegExp(`${errorVar}\\.name`, 'g')
            ];

            for (const pattern of unsafePatterns) {
              if (pattern.test(newContent)) {
                const property = pattern.source.split('\\.')[1];
                newContent = newContent.replace(
                  pattern,
                  `${errorVar} instanceof Error ? ${errorVar}.${property} : String(${errorVar})`
                );
                this.totalReplacements++;
                fileChanged = true;
              }
            }
          }
        }
      }

      // Pattern 3: Error includes checks
      newContent = newContent.replace(
        /(\w+)\.message\.includes\(/g,
        (match, errorVar) => {
          if (content.includes(`catch (${errorVar})`)) {
            this.totalReplacements++;
            return `(${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar})).includes(`;
          }
          return match;
        }
      );

      if (newContent !== content) {
        writeFileSync(filePath, newContent, 'utf-8');
        this.fixedFiles++;
        console.log(`✅ Fixed: ${filePath}`);
      }

    } catch (error) {
      console.log(`❌ Error processing ${filePath}:`, error instanceof Error ? error.message : String(error));
    }
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new ErrorHandlingFixer();
  fixer.fixErrorHandling().catch(console.error);
}

export { ErrorHandlingFixer };
