#!/usr/bin/env node
/**
 * Import Standardization Script
 * Converts relative imports to absolute imports and standardizes import ordering
 */

const fs = require('fs');
const path = require('path');

class ImportStandardizer {
  constructor() {
    this.projectRoot = process.cwd();
    this.srcPath = path.join(this.projectRoot, 'src');
    this.fixedFiles = 0;
    this.totalReplacements = 0;
  }

  /**
   * Convert relative import to absolute import
   */
  convertToAbsolute(filePath, importPath) {
    // Skip if already absolute or external
    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {
      return importPath;
    }

    const fileDir = path.dirname(filePath);
    const resolvedPath = path.resolve(fileDir, importPath);
    
    // Convert to relative to src directory
    const relativePath = path.relative(this.srcPath, resolvedPath);
    
    // Convert to absolute import with @/ prefix
    return '@/' + relativePath.replace(/\\/g, '/');
  }

  /**
   * Standardize import order
   */
  standardizeImportOrder(content) {
    const lines = content.split('\n');
    const imports = [];
    const nonImports = [];
    let inImportSection = true;

    for (const line of lines) {
      if (line.trim().startsWith('import ') || line.trim().startsWith('export ') && line.includes('from')) {
        if (inImportSection) {
          imports.push(line);
        } else {
          nonImports.push(line);
        }
      } else if (line.trim() === '' && inImportSection) {
        // Keep empty lines in import section
        imports.push(line);
      } else {
        inImportSection = false;
        nonImports.push(line);
      }
    }

    // Sort imports: external first, then internal
    const externalImports = imports.filter(imp => 
      imp.includes('from \'') && !imp.includes('from \'@/') && !imp.includes('from \'./') && !imp.includes('from \'../')
    );
    const internalImports = imports.filter(imp => 
      imp.includes('from \'@/') || imp.includes('from \'./') || imp.includes('from \'../')
    );
    const emptyLines = imports.filter(imp => imp.trim() === '');

    const sortedImports = [
      ...externalImports.sort(),
      ...(externalImports.length > 0 && internalImports.length > 0 ? [''] : []),
      ...internalImports.sort(),
      ...(internalImports.length > 0 ? [''] : [])
    ];

    return [...sortedImports, ...nonImports].join('\n');
  }

  /**
   * Process a single file
   */
  processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      let newContent = content;
      let fileChanged = false;

      // Convert relative imports to absolute
      const importPattern = /import\s+([^'"`]+)\s+from\s+['"`]([^'"`]+)['"`]/g;
      newContent = newContent.replace(importPattern, (match, importClause, importPath) => {
        const newImportPath = this.convertToAbsolute(filePath, importPath);
        if (newImportPath !== importPath) {
          this.totalReplacements++;
          fileChanged = true;
          return `import ${importClause} from '${newImportPath}'`;
        }
        return match;
      });

      // Convert dynamic imports
      const dynamicImportPattern = /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
      newContent = newContent.replace(dynamicImportPattern, (match, importPath) => {
        const newImportPath = this.convertToAbsolute(filePath, importPath);
        if (newImportPath !== importPath) {
          this.totalReplacements++;
          fileChanged = true;
          return `import('${newImportPath}')`;
        }
        return match;
      });

      // Standardize import order
      const orderedContent = this.standardizeImportOrder(newContent);
      if (orderedContent !== newContent) {
        newContent = orderedContent;
        fileChanged = true;
      }

      if (fileChanged) {
        fs.writeFileSync(filePath, newContent, 'utf-8');
        this.fixedFiles++;
        console.log(`✅ Standardized: ${path.relative(this.projectRoot, filePath)}`);
      }

    } catch (error) {
      console.log(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  /**
   * Process all TypeScript/JavaScript files in src directory
   */
  processDirectory(dirPath = this.srcPath) {
    if (!fs.existsSync(dirPath)) {
      console.log(`Directory ${dirPath} does not exist`);
      return;
    }

    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other build directories
        if (!['node_modules', '.next', 'coverage', '.git'].includes(file)) {
          this.processDirectory(filePath);
        }
      } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
        this.processFile(filePath);
      }
    }
  }

  /**
   * Run the standardization process
   */
  run() {
    console.log('🔧 Starting import standardization...');
    console.log(`📁 Processing directory: ${this.srcPath}`);
    
    this.processDirectory();
    
    console.log('\n📊 Import Standardization Summary:');
    console.log(`✅ Files processed: ${this.fixedFiles}`);
    console.log(`🔄 Total replacements: ${this.totalReplacements}`);
    
    if (this.fixedFiles > 0) {
      console.log('\n🎉 Import standardization completed successfully!');
      console.log('📝 All relative imports have been converted to absolute imports');
      console.log('📋 Import order has been standardized');
    } else {
      console.log('\n✨ No import standardization needed - all imports are already properly formatted!');
    }
  }
}

// Run the standardizer
if (require.main === module) {
  const standardizer = new ImportStandardizer();
  standardizer.run();
}

module.exports = { ImportStandardizer };
