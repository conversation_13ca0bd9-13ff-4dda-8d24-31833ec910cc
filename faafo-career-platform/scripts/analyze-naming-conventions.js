#!/usr/bin/env node
/**
 * Naming Convention Analysis Script
 * Analyzes and reports on file naming inconsistencies
 */

const fs = require('fs');
const path = require('path');

class NamingConventionAnalyzer {
  constructor() {
    this.projectRoot = process.cwd();
    this.srcPath = path.join(this.projectRoot, 'src');
    this.issues = [];
    this.stats = {
      totalFiles: 0,
      componentFiles: 0,
      utilityFiles: 0,
      apiFiles: 0,
      testFiles: 0,
      namingIssues: 0
    };
  }

  /**
   * Check if filename follows proper conventions
   */
  analyzeFileName(filePath) {
    const fileName = path.basename(filePath);
    const fileNameWithoutExt = path.basename(filePath, path.extname(filePath));
    const ext = path.extname(filePath);
    const relativePath = path.relative(this.srcPath, filePath);
    
    const issues = [];

    // Component files should be PascalCase
    if (ext === '.tsx' && !fileName.includes('.test.') && !fileName.includes('.spec.')) {
      this.stats.componentFiles++;
      
      if (!/^[A-Z][a-zA-Z0-9]*\.tsx$/.test(fileName)) {
        issues.push({
          type: 'COMPONENT_NAMING',
          severity: 'MEDIUM',
          message: 'Component files should use PascalCase (e.g., UserProfile.tsx)',
          suggestion: this.toPascalCase(fileNameWithoutExt) + ext
        });
      }
    }

    // API route files should be kebab-case or route.ts
    if (relativePath.includes('api/') && ext === '.ts') {
      this.stats.apiFiles++;
      
      if (fileName !== 'route.ts' && !/^[a-z][a-z0-9-]*\.ts$/.test(fileName)) {
        issues.push({
          type: 'API_NAMING',
          severity: 'LOW',
          message: 'API files should use kebab-case or be named route.ts',
          suggestion: this.toKebabCase(fileNameWithoutExt) + ext
        });
      }
    }

    // Utility files should be kebab-case
    if ((relativePath.includes('lib/') || relativePath.includes('utils/')) && ext === '.ts') {
      this.stats.utilityFiles++;
      
      if (!/^[a-z][a-z0-9-]*\.ts$/.test(fileName)) {
        issues.push({
          type: 'UTILITY_NAMING',
          severity: 'LOW',
          message: 'Utility files should use kebab-case',
          suggestion: this.toKebabCase(fileNameWithoutExt) + ext
        });
      }
    }

    // Test files
    if (fileName.includes('.test.') || fileName.includes('.spec.')) {
      this.stats.testFiles++;
    }

    // Check for underscore usage (should be avoided)
    if (fileNameWithoutExt.includes('_')) {
      issues.push({
        type: 'UNDERSCORE_USAGE',
        severity: 'MEDIUM',
        message: 'Avoid underscores in filenames, use kebab-case or camelCase',
        suggestion: this.toKebabCase(fileNameWithoutExt) + ext
      });
    }

    // Check for very short names (not descriptive)
    if (fileNameWithoutExt.length < 3 && fileName !== 'index.ts' && fileName !== 'index.tsx') {
      issues.push({
        type: 'SHORT_NAME',
        severity: 'LOW',
        message: 'Filename is too short, use more descriptive names',
        suggestion: 'Consider a more descriptive name'
      });
    }

    // Check for very long names
    if (fileNameWithoutExt.length > 50) {
      issues.push({
        type: 'LONG_NAME',
        severity: 'LOW',
        message: 'Filename is very long, consider shortening',
        suggestion: 'Consider breaking into smaller modules'
      });
    }

    if (issues.length > 0) {
      this.issues.push({
        file: relativePath,
        issues
      });
      this.stats.namingIssues++;
    }

    this.stats.totalFiles++;
  }

  /**
   * Convert string to PascalCase
   */
  toPascalCase(str) {
    return str
      .replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '')
      .replace(/^(.)/, (_, char) => char.toUpperCase());
  }

  /**
   * Convert string to kebab-case
   */
  toKebabCase(str) {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  }

  /**
   * Process directory recursively
   */
  processDirectory(dirPath = this.srcPath) {
    if (!fs.existsSync(dirPath)) {
      console.log(`Directory ${dirPath} does not exist`);
      return;
    }

    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other build directories
        if (!['node_modules', '.next', 'coverage', '.git'].includes(file)) {
          this.processDirectory(filePath);
        }
      } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
        this.analyzeFileName(filePath);
      }
    }
  }

  /**
   * Generate report
   */
  generateReport() {
    console.log('📋 NAMING CONVENTION ANALYSIS REPORT');
    console.log('=====================================');
    
    console.log('\n📊 File Statistics:');
    console.log(`Total files analyzed: ${this.stats.totalFiles}`);
    console.log(`Component files (.tsx): ${this.stats.componentFiles}`);
    console.log(`Utility files: ${this.stats.utilityFiles}`);
    console.log(`API files: ${this.stats.apiFiles}`);
    console.log(`Test files: ${this.stats.testFiles}`);
    console.log(`Files with naming issues: ${this.stats.namingIssues}`);
    
    const complianceRate = ((this.stats.totalFiles - this.stats.namingIssues) / this.stats.totalFiles * 100).toFixed(1);
    console.log(`\n🎯 Naming compliance rate: ${complianceRate}%`);

    if (this.issues.length > 0) {
      console.log('\n⚠️  NAMING ISSUES FOUND:');
      console.log('========================');
      
      // Group by severity
      const severityGroups = {
        HIGH: [],
        MEDIUM: [],
        LOW: []
      };

      this.issues.forEach(fileIssue => {
        fileIssue.issues.forEach(issue => {
          severityGroups[issue.severity].push({
            file: fileIssue.file,
            ...issue
          });
        });
      });

      // Report by severity
      ['HIGH', 'MEDIUM', 'LOW'].forEach(severity => {
        const issues = severityGroups[severity];
        if (issues.length > 0) {
          console.log(`\n🔴 ${severity} Priority (${issues.length} issues):`);
          issues.forEach(issue => {
            console.log(`   📁 ${issue.file}`);
            console.log(`      ❌ ${issue.message}`);
            if (issue.suggestion !== 'Consider a more descriptive name' && 
                issue.suggestion !== 'Consider breaking into smaller modules') {
              console.log(`      ✅ Suggested: ${issue.suggestion}`);
            }
            console.log('');
          });
        }
      });

      console.log('\n🔧 RECOMMENDED ACTIONS:');
      console.log('=======================');
      console.log('1. Rename component files to PascalCase (e.g., UserProfile.tsx)');
      console.log('2. Use kebab-case for utility and API files');
      console.log('3. Avoid underscores in filenames');
      console.log('4. Use descriptive names (minimum 3 characters)');
      console.log('5. Consider breaking down very long filenames');
      
    } else {
      console.log('\n✅ All files follow proper naming conventions!');
    }

    return {
      totalFiles: this.stats.totalFiles,
      issueCount: this.stats.namingIssues,
      complianceRate: parseFloat(complianceRate),
      issues: this.issues
    };
  }

  /**
   * Run the analysis
   */
  run() {
    console.log('🔍 Starting naming convention analysis...');
    console.log(`📁 Analyzing directory: ${this.srcPath}`);
    
    this.processDirectory();
    return this.generateReport();
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new NamingConventionAnalyzer();
  analyzer.run();
}

module.exports = { NamingConventionAnalyzer };
