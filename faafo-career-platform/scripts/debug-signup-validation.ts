#!/usr/bin/env tsx

/**
 * Debug script to test the signup form validation
 */

import { chromium } from 'playwright';

const BASE_URL = 'http://localhost:3000';

async function debugSignupValidation() {
  console.log('🔍 DEBUGGING SIGNUP FORM VALIDATION');
  console.log('===================================\n');

  const browser = await chromium.launch({ 
    headless: false, 
    slowMo: 1000,
    args: ['--start-maximized']
  });
  
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  const page = await context.newPage();

  try {
    // Step 1: Navigate to signup page
    console.log('1️⃣ Navigating to signup page...');
    await page.goto(`${BASE_URL}/signup`);
    await page.waitForLoadState('networkidle');
    console.log('✅ Signup page loaded');

    // Step 2: Fill invalid email
    console.log('\n2️⃣ Testing email validation...');
    await page.fill('input[type="email"]', 'test@');
    await page.fill('input[id="password"]', 'password123');
    await page.fill('input[id="confirm-password"]', 'password123');
    
    console.log('✅ Filled form with invalid email: test@');

    // Step 3: Try to submit
    console.log('\n3️⃣ Submitting form...');
    await page.click('button[type="submit"]');
    
    // Wait for any validation to appear
    await page.waitForTimeout(2000);
    
    // Check for validation errors
    const validationErrors = await page.locator('.text-red-500').allTextContents();
    console.log('Validation errors found:', validationErrors);
    
    // Check if any error elements exist
    const errorElements = await page.locator('.text-red-500').count();
    console.log('Number of error elements:', errorElements);
    
    // Take screenshot
    await page.screenshot({ path: 'debug-signup-validation.png' });
    console.log('📸 Screenshot saved: debug-signup-validation.png');

    // Step 4: Test password validation
    console.log('\n4️⃣ Testing password validation...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[id="password"]', '123');
    await page.fill('input[id="confirm-password"]', '123');
    
    console.log('✅ Filled form with weak password: 123');
    
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);
    
    const passwordErrors = await page.locator('.text-red-500').allTextContents();
    console.log('Password validation errors found:', passwordErrors);
    
    // Take another screenshot
    await page.screenshot({ path: 'debug-password-validation.png' });
    console.log('📸 Screenshot saved: debug-password-validation.png');

    // Step 5: Check form state
    console.log('\n5️⃣ Checking form state...');
    
    const formData = await page.evaluate(() => {
      const form = document.querySelector('form');
      const emailInput = document.querySelector('input[type="email"]') as HTMLInputElement;
      const passwordInput = document.querySelector('input[id="password"]') as HTMLInputElement;
      const confirmPasswordInput = document.querySelector('input[id="confirm-password"]') as HTMLInputElement;
      const submitButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
      
      return {
        formExists: !!form,
        emailValue: emailInput?.value,
        passwordValue: passwordInput?.value,
        confirmPasswordValue: confirmPasswordInput?.value,
        submitButtonDisabled: submitButton?.disabled,
        submitButtonText: submitButton?.textContent,
        hasNoValidate: form?.hasAttribute('novalidate')
      };
    });
    
    console.log('Form state:', formData);

  } catch (error) {
    console.error('❌ Debug script failed:', error);
    await page.screenshot({ path: 'debug-error.png' });
    console.log('📸 Error screenshot saved: debug-error.png');
  } finally {
    await browser.close();
  }
}

if (require.main === module) {
  debugSignupValidation().catch(console.error);
}

export { debugSignupValidation };
