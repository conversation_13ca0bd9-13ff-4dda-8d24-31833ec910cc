#!/usr/bin/env tsx

/**
 * Database Schema Validation Script
 * Checks Prisma schema consistency, migration issues, and data integrity
 */

import { PrismaClient } from '@prisma/client';
import { readFileSync } from 'fs';
import { join } from 'path';

interface SchemaIssue {
  type: 'MISSING_INDEX' | 'MISSING_CONSTRAINT' | 'ORPHANED_DATA' | 'SCHEMA_DRIFT' | 'MIGRATION_ISSUE' | 'PERFORMANCE_ISSUE';
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  table: string;
  description: string;
  recommendation: string;
  query?: string;
}

class DatabaseSchemaValidator {
  private prisma: PrismaClient;
  private issues: SchemaIssue[] = [];

  constructor() {
    this.prisma = new PrismaClient();
  }

  async validateSchema() {
    console.log('🔍 DATABASE SCHEMA VALIDATION');
    console.log('=============================\n');

    try {
      // Test database connection
      await this.testConnection();
      
      // Check schema consistency
      await this.checkSchemaConsistency();
      
      // Check foreign key integrity
      await this.checkForeignKeyIntegrity();
      
      // Check for orphaned records
      await this.checkOrphanedRecords();
      
      // Check indexes and performance
      await this.checkIndexes();
      
      // Check data integrity constraints
      await this.checkDataIntegrity();
      
      // Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Database validation failed:', error instanceof Error ? error.message : String(error));
    } finally {
      await this.prisma.$disconnect();
    }
  }

  private async testConnection() {
    console.log('🔌 Testing database connection...');
    
    try {
      await this.prisma.$connect();
      const result = await this.prisma.$queryRaw`SELECT 1 as test`;
      console.log('✅ Database connection successful\n');
    } catch (error) {
      this.issues.push({
        type: 'MIGRATION_ISSUE',
        severity: 'CRITICAL',
        table: 'CONNECTION',
        description: 'Cannot connect to database',
        recommendation: 'Check DATABASE_URL and database server status',
      });
      throw error;
    }
  }

  private async checkSchemaConsistency() {
    console.log('📋 Checking schema consistency...');
    
    try {
      // Check if all expected tables exist
      const tables = await this.prisma.$queryRaw<Array<{table_name: string}>>`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name;
      `;
      
      const expectedTables = [
        'User', 'Profile', 'Account', 'Session', 'Assessment', 'CareerPath', 
        'LearningResource', 'Resume', 'InterviewSession', 'FreedomFund',
        'Skill', 'UserSkillProgress', 'SecurityToken', 'RateLimitEntry'
      ];
      
      const existingTables = tables.map(t => t.table_name);
      
      for (const expectedTable of expectedTables) {
        if (!existingTables.includes(expectedTable)) {
          this.issues.push({
            type: 'SCHEMA_DRIFT',
            severity: 'HIGH',
            table: expectedTable,
            description: `Expected table '${expectedTable}' is missing`,
            recommendation: 'Run database migration: npx prisma db push',
          });
        }
      }
      
      console.log(`✅ Found ${existingTables.length} tables\n`);
    } catch (error) {
      console.error('❌ Schema consistency check failed:', error instanceof Error ? error.message : String(error));
    }
  }

  private async checkForeignKeyIntegrity() {
    console.log('🔗 Checking foreign key integrity...');
    
    const foreignKeyChecks = [
      {
        table: 'Profile',
        column: 'userId',
        referencedTable: 'User',
        referencedColumn: 'id',
        query: `
          SELECT COUNT(*) as count 
          FROM "Profile" p 
          LEFT JOIN "User" u ON p."userId" = u.id 
          WHERE u.id IS NULL
        `
      },
      {
        table: 'Assessment',
        column: 'userId',
        referencedTable: 'User',
        referencedColumn: 'id',
        query: `
          SELECT COUNT(*) as count 
          FROM "Assessment" a 
          LEFT JOIN "User" u ON a."userId" = u.id 
          WHERE u.id IS NULL
        `
      },
      {
        table: 'Resume',
        column: 'userId',
        referencedTable: 'User',
        referencedColumn: 'id',
        query: `
          SELECT COUNT(*) as count 
          FROM "Resume" r 
          LEFT JOIN "User" u ON r."userId" = u.id 
          WHERE u.id IS NULL
        `
      },
      {
        table: 'InterviewSession',
        column: 'userId',
        referencedTable: 'User',
        referencedColumn: 'id',
        query: `
          SELECT COUNT(*) as count 
          FROM "InterviewSession" i 
          LEFT JOIN "User" u ON i."userId" = u.id 
          WHERE u.id IS NULL
        `
      }
    ];

    for (const check of foreignKeyChecks) {
      try {
        const result = await this.prisma.$queryRawUnsafe<Array<{count: bigint}>>(check.query);
        const orphanedCount = Number(result[0]?.count || 0);
        
        if (orphanedCount > 0) {
          this.issues.push({
            type: 'ORPHANED_DATA',
            severity: 'HIGH',
            table: check.table,
            description: `Found ${orphanedCount} orphaned records in ${check.table} referencing non-existent ${check.referencedTable}`,
            recommendation: `Clean up orphaned records or restore missing ${check.referencedTable} records`,
            query: check.query
          });
        }
      } catch (error) {
        console.warn(`Warning: Could not check foreign key integrity for ${check.table}:`, error instanceof Error ? error.message : String(error));
      }
    }
    
    console.log('✅ Foreign key integrity check completed\n');
  }

  private async checkOrphanedRecords() {
    console.log('🧹 Checking for orphaned records...');
    
    // Check for users without profiles (if profiles are expected)
    try {
      const usersWithoutProfiles = await this.prisma.$queryRaw<Array<{count: bigint}>>`
        SELECT COUNT(*) as count 
        FROM "User" u 
        LEFT JOIN "Profile" p ON u.id = p."userId" 
        WHERE p."userId" IS NULL
      `;
      
      const count = Number(usersWithoutProfiles[0]?.count || 0);
      if (count > 0) {
        this.issues.push({
          type: 'ORPHANED_DATA',
          severity: 'MEDIUM',
          table: 'User',
          description: `Found ${count} users without profiles`,
          recommendation: 'Consider creating default profiles for these users or investigate if this is expected',
        });
      }
    } catch (error) {
      console.warn('Warning: Could not check for users without profiles:', error instanceof Error ? error.message : String(error));
    }
    
    console.log('✅ Orphaned records check completed\n');
  }

  private async checkIndexes() {
    console.log('📊 Checking database indexes...');
    
    try {
      // Get all indexes
      const indexes = await this.prisma.$queryRaw<Array<{
        tablename: string;
        indexname: string;
        indexdef: string;
      }>>`
        SELECT tablename, indexname, indexdef 
        FROM pg_indexes 
        WHERE schemaname = 'public' 
        ORDER BY tablename, indexname;
      `;
      
      // Check for missing critical indexes
      const criticalIndexes = [
        { table: 'User', column: 'email', reason: 'Frequent lookups during authentication' },
        { table: 'Assessment', column: 'userId', reason: 'User-specific queries' },
        { table: 'Resume', column: 'userId', reason: 'User-specific queries' },
        { table: 'InterviewSession', column: 'userId', reason: 'User-specific queries' },
        { table: 'SecurityToken', column: 'expiresAt', reason: 'Token cleanup queries' },
        { table: 'RateLimitEntry', column: 'identifier', reason: 'Rate limiting lookups' },
      ];
      
      for (const criticalIndex of criticalIndexes) {
        const hasIndex = indexes.some(idx => 
          idx.tablename === criticalIndex.table && 
          idx.indexdef.includes(criticalIndex.column)
        );
        
        if (!hasIndex) {
          this.issues.push({
            type: 'MISSING_INDEX',
            severity: 'MEDIUM',
            table: criticalIndex.table,
            description: `Missing index on ${criticalIndex.table}.${criticalIndex.column}`,
            recommendation: `Add index for performance: CREATE INDEX ON "${criticalIndex.table}" ("${criticalIndex.column}");`,
          });
        }
      }
      
      console.log(`✅ Found ${indexes.length} indexes\n`);
    } catch (error) {
      console.warn('Warning: Could not check indexes:', error instanceof Error ? error.message : String(error));
    }
  }

  private async checkDataIntegrity() {
    console.log('🔍 Checking data integrity...');
    
    const integrityChecks = [
      {
        name: 'Invalid email formats',
        query: `
          SELECT COUNT(*) as count 
          FROM "User" 
          WHERE email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
        `,
        table: 'User'
      },
      {
        name: 'Future created dates',
        query: `
          SELECT COUNT(*) as count 
          FROM "User" 
          WHERE "createdAt" > NOW()
        `,
        table: 'User'
      },
      {
        name: 'Invalid assessment scores',
        query: `
          SELECT COUNT(*) as count 
          FROM "Assessment" 
          WHERE score < 0 OR score > 100
        `,
        table: 'Assessment'
      }
    ];

    for (const check of integrityChecks) {
      try {
        const result = await this.prisma.$queryRawUnsafe<Array<{count: bigint}>>(check.query);
        const invalidCount = Number(result[0]?.count || 0);
        
        if (invalidCount > 0) {
          this.issues.push({
            type: 'ORPHANED_DATA',
            severity: 'HIGH',
            table: check.table,
            description: `Found ${invalidCount} records with ${check.name.toLowerCase()}`,
            recommendation: `Review and clean up invalid data in ${check.table}`,
            query: check.query
          });
        }
      } catch (error) {
        console.warn(`Warning: Could not run integrity check '${check.name}':`, error instanceof Error ? error.message : String(error));
      }
    }
    
    console.log('✅ Data integrity check completed\n');
  }

  private generateReport() {
    console.log('📊 DATABASE SCHEMA VALIDATION REPORT');
    console.log('====================================\n');
    
    // Summary statistics
    const criticalIssues = this.issues.filter(i => i.severity === 'CRITICAL');
    const highIssues = this.issues.filter(i => i.severity === 'HIGH');
    const mediumIssues = this.issues.filter(i => i.severity === 'MEDIUM');
    const lowIssues = this.issues.filter(i => i.severity === 'LOW');
    
    console.log('📈 SUMMARY:');
    console.log(`   Total Issues: ${this.issues.length}`);
    console.log(`   🔴 Critical: ${criticalIssues.length}`);
    console.log(`   🟠 High: ${highIssues.length}`);
    console.log(`   🟡 Medium: ${mediumIssues.length}`);
    console.log(`   🟢 Low: ${lowIssues.length}\n`);
    
    if (this.issues.length === 0) {
      console.log('🎉 No database schema issues found! Your database is in excellent condition.\n');
      return;
    }
    
    // Detailed issues
    console.log('🔍 DETAILED ISSUES:\n');
    
    for (const issue of this.issues) {
      const icon = issue.severity === 'CRITICAL' ? '🔴' : 
                   issue.severity === 'HIGH' ? '🟠' : 
                   issue.severity === 'MEDIUM' ? '🟡' : '🟢';
      
      console.log(`${icon} ${issue.table} - ${issue.type}`);
      console.log(`   Issue: ${issue.description}`);
      console.log(`   Fix: ${issue.recommendation}`);
      if (issue.query) {
        console.log(`   Query: ${issue.query.trim()}`);
      }
      console.log('');
    }
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    console.log('==================');
    console.log('1. Run database migrations: npx prisma db push');
    console.log('2. Add missing indexes for performance');
    console.log('3. Clean up orphaned records');
    console.log('4. Validate and fix data integrity issues');
    console.log('5. Set up regular database maintenance tasks');
    console.log('6. Monitor database performance metrics');
    console.log('7. Implement automated backup verification');
  }
}

// Run the validation
if (require.main === module) {
  const validator = new DatabaseSchemaValidator();
  validator.validateSchema().catch(console.error);
}

export { DatabaseSchemaValidator };
