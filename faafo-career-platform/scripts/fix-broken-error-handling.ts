#!/usr/bin/env tsx

/**
 * Fix Broken Error Handling Script
 * Cleans up the malformed ternary expressions created by the previous script
 */

import { readFileSync, writeFileSync } from 'fs';
import { glob } from 'glob';

class ErrorHandlingFixer {
  private fixedFiles = 0;
  private totalReplacements = 0;

  async fixBrokenErrorHandling() {
    console.log('🔧 FIXING BROKEN ERROR HANDLING');
    console.log('===============================\n');

    // Find all TypeScript and JavaScript files
    const files = await glob('**/*.{ts,tsx,js,jsx}', {
      ignore: ['node_modules/**', 'dist/**', '.next/**', 'scripts/**'],
      cwd: process.cwd()
    });

    console.log(`Found ${files.length} files to check...\n`);

    for (const file of files) {
      await this.fixFileErrorHandling(file);
    }

    console.log(`\n✅ COMPLETED:`);
    console.log(`   Files fixed: ${this.fixedFiles}`);
    console.log(`   Total replacements: ${this.totalReplacements}`);
  }

  private async fixFileErrorHandling(filePath: string) {
    try {
      const content = readFileSync(filePath, 'utf-8');
      let newContent = content;
      let fileChanged = false;

      // Pattern 1: Fix nested ternary expressions like:
      // error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error)
      const nestedTernaryPattern = /(\w+) instanceof Error \? \1 instanceof Error \? (\1\.(?:message|name|stack)) : String\(\1\) : String\(\1\)/g;
      
      newContent = newContent.replace(nestedTernaryPattern, (match, errorVar, property) => {
        this.totalReplacements++;
        fileChanged = true;
        return `${errorVar} instanceof Error ? ${property} : String(${errorVar})`;
      });

      // Pattern 2: Fix extremely nested ternary expressions
      const extremelyNestedPattern = /(\w+) instanceof Error \? (\1 instanceof Error \? )+(\1\.(?:message|name|stack))(?: : String\(\1\))+/g;
      
      newContent = newContent.replace(extremelyNestedPattern, (match, errorVar, nested, property) => {
        this.totalReplacements++;
        fileChanged = true;
        return `${errorVar} instanceof Error ? ${property} : String(${errorVar})`;
      });

      // Pattern 3: Fix assignment to ternary expressions (invalid syntax)
      const invalidAssignmentPattern = /(\w+) instanceof Error \? (\1\.(?:name|stack)) : String\(\1\) = /g;
      
      newContent = newContent.replace(invalidAssignmentPattern, (match, errorVar, property) => {
        this.totalReplacements++;
        fileChanged = true;
        const propName = property.split('.')[1];
        return `if (${errorVar} instanceof Error) { ${errorVar}.${propName} = `;
      });

      // Pattern 4: Fix complex nested error checks in conditions
      const complexConditionPattern = /(\w+) instanceof (\w+) && (\1) instanceof Error \? \3 instanceof Error \? \3 instanceof Error \? (\3\.message) : String\(\3\) : String\(\3\) : String\(\3\)/g;
      
      newContent = newContent.replace(complexConditionPattern, (match, errorVar, errorType, errorVar2, property) => {
        this.totalReplacements++;
        fileChanged = true;
        return `${errorVar} instanceof ${errorType} && ${errorVar} instanceof Error ? ${property} : String(${errorVar})`;
      });

      // Pattern 5: Fix setError calls with nested ternaries
      const setErrorPattern = /setError\((\w+) instanceof Error \? \1 instanceof Error \? (\1\.message) : String\(\1\) : String\(\1\)\);/g;
      
      newContent = newContent.replace(setErrorPattern, (match, errorVar, property) => {
        this.totalReplacements++;
        fileChanged = true;
        return `setError(${errorVar} instanceof Error ? ${property} : String(${errorVar}));`;
      });

      // Pattern 6: Fix extremely complex nested patterns
      const ultraComplexPattern = /(\w+) instanceof Error \? (\1 instanceof Error \? ){2,}(\1\.(?:message|name|stack))(?: : String\(\1\)){2,}/g;
      
      newContent = newContent.replace(ultraComplexPattern, (match, errorVar, nested, property) => {
        this.totalReplacements++;
        fileChanged = true;
        return `${errorVar} instanceof Error ? ${property} : String(${errorVar})`;
      });

      // Pattern 7: Fix includes() calls with nested ternaries
      const includesPattern = /(\w+) instanceof Error \? (\1 instanceof Error \? ){1,}(\1\.message) : String\(\1\)(?: : String\(\1\))*\.includes\(/g;
      
      newContent = newContent.replace(includesPattern, (match, errorVar, nested, property) => {
        this.totalReplacements++;
        fileChanged = true;
        return `(${errorVar} instanceof Error ? ${property} : String(${errorVar})).includes(`;
      });

      if (fileChanged) {
        writeFileSync(filePath, newContent, 'utf-8');
        this.fixedFiles++;
        console.log(`✅ Fixed: ${filePath}`);
      }

    } catch (error) {
      console.log(`❌ Error processing ${filePath}:`, error instanceof Error ? error.message : String(error));
    }
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new ErrorHandlingFixer();
  fixer.fixBrokenErrorHandling().catch(console.error);
}

export { ErrorHandlingFixer };
