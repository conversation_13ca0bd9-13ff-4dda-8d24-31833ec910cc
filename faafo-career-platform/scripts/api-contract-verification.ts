#!/usr/bin/env tsx

/**
 * API Contract Verification Script
 * Tests all API endpoints with various inputs and verifies response schemas
 */

import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

interface ApiEndpoint {
  path: string;
  method: string;
  file: string;
  requiresAuth: boolean;
  requiresAdmin: boolean;
  hasValidation: boolean;
  hasRateLimit: boolean;
  hasCSRFProtection: boolean;
  responseSchema?: any;
  requestSchema?: any;
}

interface ApiContractIssue {
  endpoint: string;
  type: 'MISSING_VALIDATION' | 'MISSING_RATE_LIMIT' | 'MISSING_CSRF' | 'MISSING_AUTH' | 'MISSING_ERROR_HANDLING' | 'INCONSISTENT_RESPONSE';
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  description: string;
  recommendation: string;
}

class ApiContractVerifier {
  private endpoints: ApiEndpoint[] = [];
  private issues: ApiContractIssue[] = [];
  private apiDir = 'src/app/api';

  async verifyApiContracts() {
    console.log('🔍 API CONTRACT VERIFICATION');
    console.log('============================\n');

    // Discover all API endpoints
    await this.discoverEndpoints();
    
    // Analyze each endpoint
    await this.analyzeEndpoints();
    
    // Generate report
    this.generateReport();
  }

  private async discoverEndpoints() {
    console.log('📡 Discovering API endpoints...\n');
    
    const apiPath = join(process.cwd(), this.apiDir);
    this.scanDirectory(apiPath, '');
    
    console.log(`Found ${this.endpoints.length} API endpoints\n`);
  }

  private scanDirectory(dirPath: string, relativePath: string) {
    try {
      const items = readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = join(dirPath, item);
        const stat = statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Skip certain directories
          if (item === '__tests__' || item === 'node_modules') continue;
          
          this.scanDirectory(fullPath, join(relativePath, item));
        } else if (item === 'route.ts' || item === 'route.js') {
          // Found an API route
          const apiPath = '/api' + (relativePath ? '/' + relativePath.replace(/\\/g, '/') : '');
          this.analyzeRouteFile(fullPath, apiPath);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not scan directory ${dirPath}:`, error instanceof Error ? error.message : String(error));
    }
  }

  private analyzeRouteFile(filePath: string, apiPath: string) {
    try {
      const content = readFileSync(filePath, 'utf-8');
      
      // Extract HTTP methods
      const methods = this.extractMethods(content);
      
      for (const method of methods) {
        const endpoint: ApiEndpoint = {
          path: apiPath,
          method,
          file: filePath,
          requiresAuth: this.checkAuthRequirement(content, apiPath),
          requiresAdmin: this.checkAdminRequirement(content, apiPath),
          hasValidation: this.checkValidation(content),
          hasRateLimit: this.checkRateLimit(content),
          hasCSRFProtection: this.checkCSRFProtection(content),
        };
        
        this.endpoints.push(endpoint);
      }
    } catch (error) {
      console.warn(`Warning: Could not analyze route file ${filePath}:`, error instanceof Error ? error.message : String(error));
    }
  }

  private extractMethods(content: string): string[] {
    const methods: string[] = [];
    const methodRegex = /export\s+const\s+(GET|POST|PUT|DELETE|PATCH)\s*=/g;
    let match;
    
    while ((match = methodRegex.exec(content)) !== null) {
      methods.push(match[1]);
    }
    
    return methods;
  }

  private checkAuthRequirement(content: string, apiPath: string): boolean {
    // Check middleware.ts for protected routes
    const protectedRoutes = [
      '/api/assessment',
      '/api/profile',
      '/api/freedom-fund',
      '/api/learning-progress',
      '/api/personalized-resources',
      '/api/progress-tracker',
      '/api/recommendations',
      '/api/resource-ratings',
      '/api/interview-practice',
      '/api/resume-builder',
      '/api/tools',
      '/api/audit',
    ];
    
    return protectedRoutes.some(route => apiPath.startsWith(route)) ||
           content.includes('getServerSession') ||
           content.includes('UnifiedAuthenticationService');
  }

  private checkAdminRequirement(content: string, apiPath: string): boolean {
    const adminRoutes = ['/api/admin'];
    
    return adminRoutes.some(route => apiPath.startsWith(route)) ||
           content.includes('isAdminUser') ||
           content.includes('requireAdmin');
  }

  private checkValidation(content: string): boolean {
    return content.includes('z.object') ||
           content.includes('zod') ||
           content.includes('ValidationPipelines') ||
           content.includes('validateFormData') ||
           content.includes('Schema');
  }

  private checkRateLimit(content: string): boolean {
    return content.includes('withRateLimit') ||
           content.includes('rateLimiters') ||
           content.includes('rateLimit');
  }

  private checkCSRFProtection(content: string): boolean {
    return content.includes('withCSRFProtection') ||
           content.includes('csrf');
  }

  private async analyzeEndpoints() {
    console.log('🔬 Analyzing endpoint contracts...\n');
    
    for (const endpoint of this.endpoints) {
      this.analyzeEndpointSecurity(endpoint);
      this.analyzeEndpointValidation(endpoint);
      this.analyzeEndpointConsistency(endpoint);
    }
  }

  private analyzeEndpointSecurity(endpoint: ApiEndpoint) {
    // Check for missing authentication on sensitive endpoints
    if (this.isSensitiveEndpoint(endpoint.path) && !endpoint.requiresAuth) {
      this.issues.push({
        endpoint: `${endpoint.method} ${endpoint.path}`,
        type: 'MISSING_AUTH',
        severity: 'HIGH',
        description: 'Sensitive endpoint lacks authentication',
        recommendation: 'Add authentication middleware or move to protected routes'
      });
    }

    // Check for missing CSRF protection on state-changing operations
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(endpoint.method) && !endpoint.hasCSRFProtection) {
      this.issues.push({
        endpoint: `${endpoint.method} ${endpoint.path}`,
        type: 'MISSING_CSRF',
        severity: 'MEDIUM',
        description: 'State-changing endpoint lacks CSRF protection',
        recommendation: 'Add withCSRFProtection middleware'
      });
    }

    // Check for missing rate limiting
    if (!endpoint.hasRateLimit && !this.isInternalEndpoint(endpoint.path)) {
      this.issues.push({
        endpoint: `${endpoint.method} ${endpoint.path}`,
        type: 'MISSING_RATE_LIMIT',
        severity: 'MEDIUM',
        description: 'Endpoint lacks rate limiting',
        recommendation: 'Add withRateLimit middleware'
      });
    }
  }

  private analyzeEndpointValidation(endpoint: ApiEndpoint) {
    // Check for missing input validation on endpoints that accept data
    if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && !endpoint.hasValidation) {
      this.issues.push({
        endpoint: `${endpoint.method} ${endpoint.path}`,
        type: 'MISSING_VALIDATION',
        severity: 'HIGH',
        description: 'Data-accepting endpoint lacks input validation',
        recommendation: 'Add Zod schema validation or ValidationPipelines'
      });
    }
  }

  private analyzeEndpointConsistency(endpoint: ApiEndpoint) {
    // Check for consistent error handling
    try {
      const content = readFileSync(endpoint.file, 'utf-8');
      
      if (!content.includes('withUnifiedErrorHandling')) {
        this.issues.push({
          endpoint: `${endpoint.method} ${endpoint.path}`,
          type: 'MISSING_ERROR_HANDLING',
          severity: 'MEDIUM',
          description: 'Endpoint lacks unified error handling',
          recommendation: 'Wrap handler with withUnifiedErrorHandling'
        });
      }
    } catch (error) {
      // Skip if file can't be read
    }
  }

  private isSensitiveEndpoint(path: string): boolean {
    const sensitivePatterns = [
      '/api/profile',
      '/api/freedom-fund',
      '/api/assessment',
      '/api/admin',
      '/api/audit',
      '/api/interview-practice',
      '/api/resume-builder'
    ];
    
    return sensitivePatterns.some(pattern => path.startsWith(pattern));
  }

  private isInternalEndpoint(path: string): boolean {
    const internalPatterns = [
      '/api/health',
      '/api/test',
      '/api/csrf-token'
    ];
    
    return internalPatterns.some(pattern => path.startsWith(pattern));
  }

  private generateReport() {
    console.log('📊 API CONTRACT VERIFICATION REPORT');
    console.log('===================================\n');
    
    // Summary statistics
    const totalEndpoints = this.endpoints.length;
    const secureEndpoints = this.endpoints.filter(e => e.requiresAuth && e.hasRateLimit && e.hasCSRFProtection).length;
    const validatedEndpoints = this.endpoints.filter(e => e.hasValidation || e.method === 'GET').length;
    
    console.log('📈 SUMMARY:');
    console.log(`   Total Endpoints: ${totalEndpoints}`);
    console.log(`   Secure Endpoints: ${secureEndpoints}/${totalEndpoints} (${Math.round(secureEndpoints/totalEndpoints*100)}%)`);
    console.log(`   Validated Endpoints: ${validatedEndpoints}/${totalEndpoints} (${Math.round(validatedEndpoints/totalEndpoints*100)}%)`);
    console.log(`   Total Issues: ${this.issues.length}\n`);
    
    // Issues by severity
    const highIssues = this.issues.filter(i => i.severity === 'HIGH');
    const mediumIssues = this.issues.filter(i => i.severity === 'MEDIUM');
    const lowIssues = this.issues.filter(i => i.severity === 'LOW');
    
    console.log('🚨 ISSUES BY SEVERITY:');
    console.log(`   🔴 High: ${highIssues.length}`);
    console.log(`   🟡 Medium: ${mediumIssues.length}`);
    console.log(`   🟢 Low: ${lowIssues.length}\n`);
    
    // Detailed issues
    if (this.issues.length > 0) {
      console.log('🔍 DETAILED ISSUES:\n');
      
      for (const issue of this.issues) {
        const icon = issue.severity === 'HIGH' ? '🔴' : issue.severity === 'MEDIUM' ? '🟡' : '🟢';
        console.log(`${icon} ${issue.endpoint}`);
        console.log(`   Type: ${issue.type}`);
        console.log(`   Issue: ${issue.description}`);
        console.log(`   Fix: ${issue.recommendation}\n`);
      }
    }
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    console.log('==================');
    console.log('1. Add authentication to all sensitive endpoints');
    console.log('2. Implement CSRF protection on state-changing operations');
    console.log('3. Add rate limiting to prevent abuse');
    console.log('4. Use Zod schemas for input validation');
    console.log('5. Wrap all handlers with withUnifiedErrorHandling');
    console.log('6. Document API contracts with OpenAPI/Swagger');
    console.log('7. Add comprehensive API tests');
  }
}

// Run the verification
if (require.main === module) {
  const verifier = new ApiContractVerifier();
  verifier.verifyApiContracts().catch(console.error);
}

export { ApiContractVerifier };
