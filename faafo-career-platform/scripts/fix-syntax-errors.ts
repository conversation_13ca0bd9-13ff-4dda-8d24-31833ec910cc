#!/usr/bin/env tsx

/**
 * Fix Syntax Errors Script
 * Fixes remaining syntax errors from malformed ternary expressions
 */

import { readFileSync, writeFileSync } from 'fs';
import { glob } from 'glob';

class SyntaxErrorFixer {
  private fixedFiles = 0;
  private totalReplacements = 0;

  async fixSyntaxErrors() {
    console.log('🔧 FIXING SYNTAX ERRORS');
    console.log('=======================\n');

    // Find all TypeScript and JavaScript files
    const files = await glob('**/*.{ts,tsx,js,jsx}', {
      ignore: ['node_modules/**', 'dist/**', '.next/**', 'scripts/**'],
      cwd: process.cwd()
    });

    console.log(`Found ${files.length} files to check...\n`);

    for (const file of files) {
      await this.fixFileSyntaxErrors(file);
    }

    console.log(`\n✅ COMPLETED:`);
    console.log(`   Files fixed: ${this.fixedFiles}`);
    console.log(`   Total replacements: ${this.totalReplacements}`);
  }

  private async fixFileSyntaxErrors(filePath: string) {
    try {
      const content = readFileSync(filePath, 'utf-8');
      let newContent = content;
      let fileChanged = false;

      // Pattern 1: Fix setError calls with extra ternary
      // setError(err instanceof Error ? err.message : String(err) : 'error message');
      const setErrorPattern = /setError\((\w+) instanceof Error \? \1\.message : String\(\1\) : '[^']+'\);/g;
      
      newContent = newContent.replace(setErrorPattern, (match, errorVar) => {
        this.totalReplacements++;
        fileChanged = true;
        return `setError(${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar}));`;
      });

      // Pattern 2: Fix console.error calls with extra ternary
      const consoleErrorPattern = /console\.error\([^,]+, (\w+) instanceof Error \? \1\.message : String\(\1\) : '[^']+'\);/g;
      
      newContent = newContent.replace(consoleErrorPattern, (match, errorVar) => {
        this.totalReplacements++;
        fileChanged = true;
        return match.replace(/ : '[^']+'/, '');
      });

      // Pattern 3: Fix condition checks with malformed ternary
      // if (err instanceof Error && !err instanceof Error ? err.message : String(err).includes('404'))
      const conditionPattern = /if \((\w+) instanceof Error && !(\1) instanceof Error \? \2\.message : String\(\2\)\.includes\(/g;
      
      newContent = newContent.replace(conditionPattern, (match, errorVar1, errorVar2) => {
        this.totalReplacements++;
        fileChanged = true;
        return `if (${errorVar1} instanceof Error && !(${errorVar1} instanceof Error ? ${errorVar1}.message : String(${errorVar1})).includes(`;
      });

      // Pattern 4: Fix any remaining double ternary patterns
      const doubleTernaryPattern = /(\w+) instanceof Error \? \1\.(\w+) : String\(\1\) : '[^']+'/g;
      
      newContent = newContent.replace(doubleTernaryPattern, (match, errorVar, property) => {
        this.totalReplacements++;
        fileChanged = true;
        return `${errorVar} instanceof Error ? ${errorVar}.${property} : String(${errorVar})`;
      });

      // Pattern 5: Fix malformed includes() calls
      const includesPattern = /(\w+) instanceof Error && !(\1) instanceof Error \? \2\.message : String\(\2\)\.includes\(/g;
      
      newContent = newContent.replace(includesPattern, (match, errorVar1, errorVar2) => {
        this.totalReplacements++;
        fileChanged = true;
        return `${errorVar1} instanceof Error && !(${errorVar1} instanceof Error ? ${errorVar1}.message : String(${errorVar1})).includes(`;
      });

      if (fileChanged) {
        writeFileSync(filePath, newContent, 'utf-8');
        this.fixedFiles++;
        console.log(`✅ Fixed: ${filePath}`);
      }

    } catch (error) {
      console.log(`❌ Error processing ${filePath}:`, error instanceof Error ? error.message : String(error));
    }
  }
}

// Run the fixer
if (require.main === module) {
  const fixer = new SyntaxErrorFixer();
  fixer.fixSyntaxErrors().catch(console.error);
}

export { SyntaxErrorFixer };
