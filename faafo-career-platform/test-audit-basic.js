#!/usr/bin/env node

/**
 * Basic Audit System Test
 * Tests core functionality without complex imports
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Basic Audit System Test');
console.log('=' .repeat(50));

// Test 1: Check if core files exist
console.log('\n📁 Checking Core Files...');

const coreFiles = [
  'src/app/audit/page.tsx',
  'src/components/audit/AuditDashboard.tsx',
  'src/components/audit/AuditMetrics.tsx',
  'src/components/audit/RecentIssues.tsx',
  'src/components/audit/AuditRunsList.tsx',
  'src/app/api/audit/runs/route.ts',
  'src/app/api/audit/issues/route.ts',
  'src/lib/audit/core-audit-engine.ts',
  'src/lib/audit/audit-service.ts'
];

let filesExist = 0;
for (const file of coreFiles) {
  const exists = fs.existsSync(path.join(process.cwd(), file));
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (exists) filesExist++;
}

console.log(`\n📊 Files Status: ${filesExist}/${coreFiles.length} files exist`);

// Test 2: Check Prisma schema
console.log('\n🗄️  Checking Database Schema...');

try {
  const schemaPath = path.join(process.cwd(), 'prisma/schema.prisma');
  const schemaContent = fs.readFileSync(schemaPath, 'utf8');
  
  const hasAuditRun = schemaContent.includes('model AuditRun');
  const hasAuditIssue = schemaContent.includes('model AuditIssue');
  const hasIssueComment = schemaContent.includes('model IssueComment');
  
  console.log(`${hasAuditRun ? '✅' : '❌'} AuditRun model exists`);
  console.log(`${hasAuditIssue ? '✅' : '❌'} AuditIssue model exists`);
  console.log(`${hasIssueComment ? '✅' : '❌'} IssueComment model exists`);
  
} catch (error) {
  console.log('❌ Error reading Prisma schema:', error instanceof Error ? error.message : String(error));
}

// Test 3: Check API route structure
console.log('\n🌐 Checking API Routes...');

try {
  const runsApiPath = path.join(process.cwd(), 'src/app/api/audit/runs/route.ts');
  const runsApiContent = fs.readFileSync(runsApiPath, 'utf8');
  
  const hasGET = runsApiContent.includes('export const GET');
  const hasPOST = runsApiContent.includes('export const POST');
  const hasAuth = runsApiContent.includes('getServerSession');
  const hasErrorHandling = runsApiContent.includes('withUnifiedErrorHandling');
  
  console.log(`${hasGET ? '✅' : '❌'} GET endpoint exists`);
  console.log(`${hasPOST ? '✅' : '❌'} POST endpoint exists`);
  console.log(`${hasAuth ? '✅' : '❌'} Authentication check exists`);
  console.log(`${hasErrorHandling ? '✅' : '❌'} Error handling exists`);
  
} catch (error) {
  console.log('❌ Error reading API routes:', error instanceof Error ? error.message : String(error));
}

// Test 4: Check component structure
console.log('\n🎨 Checking Component Structure...');

try {
  const dashboardPath = path.join(process.cwd(), 'src/components/audit/AuditDashboard.tsx');
  const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');
  
  const hasUseState = dashboardContent.includes('useState');
  const hasUseEffect = dashboardContent.includes('useEffect');
  const hasFetch = dashboardContent.includes('fetch(');
  const hasErrorHandling = dashboardContent.includes('catch');
  
  console.log(`${hasUseState ? '✅' : '❌'} State management`);
  console.log(`${hasUseEffect ? '✅' : '❌'} Lifecycle hooks`);
  console.log(`${hasFetch ? '✅' : '❌'} API integration`);
  console.log(`${hasErrorHandling ? '✅' : '❌'} Error handling`);
  
} catch (error) {
  console.log('❌ Error reading components:', error instanceof Error ? error.message : String(error));
}

// Test 5: Check navigation integration
console.log('\n🧭 Checking Navigation Integration...');

try {
  const navPath = path.join(process.cwd(), 'src/components/layout/NavigationBar.tsx');
  const navContent = fs.readFileSync(navPath, 'utf8');
  
  const hasAuditLink = navContent.includes('/audit');
  const hasBugIcon = navContent.includes('Bug');
  const hasAdminCheck = navContent.includes('isAdmin');
  
  console.log(`${hasAuditLink ? '✅' : '❌'} Audit link in navigation`);
  console.log(`${hasBugIcon ? '✅' : '❌'} Bug icon imported`);
  console.log(`${hasAdminCheck ? '✅' : '❌'} Admin access check`);
  
} catch (error) {
  console.log('❌ Error reading navigation:', error instanceof Error ? error.message : String(error));
}

// Test 6: Check middleware protection
console.log('\n🔒 Checking Security...');

try {
  const middlewarePath = path.join(process.cwd(), 'middleware.ts');
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  
  const hasAuditRoute = middlewareContent.includes('/audit');
  const hasAdminRoutes = middlewareContent.includes('adminRoutes');
  
  console.log(`${hasAuditRoute ? '✅' : '❌'} Audit routes protected`);
  console.log(`${hasAdminRoutes ? '✅' : '❌'} Admin route protection`);
  
} catch (error) {
  console.log('❌ Error reading middleware:', error instanceof Error ? error.message : String(error));
}

console.log('\n🎉 Basic Test Complete!');
console.log('=' .repeat(50));
