#!/usr/bin/env node

/**
 * Audit System Database Testing
 * Tests database operations and schema functionality
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

console.log('🗄️  Audit System Database Testing');
console.log('=' .repeat(60));

let testsPassed = 0;
let testsFailed = 0;

function logTest(testName, passed, details = '') {
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ` - ${details}` : ''}`);
  
  if (passed) {
    testsPassed++;
  } else {
    testsFailed++;
  }
}

async function testDatabaseConnection() {
  console.log('\n🔌 Testing Database Connection...');
  
  try {
    await prisma.$connect();
    logTest('Database connection', true);
    return true;
  } catch (error) {
    logTest('Database connection', false, error instanceof Error ? error.message : String(error));
    return false;
  }
}

async function testAuditRunOperations() {
  console.log('\n📊 Testing AuditRun Operations...');
  
  try {
    // Test 1: Create AuditRun
    const testRun = await prisma.auditRun.create({
      data: {
        status: 'PENDING',
        totalIssues: 0,
        criticalCount: 0,
        highCount: 0,
        mediumCount: 0,
        lowCount: 0,
        triggeredBy: 'test-system'
      }
    });
    logTest('Create AuditRun', true, `Created run ${testRun.id.slice(-8)}`);
    
    // Test 2: Read AuditRun
    const foundRun = await prisma.auditRun.findUnique({
      where: { id: testRun.id }
    });
    logTest('Read AuditRun', foundRun !== null, `Found run ${foundRun?.id.slice(-8)}`);
    
    // Test 3: Update AuditRun
    const updatedRun = await prisma.auditRun.update({
      where: { id: testRun.id },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        totalIssues: 5,
        criticalCount: 1,
        highCount: 2,
        mediumCount: 2,
        lowCount: 0
      }
    });
    logTest('Update AuditRun', updatedRun.status === 'COMPLETED', 
      `Status: ${updatedRun.status}, Issues: ${updatedRun.totalIssues}`);
    
    // Test 4: List AuditRuns
    const runs = await prisma.auditRun.findMany({
      take: 5,
      orderBy: { startedAt: 'desc' }
    });
    logTest('List AuditRuns', runs.length > 0, `Found ${runs.length} runs`);
    
    return testRun.id;
    
  } catch (error) {
    logTest('AuditRun operations', false, error instanceof Error ? error.message : String(error));
    return null;
  }
}

async function testAuditIssueOperations(auditRunId) {
  console.log('\n🐛 Testing AuditIssue Operations...');
  
  if (!auditRunId) {
    logTest('AuditIssue operations', false, 'No audit run ID provided');
    return null;
  }
  
  try {
    // Test 1: Create AuditIssue
    const testIssue = await prisma.auditIssue.create({
      data: {
        auditRunId: auditRunId,
        severity: 'HIGH',
        category: 'SECURITY',
        title: 'Test Security Issue',
        description: 'This is a test security issue for validation',
        filePath: '/src/test/security-test.ts',
        lineNumber: 42,
        columnNumber: 15,
        codeSnippet: 'const unsafeQuery = `SELECT * FROM users WHERE id = ${userId}`;',
        recommendation: 'Use parameterized queries to prevent SQL injection',
        fixExample: 'const user = await prisma.user.findUnique({ where: { id: userId } });',
        status: 'OPEN'
      }
    });
    logTest('Create AuditIssue', true, `Created issue ${testIssue.id.slice(-8)}`);
    
    // Test 2: Read AuditIssue with relations
    const foundIssue = await prisma.auditIssue.findUnique({
      where: { id: testIssue.id },
      include: {
        auditRun: true,
        comments: true
      }
    });
    logTest('Read AuditIssue with relations', foundIssue !== null && foundIssue.auditRun !== null,
      `Found issue with run ${foundIssue?.auditRun.id.slice(-8)}`);
    
    // Test 3: Update AuditIssue status
    const updatedIssue = await prisma.auditIssue.update({
      where: { id: testIssue.id },
      data: {
        status: 'IN_PROGRESS'
      }
    });
    logTest('Update AuditIssue status', updatedIssue.status === 'IN_PROGRESS',
      `Status: ${updatedIssue.status}`);
    
    // Test 4: Filter issues by severity
    const criticalIssues = await prisma.auditIssue.findMany({
      where: {
        severity: 'CRITICAL'
      }
    });
    logTest('Filter issues by severity', true, `Found ${criticalIssues.length} critical issues`);
    
    // Test 5: Filter issues by category
    const securityIssues = await prisma.auditIssue.findMany({
      where: {
        category: 'SECURITY'
      }
    });
    logTest('Filter issues by category', securityIssues.length > 0, 
      `Found ${securityIssues.length} security issues`);
    
    return testIssue.id;
    
  } catch (error) {
    logTest('AuditIssue operations', false, error instanceof Error ? error.message : String(error));
    return null;
  }
}

async function testIssueCommentOperations(issueId) {
  console.log('\n💬 Testing IssueComment Operations...');
  
  if (!issueId) {
    logTest('IssueComment operations', false, 'No issue ID provided');
    return;
  }
  
  try {
    // Find a test user
    const testUser = await prisma.user.findFirst();
    
    if (!testUser) {
      logTest('IssueComment operations', false, 'No test user found');
      return;
    }
    
    // Test 1: Create IssueComment
    const testComment = await prisma.issueComment.create({
      data: {
        issueId: issueId,
        userId: testUser.id,
        comment: 'This is a test comment for the audit issue validation system.'
      }
    });
    logTest('Create IssueComment', true, `Created comment ${testComment.id.slice(-8)}`);
    
    // Test 2: Read comments for issue
    const comments = await prisma.issueComment.findMany({
      where: { issueId: issueId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });
    logTest('Read IssueComments with user', comments.length > 0 && comments[0].user !== null,
      `Found ${comments.length} comments`);
    
    // Test 3: Update comment
    const updatedComment = await prisma.issueComment.update({
      where: { id: testComment.id },
      data: {
        comment: 'Updated test comment for audit issue validation.'
      }
    });
    logTest('Update IssueComment', updatedComment.comment.includes('Updated'),
      'Comment updated successfully');
    
    return testComment.id;
    
  } catch (error) {
    logTest('IssueComment operations', false, error instanceof Error ? error.message : String(error));
    return null;
  }
}

async function testRelationships(auditRunId, issueId) {
  console.log('\n🔗 Testing Relationships...');
  
  try {
    // Test 1: AuditRun with issues
    const runWithIssues = await prisma.auditRun.findUnique({
      where: { id: auditRunId },
      include: {
        issues: true
      }
    });
    logTest('AuditRun includes issues', runWithIssues?.issues.length > 0,
      `Run has ${runWithIssues?.issues.length} issues`);
    
    // Test 2: AuditIssue with run and comments
    const issueWithRelations = await prisma.auditIssue.findUnique({
      where: { id: issueId },
      include: {
        auditRun: true,
        comments: {
          include: {
            user: true
          }
        }
      }
    });
    logTest('AuditIssue includes run and comments', 
      issueWithRelations?.auditRun !== null && issueWithRelations?.comments !== null,
      `Issue has run and ${issueWithRelations?.comments.length} comments`);
    
    // Test 3: Cascade delete test (comments should be deleted with issue)
    const commentCount = await prisma.issueComment.count({
      where: { issueId: issueId }
    });
    logTest('Comments exist before cascade test', commentCount > 0, 
      `${commentCount} comments found`);
    
  } catch (error) {
    logTest('Relationships test', false, error instanceof Error ? error.message : String(error));
  }
}

async function testAggregations() {
  console.log('\n📈 Testing Aggregations...');
  
  try {
    // Test 1: Count issues by severity
    const severityCounts = await prisma.auditIssue.groupBy({
      by: ['severity'],
      _count: {
        severity: true
      }
    });
    logTest('Group by severity', severityCounts.length > 0,
      `Found ${severityCounts.length} severity groups`);
    
    // Test 2: Count issues by category
    const categoryCounts = await prisma.auditIssue.groupBy({
      by: ['category'],
      _count: {
        category: true
      }
    });
    logTest('Group by category', categoryCounts.length > 0,
      `Found ${categoryCounts.length} category groups`);
    
    // Test 3: Count issues by status
    const statusCounts = await prisma.auditIssue.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    });
    logTest('Group by status', statusCounts.length > 0,
      `Found ${statusCounts.length} status groups`);
    
    // Test 4: Total issue count
    const totalIssues = await prisma.auditIssue.count();
    logTest('Total issue count', totalIssues >= 0, `${totalIssues} total issues`);
    
  } catch (error) {
    logTest('Aggregations test', false, error instanceof Error ? error.message : String(error));
  }
}

async function cleanupTestData(auditRunId, issueId, commentId) {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Delete comment first (due to foreign key constraints)
    if (commentId) {
      await prisma.issueComment.delete({
        where: { id: commentId }
      });
      logTest('Delete test comment', true);
    }
    
    // Delete issue
    if (issueId) {
      await prisma.auditIssue.delete({
        where: { id: issueId }
      });
      logTest('Delete test issue', true);
    }
    
    // Delete audit run
    if (auditRunId) {
      await prisma.auditRun.delete({
        where: { id: auditRunId }
      });
      logTest('Delete test audit run', true);
    }
    
  } catch (error) {
    logTest('Cleanup test data', false, error instanceof Error ? error.message : String(error));
  }
}

async function runDatabaseTests() {
  let auditRunId = null;
  let issueId = null;
  let commentId = null;
  
  try {
    // Test database connection
    const connected = await testDatabaseConnection();
    if (!connected) {
      console.log('\n❌ Database connection failed. Skipping tests.');
      return;
    }
    
    // Test AuditRun operations
    auditRunId = await testAuditRunOperations();
    
    // Test AuditIssue operations
    if (auditRunId) {
      issueId = await testAuditIssueOperations(auditRunId);
    }
    
    // Test IssueComment operations
    if (issueId) {
      commentId = await testIssueCommentOperations(issueId);
    }
    
    // Test relationships
    if (auditRunId && issueId) {
      await testRelationships(auditRunId, issueId);
    }
    
    // Test aggregations
    await testAggregations();
    
  } finally {
    // Always cleanup test data
    await cleanupTestData(auditRunId, issueId, commentId);
    
    // Disconnect from database
    await prisma.$disconnect();
  }
  
  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Database Test Results:');
  console.log(`✅ Passed: ${testsPassed}`);
  console.log(`❌ Failed: ${testsFailed}`);
  console.log(`📈 Success Rate: ${Math.round((testsPassed / (testsPassed + testsFailed)) * 100)}%`);
  
  if (testsFailed === 0) {
    console.log('\n🎉 All database tests passed!');
  } else {
    console.log(`\n⚠️  ${testsFailed} tests failed. Review the issues above.`);
  }
  
  console.log('\n🗄️  Database Testing Complete!');
  console.log('=' .repeat(60));
}

// Run the tests
runDatabaseTests().catch(console.error);
