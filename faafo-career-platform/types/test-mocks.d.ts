/// <reference types="jest" />

import { PrismaClient } from '@prisma/client';

declare global {
  namespace jest {
    interface Mock<T = any, Y extends any[] = any, C = any> {
      mockResolvedValue(value: T): this;
      mockRejectedValue(value: any): this;
      mockImplementation(fn?: (...args: Y) => T): this;
      mockReturnValue(value: T): this;
    }
  }

  // Global fetch mock
  var fetch: jest.MockedFunction<typeof fetch>;
  
  // Global mockUseSession
  var mockUseSession: jest.MockedFunction<any>;
}

// Prisma Mock Types
export interface MockPrismaClient {
  user: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    findFirst: jest.MockedFunction<any>;
    upsert: jest.MockedFunction<any>;
    count: jest.MockedFunction<any>;
  };
  resume: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    findFirst: jest.MockedFunction<any>;
    upsert: jest.MockedFunction<any>;
    count: jest.MockedFunction<any>;
  };
  freedomFund: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    findFirst: jest.MockedFunction<any>;
    upsert: jest.MockedFunction<any>;
    count: jest.MockedFunction<any>;
  };
  interviewSession: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    findFirst: jest.MockedFunction<any>;
    upsert: jest.MockedFunction<any>;
    count: jest.MockedFunction<any>;
  };
  skill: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    findFirst: jest.MockedFunction<any>;
    upsert: jest.MockedFunction<any>;
    count: jest.MockedFunction<any>;
  };
  learningResource: {
    findUnique: jest.MockedFunction<any>;
    findMany: jest.MockedFunction<any>;
    create: jest.MockedFunction<any>;
    update: jest.MockedFunction<any>;
    delete: jest.MockedFunction<any>;
    findFirst: jest.MockedFunction<any>;
    upsert: jest.MockedFunction<any>;
    count: jest.MockedFunction<any>;
  };
  $transaction: jest.MockedFunction<any>;
  $connect: jest.MockedFunction<any>;
  $disconnect: jest.MockedFunction<any>;
  $executeRaw: jest.MockedFunction<any>;
  $queryRaw: jest.MockedFunction<any>;
}

// Form Validation Mock Types
export interface MockFormValidationState {
  errors: Record<string, string>;
  warnings: string[];
  isValid: boolean;
  isValidating: boolean;
  touchedFields: Set<string>;
}

export interface MockFormValidationActions {
  validateField: jest.MockedFunction<any>;
  clearFieldError: jest.MockedFunction<any>;
  setFieldTouched: jest.MockedFunction<any>;
  validateForm: jest.MockedFunction<any>;
  clearAllErrors: jest.MockedFunction<any>;
}

export interface MockUseValidatedFormReturn {
  data: Record<string, any>;
  updateField: jest.MockedFunction<(fieldName: string, value: any) => void>;
  handleSubmit: jest.MockedFunction<(e?: React.FormEvent) => Promise<void>>;
  resetForm: jest.MockedFunction<() => void>;
  isSubmitting: boolean;
  submitError: string | undefined;
  validation: MockFormValidationState;
  validationActions: MockFormValidationActions;
}

// NextAuth Mock Types
export interface MockSession {
  user: {
    id: string;
    email: string;
    name: string;
    image?: string;
  };
  expires: string;
}

export interface MockUseSessionReturn {
  data: MockSession | null;
  status: 'loading' | 'authenticated' | 'unauthenticated';
  update: jest.MockedFunction<any>;
}

// API Response Mock Types
export interface MockApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string>;
}

// Test Helper Types
export interface MockUserData {
  id: string;
  email: string;
  name: string;
  password: string;
  image?: string | null;
  emailVerified?: Date | null;
  failedLoginAttempts?: number;
  lockedUntil?: Date | null;
  passwordResetToken?: string | null;
  passwordResetExpires?: Date | null;
  createdAt?: Date;
  updatedAt?: Date;
  completedAt?: Date | null;
}

export interface MockResumeData {
  id: string;
  userId: string;
  title: string;
  summary: string | null;
  template: string;
  personalInfo: any;
  experience: any;
  education: any;
  skills: any;
  projects: any;
  certifications: any;
  sections: any;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  exportCount: number;
}

export interface MockLearningResourceData {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  difficulty: string;
  estimatedHours: number;
  isActive: boolean;
  author?: string | null;
  duration?: string | null;
  cost?: string;
  createdAt: Date;
  updatedAt: Date;
}

export {};
