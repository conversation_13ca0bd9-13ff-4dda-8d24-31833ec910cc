/**
 * CI Setup Utilities
 * Quality gates and coverage validation for CI/CD pipeline
 */

const fs = require('fs');
const path = require('path');
const coverageConfig = require('../jest.coverage.config.js');

// Global CI setup
global.CI_ENVIRONMENT = true;
global.QUALITY_GATES_ENABLED = true;

/**
 * Coverage validation hook
 */
if (typeof afterAll !== 'undefined') {
  afterAll(async () => {
    if (process.env.CI && process.env.COVERAGE_VALIDATION === 'true') {
      await validateCoverageResults();
    }
  });
}

/**
 * Validate coverage results against quality gates
 */
async function validateCoverageResults() {
  try {
    const coveragePath = path.join(process.cwd(), 'coverage/ci/coverage-summary.json');
    
    if (!fs.existsSync(coveragePath)) {
      console.warn('⚠️  Coverage summary not found, skipping validation');
      return;
    }
    
    const coverageData = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
    const validation = coverageConfig.validateCoverage(coverageData);
    
    if (!validation.passed) {
      console.error('\n❌ COVERAGE QUALITY GATES FAILED:');
      validation.errors.forEach(error => {
        console.error(`   • ${error}`);
      });
      
      if (validation.warnings.length > 0) {
        console.warn('\n⚠️  Coverage Warnings:');
        validation.warnings.forEach(warning => {
          console.warn(`   • ${warning}`);
        });
      }
      
      // Fail the build
      process.exit(1);
    } else {
      console.log('\n✅ Coverage quality gates passed!');
      console.log(`   Profile: ${coverageConfig.getCurrentProfile()}`);
      console.log(`   Global coverage: ${JSON.stringify(coverageData.total, null, 2)}`);
    }
    
  } catch (error) {
    console.error('❌ Coverage validation failed:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

/**
 * Memory monitoring for CI
 */
let memoryCheckInterval;

if (typeof beforeAll !== 'undefined') {
  beforeAll(() => {
    if (process.env.CI) {
      // Monitor memory usage in CI
      memoryCheckInterval = setInterval(() => {
        const memUsage = process.memoryUsage();
        const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);

        // Log memory usage every 30 seconds
        if (heapUsedMB > 512) {
          console.warn(`⚠️  High memory usage: ${heapUsedMB}MB`);
        }
      }, 30000);
    }
  });
}

if (typeof afterAll !== 'undefined') {
  afterAll(() => {
    if (memoryCheckInterval) {
      clearInterval(memoryCheckInterval);
    }
  });
}

/**
 * Test timeout monitoring
 */
if (typeof jasmine !== 'undefined') {
  const originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;

  if (typeof beforeEach !== 'undefined') {
    beforeEach(() => {
      // Set stricter timeouts in CI
      if (process.env.CI) {
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 15000; // 15 seconds max per test
      }
    });
  }

  if (typeof afterEach !== 'undefined') {
    afterEach(() => {
      jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });
  }
}

/**
 * Quality gates configuration
 */
global.QUALITY_GATES = {
  coverage: {
    enforceThresholds: true,
    failOnDecrease: true,
    minimumIncrease: 2,
  },
  
  performance: {
    maxTestDuration: 15000,
    maxMemoryUsage: 512 * 1024 * 1024, // 512MB
  },
  
  reliability: {
    maxRetries: 0, // No retries in CI
    failFast: true,
  }
};

/**
 * Enhanced error reporting for CI
 */
const originalConsoleError = console.error;
console.error = (...args) => {
  if (process.env.CI) {
    // Add CI-specific error formatting
    originalConsoleError('\n🚨 CI ERROR:', ...args);
  } else {
    originalConsoleError(...args);
  }
};

/**
 * Test result validation
 */
global.validateTestResult = (result, expectations = {}) => {
  const {
    minCoverage = 90,
    maxDuration = 10000,
    requiredAssertions = 1
  } = expectations;
  
  // Validate coverage if available
  if (result.coverage && result.coverage.global) {
    Object.entries(result.coverage.global).forEach(([metric, value]) => {
      if (value < minCoverage) {
        throw new Error(`Coverage ${metric} ${value}% below minimum ${minCoverage}%`);
      }
    });
  }
  
  // Validate test duration
  if (result.duration && result.duration > maxDuration) {
    console.warn(`⚠️  Test duration ${result.duration}ms exceeds recommended ${maxDuration}ms`);
  }
  
  return true;
};

console.log('✅ CI setup initialized with quality gates enabled');
console.log(`   Coverage profile: ${coverageConfig.getCurrentProfile()}`);
console.log(`   Quality gates: ${JSON.stringify(global.QUALITY_GATES, null, 2)}`);
